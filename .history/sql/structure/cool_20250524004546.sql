/*
 Navicat Premium Dump SQL

 Source Server         : local-dev
 Source Server Type    : MySQL
 Source Server Version : 50744 (5.7.44)
 Source Host           : localhost:3306
 Source Schema         : cool

 Target Server Type    : MySQL
 Target Server Version : 50744 (5.7.44)
 File Encoding         : 65001

 Date: 24/05/2025 00:45:47
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for app_complain
-- ----------------------------
DROP TABLE IF EXISTS `app_complain`;
CREATE TABLE `app_complain` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `userId` int(11) NOT NULL COMMENT '用户ID',
  `type` int(11) NOT NULL COMMENT '类型',
  `contact` varchar(255) NOT NULL COMMENT '联系方式',
  `content` varchar(255) NOT NULL COMMENT '内容',
  `images` json DEFAULT NULL COMMENT '图片',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '状态 0-未处理 1-已处理',
  `handlerId` int(11) DEFAULT NULL COMMENT '处理人ID',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `IDX_2f9905ed434f8065d8be80bce7` (`createTime`),
  KEY `IDX_adbe91924664fe1c02f817e1ac` (`updateTime`),
  KEY `IDX_f45c94239321035e0daf72d5c7` (`tenantId`),
  KEY `IDX_f4320d6042879f3344e0ca29af` (`userId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for app_feedback
-- ----------------------------
DROP TABLE IF EXISTS `app_feedback`;
CREATE TABLE `app_feedback` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `userId` int(11) NOT NULL COMMENT '用户ID',
  `contact` varchar(255) NOT NULL COMMENT '联系方式',
  `type` int(11) NOT NULL COMMENT '类型',
  `content` varchar(255) NOT NULL COMMENT '内容',
  `images` json DEFAULT NULL COMMENT '图片',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '状态 0-未处理 1-已处理',
  `handlerId` int(11) DEFAULT NULL COMMENT '处理人ID',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `IDX_bdb81e80ae9e0f35e7a771e66c` (`createTime`),
  KEY `IDX_68857f0a96b554263b65ddff92` (`updateTime`),
  KEY `IDX_ae9ee867d638dd8e57662d1483` (`tenantId`),
  KEY `IDX_c490bc4960d7c137e54f304220` (`userId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for app_goods
-- ----------------------------
DROP TABLE IF EXISTS `app_goods`;
CREATE TABLE `app_goods` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `title` varchar(255) NOT NULL COMMENT '标题',
  `price` decimal(12,2) NOT NULL COMMENT '价格',
  `originalPrice` decimal(12,2) NOT NULL COMMENT '原价',
  `description` text COMMENT '描述',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态 0-禁用 1-启用',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `type` int(11) NOT NULL DEFAULT '0' COMMENT '类型 0-天 1-月 2-年 3-永久',
  `duration` int(11) NOT NULL DEFAULT '1' COMMENT '时长',
  `tag` varchar(255) DEFAULT NULL COMMENT '标签',
  `tagColor` varchar(255) NOT NULL DEFAULT '#26A7FD' COMMENT '标签颜色',
  PRIMARY KEY (`id`),
  KEY `IDX_88997e45cf1af49f037df45dac` (`createTime`),
  KEY `IDX_9836e0d0414396e0dbd82fcaf2` (`updateTime`),
  KEY `IDX_50ddf3e18aa7267aa1c8d02f5b` (`tenantId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for app_version
-- ----------------------------
DROP TABLE IF EXISTS `app_version`;
CREATE TABLE `app_version` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `name` varchar(255) NOT NULL COMMENT '名称',
  `version` varchar(255) NOT NULL COMMENT '版本号',
  `type` int(11) NOT NULL DEFAULT '0' COMMENT '类型',
  `url` varchar(255) NOT NULL COMMENT '下载地址',
  `forceUpdate` int(11) NOT NULL DEFAULT '0' COMMENT '强制更新 0-否 1-是',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态 0-禁用 1-启用',
  `hotUpdate` int(11) NOT NULL DEFAULT '0' COMMENT '热更新 0-否 1-是',
  `description` text NOT NULL COMMENT '描述',
  PRIMARY KEY (`id`),
  KEY `IDX_1e8f47a04c7029b8f0fea2a53c` (`createTime`),
  KEY `IDX_78780202ab1a7dc0f99477c555` (`updateTime`),
  KEY `IDX_8f060729ae897e0684d91811f2` (`tenantId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for base_sys_conf
-- ----------------------------
DROP TABLE IF EXISTS `base_sys_conf`;
CREATE TABLE `base_sys_conf` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `cKey` varchar(255) NOT NULL COMMENT '配置键',
  `cValue` varchar(255) NOT NULL COMMENT '配置值',
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_9be195d27767b4485417869c3a` (`cKey`),
  KEY `IDX_905208f206a3ff9fd513421971` (`createTime`),
  KEY `IDX_4c6f27f6ecefe51a5a196a047a` (`updateTime`),
  KEY `IDX_03fc424a2f8093a538730a7ff2` (`tenantId`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for base_sys_department
-- ----------------------------
DROP TABLE IF EXISTS `base_sys_department`;
CREATE TABLE `base_sys_department` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `name` varchar(255) NOT NULL COMMENT '部门名称',
  `parentId` int(11) DEFAULT NULL COMMENT '上级部门ID',
  `orderNum` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  PRIMARY KEY (`id`),
  KEY `IDX_be4c53cd671384fa588ca9470a` (`createTime`),
  KEY `IDX_ca1473a793961ec55bc0c8d268` (`updateTime`),
  KEY `IDX_f19e8ffd9c62ddb17e76c8b9d7` (`tenantId`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for base_sys_log
-- ----------------------------
DROP TABLE IF EXISTS `base_sys_log`;
CREATE TABLE `base_sys_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `userId` int(11) DEFAULT NULL COMMENT '用户ID',
  `action` varchar(255) NOT NULL COMMENT '行为',
  `ip` varchar(255) DEFAULT NULL COMMENT 'ip',
  `params` json DEFAULT NULL COMMENT '参数',
  PRIMARY KEY (`id`),
  KEY `IDX_c9382b76219a1011f7b8e7bcd1` (`createTime`),
  KEY `IDX_bfd44e885b470da43bcc39aaa7` (`updateTime`),
  KEY `IDX_384bde153859845bf0dcdc00f6` (`tenantId`),
  KEY `IDX_51a2caeb5713efdfcb343a8772` (`userId`),
  KEY `IDX_938f886fb40e163db174b7f6c3` (`action`),
  KEY `IDX_24e18767659f8c7142580893f2` (`ip`)
) ENGINE=InnoDB AUTO_INCREMENT=2170 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for base_sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `base_sys_menu`;
CREATE TABLE `base_sys_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `parentId` int(11) DEFAULT NULL COMMENT '父菜单ID',
  `name` varchar(255) NOT NULL COMMENT '菜单名称',
  `router` varchar(255) DEFAULT NULL COMMENT '菜单地址',
  `perms` text COMMENT '权限标识',
  `type` int(11) NOT NULL DEFAULT '0' COMMENT '类型 0-目录 1-菜单 2-按钮',
  `icon` varchar(255) DEFAULT NULL COMMENT '图标',
  `orderNum` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `viewPath` varchar(255) DEFAULT NULL COMMENT '视图地址',
  `keepAlive` tinyint(4) NOT NULL DEFAULT '1' COMMENT '路由缓存',
  `isShow` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否显示',
  PRIMARY KEY (`id`),
  KEY `IDX_05e3d6a56604771a6da47ebf8e` (`createTime`),
  KEY `IDX_d5203f18daaf7c3fe0ab34497f` (`updateTime`),
  KEY `IDX_2087f9610c1fc5a184bedaacef` (`tenantId`)
) ENGINE=InnoDB AUTO_INCREMENT=119 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for base_sys_param
-- ----------------------------
DROP TABLE IF EXISTS `base_sys_param`;
CREATE TABLE `base_sys_param` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `keyName` varchar(255) NOT NULL COMMENT '键',
  `name` varchar(255) NOT NULL COMMENT '名称',
  `data` text NOT NULL COMMENT '数据',
  `dataType` int(11) NOT NULL DEFAULT '0' COMMENT '数据类型 0-字符串 1-富文本 2-文件 ',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_cf19b5e52d8c71caa9c4534454` (`keyName`),
  KEY `IDX_7bcb57371b481d8e2d66ddeaea` (`createTime`),
  KEY `IDX_479122e3bf464112f7a7253dac` (`updateTime`),
  KEY `IDX_8a0ab598ca7d63475356ca1157` (`tenantId`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for base_sys_role
-- ----------------------------
DROP TABLE IF EXISTS `base_sys_role`;
CREATE TABLE `base_sys_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `userId` varchar(255) NOT NULL COMMENT '用户ID',
  `name` varchar(255) NOT NULL COMMENT '名称',
  `label` varchar(50) DEFAULT NULL COMMENT '角色标签',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `relevance` tinyint(4) NOT NULL DEFAULT '0' COMMENT '数据权限是否关联上下级',
  `menuIdList` json NOT NULL COMMENT '菜单权限',
  `departmentIdList` json NOT NULL COMMENT '部门权限',
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_469d49a5998170e9550cf113da` (`name`),
  UNIQUE KEY `IDX_f3f24fbbccf00192b076e549a7` (`label`),
  KEY `IDX_6f01184441dec49207b41bfd92` (`createTime`),
  KEY `IDX_d64ca209f3fc52128d9b20e97b` (`updateTime`),
  KEY `IDX_953dc26a4e8bd5d9c989295796` (`tenantId`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for base_sys_role_department
-- ----------------------------
DROP TABLE IF EXISTS `base_sys_role_department`;
CREATE TABLE `base_sys_role_department` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `roleId` int(11) NOT NULL COMMENT '角色ID',
  `departmentId` int(11) NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`id`),
  KEY `IDX_e881a66f7cce83ba431cf20194` (`createTime`),
  KEY `IDX_cbf48031efee5d0de262965e53` (`updateTime`),
  KEY `IDX_055658b2de49d547635e06f160` (`tenantId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for base_sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `base_sys_role_menu`;
CREATE TABLE `base_sys_role_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `roleId` int(11) NOT NULL COMMENT '角色ID',
  `menuId` int(11) NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`id`),
  KEY `IDX_3641f81d4201c524a57ce2aa54` (`createTime`),
  KEY `IDX_f860298298b26e7a697be36e5b` (`updateTime`),
  KEY `IDX_fd2d8bbe13949cfa56b1ed0a5d` (`tenantId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for base_sys_user
-- ----------------------------
DROP TABLE IF EXISTS `base_sys_user`;
CREATE TABLE `base_sys_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `departmentId` int(11) DEFAULT NULL COMMENT '部门ID',
  `name` varchar(255) DEFAULT NULL COMMENT '姓名',
  `username` varchar(100) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `passwordV` int(11) NOT NULL DEFAULT '1' COMMENT '密码版本, 作用是改完密码，让原来的token失效',
  `nickName` varchar(255) DEFAULT NULL COMMENT '昵称',
  `headImg` varchar(255) DEFAULT NULL COMMENT '头像',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机',
  `email` varchar(255) DEFAULT NULL COMMENT '邮箱',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态 0-禁用 1-启用',
  `socketId` varchar(255) DEFAULT NULL COMMENT 'socketId',
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_469ad55973f5b98930f6ad627b` (`username`),
  KEY `IDX_ca8611d15a63d52aa4e292e46a` (`createTime`),
  KEY `IDX_a0f2f19cee18445998ece93ddd` (`updateTime`),
  KEY `IDX_94cb6e88070603ac6729d514fd` (`tenantId`),
  KEY `IDX_0cf944da378d70a94f5fefd803` (`departmentId`),
  KEY `IDX_9ec6d7ac6337eafb070e4881a8` (`phone`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for base_sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `base_sys_user_role`;
CREATE TABLE `base_sys_user_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `userId` int(11) NOT NULL COMMENT '用户ID',
  `roleId` int(11) NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`id`),
  KEY `IDX_fa9555e03e42fce748c9046b1c` (`createTime`),
  KEY `IDX_3e36c0d2b1a4c659c6b4fc64b3` (`updateTime`),
  KEY `IDX_2f1dc0b6aad5604a2ddf37fba6` (`tenantId`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for demo_goods
-- ----------------------------
DROP TABLE IF EXISTS `demo_goods`;
CREATE TABLE `demo_goods` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `title` varchar(50) NOT NULL COMMENT '标题',
  `price` decimal(5,2) NOT NULL COMMENT '价格',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `mainImage` varchar(255) DEFAULT NULL COMMENT '主图',
  `type` int(11) NOT NULL COMMENT '分类',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
  `exampleImages` json DEFAULT NULL COMMENT '示例图',
  `stock` int(11) NOT NULL DEFAULT '0' COMMENT '库存',
  PRIMARY KEY (`id`),
  KEY `IDX_5075bf301ed9c39b5ca534231c` (`createTime`),
  KEY `IDX_82703e0477d1219261277df718` (`updateTime`),
  KEY `IDX_4773d4d34db0d601516da30bf3` (`tenantId`),
  KEY `IDX_85a70ee36c7c1b0a04bfa1ed27` (`title`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for dict_info
-- ----------------------------
DROP TABLE IF EXISTS `dict_info`;
CREATE TABLE `dict_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `typeId` int(11) NOT NULL COMMENT '类型ID',
  `name` varchar(255) NOT NULL COMMENT '名称',
  `value` varchar(255) DEFAULT NULL COMMENT '值',
  `orderNum` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `parentId` int(11) DEFAULT NULL COMMENT '父ID',
  PRIMARY KEY (`id`),
  KEY `IDX_5c311a4af30de1181a5d7a7cc2` (`createTime`),
  KEY `IDX_10362a62adbf120821fff209d8` (`updateTime`),
  KEY `IDX_c26dc4b1ccb26e642191995edd` (`tenantId`)
) ENGINE=InnoDB AUTO_INCREMENT=51 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for dict_type
-- ----------------------------
DROP TABLE IF EXISTS `dict_type`;
CREATE TABLE `dict_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `name` varchar(255) NOT NULL COMMENT '名称',
  `key` varchar(255) NOT NULL COMMENT '标识',
  PRIMARY KEY (`id`),
  KEY `IDX_69734e5c2d29cc2139d5078f2c` (`createTime`),
  KEY `IDX_6cccb2e33846cd354e8dc0e0ef` (`updateTime`),
  KEY `IDX_7d4f3d2336e1afdda38278a07e` (`tenantId`)
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for opoc_marketing_activity
-- ----------------------------
DROP TABLE IF EXISTS `opoc_marketing_activity`;
CREATE TABLE `opoc_marketing_activity` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `name` varchar(100) NOT NULL COMMENT '活动名称',
  `type` varchar(50) NOT NULL COMMENT '活动类型',
  `description` varchar(500) DEFAULT NULL COMMENT '活动描述',
  `startTime` datetime NOT NULL COMMENT '开始时间',
  `endTime` datetime NOT NULL COMMENT '结束时间',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '活动状态',
  `conditions` json DEFAULT NULL COMMENT '参与条件',
  `rewards` json DEFAULT NULL COMMENT '奖励规则',
  `rules` text COMMENT '活动规则',
  `participationLimit` int(11) NOT NULL DEFAULT '1' COMMENT '参与次数限制',
  `participationCount` int(11) NOT NULL DEFAULT '0' COMMENT '已参与次数',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `IDX_25d5d3618cac743e12a62fed00` (`createTime`),
  KEY `IDX_0bfbd2a1c7d8844606b177f474` (`updateTime`),
  KEY `IDX_284ce6d4f75c75314230f9c60a` (`tenantId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for opoc_product_code
-- ----------------------------
DROP TABLE IF EXISTS `opoc_product_code`;
CREATE TABLE `opoc_product_code` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `createTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '标识码状态（0:未使用, 1:已绑定, 2:已激活, 3:已失效）',
  `code` varchar(255) NOT NULL COMMENT '标识码',
  `productId` int(11) DEFAULT NULL COMMENT '产品ID',
  `activateTime` datetime DEFAULT NULL COMMENT '激活时间',
  `verifyCount` int(11) NOT NULL DEFAULT '0' COMMENT '验证次数',
  `lastVerifyTime` datetime DEFAULT NULL COMMENT '最后验证时间',
  PRIMARY KEY (`id`),
  KEY `IDX_a44e066cb16c19a50a29e220fb` (`tenantId`),
  KEY `IDX_1d8907ff834444117ae2c73ee2` (`createTime`),
  KEY `IDX_f26c919e75b7d7acc54185d9ee` (`updateTime`)
) ENGINE=InnoDB AUTO_INCREMENT=54 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for opoc_product_info
-- ----------------------------
DROP TABLE IF EXISTS `opoc_product_info`;
CREATE TABLE `opoc_product_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `productCode` varchar(50) NOT NULL COMMENT '产品编码',
  `productName` varchar(100) NOT NULL COMMENT '产品名称',
  `skuCode` varchar(50) NOT NULL COMMENT '产品SKU编码',
  `skuName` varchar(50) DEFAULT NULL COMMENT '产品SKU名称',
  `batchNo` varchar(50) DEFAULT NULL COMMENT '生产批次',
  `produceDate` datetime DEFAULT NULL COMMENT '生产日期',
  `expireDate` datetime DEFAULT NULL COMMENT '有效期至',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '产品状态',
  `description` varchar(500) DEFAULT NULL COMMENT '产品描述',
  `image` varchar(255) DEFAULT NULL COMMENT '产品图片',
  PRIMARY KEY (`id`),
  KEY `IDX_6ea5a7d87dfecf76ec96b7e8eb` (`createTime`),
  KEY `IDX_b50478a6c7f10a0f2a16c10acc` (`updateTime`),
  KEY `IDX_c740dbcd28c508fe95ebf98a8a` (`tenantId`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for opoc_trace
-- ----------------------------
DROP TABLE IF EXISTS `opoc_trace`;
CREATE TABLE `opoc_trace` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `actionType` varchar(50) NOT NULL COMMENT '操作类型',
  `operatorId` int(11) NOT NULL COMMENT '操作人ID',
  `operatorName` varchar(50) NOT NULL COMMENT '操作人名称',
  `module` varchar(50) NOT NULL COMMENT '业务模块',
  `businessId` int(11) NOT NULL COMMENT '业务ID',
  `businessType` varchar(50) NOT NULL COMMENT '业务类型',
  `ip` varchar(255) DEFAULT NULL COMMENT '操作IP',
  `url` varchar(255) DEFAULT NULL COMMENT '请求URL',
  `params` json DEFAULT NULL COMMENT '请求参数',
  `beforeData` json DEFAULT NULL COMMENT '变更前数据',
  `afterData` json DEFAULT NULL COMMENT '变更后数据',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `IDX_5287ebe2ca4d96d17794b8c605` (`createTime`),
  KEY `IDX_77aa27cc04893df2bdaecdbc5c` (`updateTime`),
  KEY `IDX_3fb91e7a9dc0bb56c7fac28a92` (`tenantId`),
  KEY `IDX_9c25a8aad2eda988da860e20ff` (`operatorId`),
  KEY `IDX_fcd52e6a12b363b9563d578893` (`businessId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for opoc_traceability
-- ----------------------------
DROP TABLE IF EXISTS `opoc_traceability`;
CREATE TABLE `opoc_traceability` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `nodeTime` datetime NOT NULL COMMENT '节点时间',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '状态',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `code` varchar(100) NOT NULL COMMENT '产品码',
  `productId` int(11) DEFAULT NULL COMMENT '产品ID',
  `productName` varchar(100) DEFAULT NULL COMMENT '产品名称',
  `nodeType` varchar(50) NOT NULL COMMENT '节点类型',
  `nodeName` varchar(100) NOT NULL COMMENT '节点名称',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作人',
  `location` varchar(100) DEFAULT NULL COMMENT '位置',
  `remark` text COMMENT '备注',
  `imageUrls` text COMMENT '图片URL',
  `longitude` decimal(10,6) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,6) DEFAULT NULL COMMENT '纬度',
  PRIMARY KEY (`id`),
  KEY `IDX_adba63edabff40d69b5eafee72` (`tenantId`),
  KEY `IDX_86b4279174e1674598ac6ae5e3` (`createTime`),
  KEY `IDX_7247aaf84a1f33c952f4757fc7` (`updateTime`),
  KEY `IDX_5f275d6f2eb51fc86a71362c39` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for opoc_traceability_record
-- ----------------------------
DROP TABLE IF EXISTS `opoc_traceability_record`;
CREATE TABLE `opoc_traceability_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `codeId` varchar(32) NOT NULL COMMENT '产品码ID',
  `productId` varchar(32) NOT NULL COMMENT '产品ID',
  `type` tinyint(4) NOT NULL COMMENT '记录类型',
  `recordTime` datetime NOT NULL COMMENT '记录时间',
  `operatorId` varchar(32) NOT NULL COMMENT '操作人ID',
  `operatorName` varchar(50) NOT NULL COMMENT '操作人名称',
  `location` varchar(255) DEFAULT NULL COMMENT '操作地点',
  `organization` varchar(100) DEFAULT NULL COMMENT '相关组织',
  `content` text NOT NULL COMMENT '记录内容',
  `extraInfo` json DEFAULT NULL COMMENT '附加信息',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `IDX_b160bc2eae85c11b907dc7cad0` (`createTime`),
  KEY `IDX_ac62d509571e0a83b5d6b7415e` (`updateTime`),
  KEY `IDX_e3674ac7a0e2cddfa0f5080fc4` (`tenantId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for opoc_verification_log
-- ----------------------------
DROP TABLE IF EXISTS `opoc_verification_log`;
CREATE TABLE `opoc_verification_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `verifyTime` datetime NOT NULL COMMENT '验证时间',
  `verifyLocation` varchar(255) DEFAULT NULL COMMENT '验证地理位置',
  `createTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `codeId` int(11) NOT NULL COMMENT '标识码ID',
  `verifyResult` int(11) NOT NULL DEFAULT '0' COMMENT '验证结果（0:真, 1:假）',
  `longitude` decimal(10,6) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,6) DEFAULT NULL COMMENT '纬度',
  PRIMARY KEY (`id`),
  KEY `IDX_702edccbe3ce35bf81275ddd04` (`tenantId`),
  KEY `IDX_9aa555cccb3bb6b5480928835d` (`createTime`),
  KEY `IDX_755b49e7a9e9af44f011111c41` (`updateTime`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for plugin_info
-- ----------------------------
DROP TABLE IF EXISTS `plugin_info`;
CREATE TABLE `plugin_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `name` varchar(255) NOT NULL COMMENT '名称',
  `description` varchar(255) NOT NULL COMMENT '简介',
  `keyName` varchar(255) NOT NULL COMMENT 'Key名',
  `hook` varchar(255) NOT NULL COMMENT 'Hook',
  `readme` text NOT NULL COMMENT '描述',
  `version` varchar(255) NOT NULL COMMENT '版本',
  `logo` text COMMENT 'Logo(base64)',
  `author` varchar(255) NOT NULL COMMENT '作者',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '状态 0-禁用 1-启用',
  `content` json NOT NULL COMMENT '内容',
  `tsContent` json NOT NULL COMMENT 'ts内容',
  `pluginJson` json DEFAULT NULL COMMENT '插件的plugin.json',
  `config` json DEFAULT NULL COMMENT '配置',
  PRIMARY KEY (`id`),
  KEY `IDX_071da0804576df95363c24357c` (`createTime`),
  KEY `IDX_d94d7c2437aca9f1b183979b07` (`updateTime`),
  KEY `IDX_89a39daf328b50686755795546` (`tenantId`),
  KEY `IDX_95719662507de0fbf70ad1b5ee` (`keyName`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for query-result-cache
-- ----------------------------
DROP TABLE IF EXISTS `query-result-cache`;
CREATE TABLE `query-result-cache` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `identifier` varchar(255) DEFAULT NULL,
  `time` bigint(20) NOT NULL,
  `duration` int(11) NOT NULL,
  `query` text NOT NULL,
  `result` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for recycle_data
-- ----------------------------
DROP TABLE IF EXISTS `recycle_data`;
CREATE TABLE `recycle_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `entityInfo` json NOT NULL COMMENT '表',
  `userId` int(11) DEFAULT NULL COMMENT '操作人',
  `data` json NOT NULL COMMENT '被删除的数据',
  `url` varchar(255) DEFAULT NULL COMMENT '请求的接口',
  `params` json DEFAULT NULL COMMENT '请求参数',
  `count` int(11) NOT NULL DEFAULT '1' COMMENT '删除数据条数',
  PRIMARY KEY (`id`),
  KEY `IDX_59fc783673f4a322e9c83e0599` (`createTime`),
  KEY `IDX_c6a499c4a4fcd37f2930d27816` (`updateTime`),
  KEY `IDX_6659453338145e11d9b5103f38` (`tenantId`),
  KEY `IDX_f3ed09ba7090f3eb378cb83b5b` (`userId`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for space_info
-- ----------------------------
DROP TABLE IF EXISTS `space_info`;
CREATE TABLE `space_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `url` varchar(255) NOT NULL COMMENT '地址',
  `type` varchar(255) NOT NULL COMMENT '类型',
  `classifyId` int(11) DEFAULT NULL COMMENT '分类ID',
  `fileId` varchar(255) NOT NULL COMMENT '文件id',
  `name` varchar(255) NOT NULL COMMENT '文件名',
  `size` int(11) NOT NULL COMMENT '文件大小',
  `version` int(11) NOT NULL DEFAULT '1' COMMENT '文档版本',
  `key` varchar(255) NOT NULL COMMENT '文件位置',
  PRIMARY KEY (`id`),
  KEY `IDX_eb1da2f304c760846b5add09b3` (`createTime`),
  KEY `IDX_d7a2539961e9aacba8b353f3c9` (`updateTime`),
  KEY `IDX_6001c5ed2088b893c0d69bb244` (`tenantId`),
  KEY `IDX_0975633032bfe6574468b3a4ae` (`fileId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for space_type
-- ----------------------------
DROP TABLE IF EXISTS `space_type`;
CREATE TABLE `space_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `name` varchar(255) NOT NULL COMMENT '类别名称',
  `parentId` int(11) DEFAULT NULL COMMENT '父分类ID',
  PRIMARY KEY (`id`),
  KEY `IDX_6669449501d275f367ca295472` (`createTime`),
  KEY `IDX_0749b509b68488caecd4cc2bbc` (`updateTime`),
  KEY `IDX_5e7f846b8cdabbceba95ed3314` (`tenantId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for task_info
-- ----------------------------
DROP TABLE IF EXISTS `task_info`;
CREATE TABLE `task_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `jobId` varchar(255) DEFAULT NULL COMMENT '任务ID',
  `repeatConf` varchar(1000) DEFAULT NULL COMMENT '任务配置',
  `name` varchar(255) NOT NULL COMMENT '名称',
  `cron` varchar(255) DEFAULT NULL COMMENT 'cron',
  `limit` int(11) DEFAULT NULL COMMENT '最大执行次数 不传为无限次',
  `every` int(11) DEFAULT NULL COMMENT '每间隔多少毫秒执行一次 如果cron设置了 这项设置就无效',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态 0-停止 1-运行',
  `startDate` datetime DEFAULT NULL COMMENT '开始时间',
  `endDate` datetime DEFAULT NULL COMMENT '结束时间',
  `data` varchar(255) DEFAULT NULL COMMENT '数据',
  `service` varchar(255) DEFAULT NULL COMMENT '执行的service实例ID',
  `type` int(11) NOT NULL DEFAULT '0' COMMENT '状态 0-系统 1-用户',
  `nextRunTime` datetime DEFAULT NULL COMMENT '下一次执行时间',
  `taskType` int(11) NOT NULL DEFAULT '0' COMMENT '状态 0-cron 1-时间间隔',
  `lastExecuteTime` datetime DEFAULT NULL,
  `lockExpireTime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_6ced02f467e59bd6306b549bb0` (`createTime`),
  KEY `IDX_2adc6f9c241391126f27dac145` (`updateTime`),
  KEY `IDX_11b991dc4a7a5585c636008d3a` (`tenantId`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for task_log
-- ----------------------------
DROP TABLE IF EXISTS `task_log`;
CREATE TABLE `task_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `taskId` int(11) DEFAULT NULL COMMENT '任务ID',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '状态 0-失败 1-成功',
  `detail` text COMMENT '详情描述',
  PRIMARY KEY (`id`),
  KEY `IDX_b9af0e100be034924b270aab31` (`createTime`),
  KEY `IDX_8857d8d43d38bebd7159af1fa6` (`updateTime`),
  KEY `IDX_fa4cb94036d961600c0f22ed91` (`tenantId`),
  KEY `IDX_1142dfec452e924b346f060fda` (`taskId`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for user_address
-- ----------------------------
DROP TABLE IF EXISTS `user_address`;
CREATE TABLE `user_address` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `userId` int(11) NOT NULL COMMENT '用户ID',
  `contact` varchar(255) NOT NULL COMMENT '联系人',
  `phone` varchar(11) NOT NULL COMMENT '手机号',
  `province` varchar(255) NOT NULL COMMENT '省',
  `city` varchar(255) NOT NULL COMMENT '市',
  `district` varchar(255) NOT NULL COMMENT '区',
  `address` varchar(255) NOT NULL COMMENT '地址',
  `isDefault` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否默认',
  PRIMARY KEY (`id`),
  KEY `IDX_144621f4f7bf21e72ed6972d85` (`createTime`),
  KEY `IDX_de647797f6286697bfe9527955` (`updateTime`),
  KEY `IDX_d93103979d4be73c3192163996` (`tenantId`),
  KEY `IDX_1abd8badc4a127b0f357d9ecbc` (`userId`),
  KEY `IDX_905be3a22a4dfda68da8e4200a` (`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for user_info
-- ----------------------------
DROP TABLE IF EXISTS `user_info`;
CREATE TABLE `user_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `unionid` varchar(255) DEFAULT NULL COMMENT '登录唯一ID',
  `avatarUrl` varchar(255) DEFAULT NULL COMMENT '头像',
  `nickName` varchar(255) DEFAULT NULL COMMENT '昵称',
  `phone` varchar(255) DEFAULT NULL COMMENT '手机号',
  `gender` int(11) NOT NULL DEFAULT '0' COMMENT '性别',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
  `loginType` int(11) NOT NULL DEFAULT '0' COMMENT '登录方式',
  `password` varchar(255) DEFAULT NULL COMMENT '密码',
  `description` text COMMENT '介绍',
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_6edeceee578056a2c1e493563a` (`unionid`),
  UNIQUE KEY `IDX_9234e7bac72991a93b172618e2` (`phone`),
  KEY `IDX_e6386e92c288d85dbc43ac53f7` (`createTime`),
  KEY `IDX_5271afbb87138d688b6220b589` (`updateTime`),
  KEY `IDX_7c8ea8d68808b77734df54ce32` (`tenantId`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for user_wx
-- ----------------------------
DROP TABLE IF EXISTS `user_wx`;
CREATE TABLE `user_wx` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` varchar(255) NOT NULL COMMENT '创建时间',
  `updateTime` varchar(255) NOT NULL COMMENT '更新时间',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `unionid` varchar(255) DEFAULT NULL COMMENT '微信unionid',
  `openid` varchar(255) NOT NULL COMMENT '微信openid',
  `avatarUrl` varchar(255) DEFAULT NULL COMMENT '头像',
  `nickName` varchar(255) DEFAULT NULL COMMENT '昵称',
  `gender` int(11) NOT NULL DEFAULT '0' COMMENT '性别 0-未知 1-男 2-女',
  `language` varchar(255) DEFAULT NULL COMMENT '语言',
  `city` varchar(255) DEFAULT NULL COMMENT '城市',
  `province` varchar(255) DEFAULT NULL COMMENT '省份',
  `country` varchar(255) DEFAULT NULL COMMENT '国家',
  `type` int(11) NOT NULL DEFAULT '0' COMMENT '类型 0-小程序 1-公众号 2-H5 3-APP',
  PRIMARY KEY (`id`),
  KEY `IDX_e23b473abf5a6b00e44f3fd842` (`createTime`),
  KEY `IDX_049adb91204e94c1ede5e6dd23` (`updateTime`),
  KEY `IDX_f39f7e2dd63c906fcee61c50ad` (`tenantId`),
  KEY `IDX_d22b5fa040a01ec1b09e1e181e` (`unionid`),
  KEY `IDX_7946849febadd93cf81fc2b53f` (`openid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

SET FOREIGN_KEY_CHECKS = 1;
