import { ModuleConfig } from '/@/cool';

export default (): ModuleConfig => {
	return {
		// 是否启用
		enable: true,

		// 插件名称
		label: '一码通',

		// 插件描述
		description: '一码通产品防伪溯源系统',

		// 作者
		author: 'YIDE Team',
		version: '1.0.0',
		updateTime: '2024-04-09',

		// 注册视图路由
		views: [
			{
				path: '/opoc/verification',
				meta: {
					label: '验证记录'
				},
				component: () => import('./views/verification.vue')
			},
			{
				path: '/opoc/product',
				meta: {
					label: '产品管理'
				},
				component: () => import('./views/product.vue')
			},
			{
				path: '/opoc/code',
				meta: {
					label: '码管理'
				},
				component: () => import('./views/code.vue')
			},
			{
				path: '/opoc/code-format',
				meta: {
					label: '码格式设置'
				},
				component: () => import('./views/code-format.vue')
			},
			{
				path: '/opoc/traceability',
				meta: {
					label: '溯源管理'
				},
				component: () => import('./views/traceability.vue')
			},
			{
				path: '/opoc/traceability/report',
				meta: {
					label: '溯源报告',
					hidden: true
				},
				component: () => import('./views/traceability-report.vue')
			},
			{
				path: '/opoc/traceability/warning',
				meta: {
					label: '异常预警'
				},
				component: () => import('./views/traceability-warning.vue')
			}
		],

		// 注册页面路由
		pages: [],

		// 注册全局组件
		components: []
	};
};
