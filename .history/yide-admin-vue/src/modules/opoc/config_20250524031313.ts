import { ModuleConfig } from '/@/cool';

export default (): ModuleConfig => {
	return {
		// 是否启用
		enable: true,

		// 插件名称
		label: '一码通',

		// 插件描述
		description: '一码通产品防伪溯源系统',

		// 作者
		author: 'YIDE Team',
		version: '1.0.0',
		updateTime: '2024-05-25',

		// 注册视图路由
		views: [
			{
				path: '/opoc/verification',
				meta: {
					label: '验证记录'
				},
				component: () => import('./views/verification.vue')
			},
			{
				path: '/opoc/verification-statistics',
				meta: {
					label: '验证统计'
				},
				component: () => import('./views/verification-statistics.vue')
			},
			{
				path: '/opoc/product',
				meta: {
					label: '产品管理'
				},
				component: () => import('./views/product.vue')
			},
			{
				path: '/opoc/code',
				meta: {
					label: '码管理'
				},
				component: () => import('./views/code.vue')
			},
			{
				path: '/opoc/code-format',
				meta: {
					label: '码格式设置'
				},
				component: () => import('./views/code-format.vue')
			},
			{
				path: '/opoc/code-rule',
				meta: {
					label: '编码规则配置'
				},
				component: () => import('./views/code-rule.vue')
			},
			{
				path: '/opoc/traceability',
				meta: {
					label: '溯源管理'
				},
				component: () => import('./views/traceability.vue')
			},
			{
				path: '/opoc/traceability/report',
				meta: {
					label: '溯源报告',
					hidden: true
				},
				component: () => import('./views/traceability-report.vue')
			},
			{
				path: '/opoc/traceability/warning',
				meta: {
					label: '异常预警'
				},
				component: () => import('./views/traceability-warning.vue')
			},
			{
				path: '/opoc/trace-node-type',
				meta: {
					label: '溯源节点类型配置'
				},
				component: () => import('./views/trace-node-type.vue')
			},
			{
				path: '/opoc/marketing-activity',
				meta: {
					label: '营销活动'
				},
				component: () => import('./views/marketing-activity.vue')
			},
			{
				path: '/opoc/activity-record',
				meta: {
					label: '活动记录'
				},
				component: () => import('./views/activity-record.vue')
			},
			{
				path: '/opoc/activity-statistics',
				meta: {
					label: '活动统计'
				},
				component: () => import('./views/activity-statistics.vue')
			}
		],

		// 注册页面路由
		pages: [],

		// 注册全局组件
		components: []
	};
};
