/**
 * 一码通模块 API
 */
import { defineStore } from 'pinia'
import {
    OpocCode,
    OpocProduct,
    OpocTraceability,
    OpocTraceabilityReport,
    OpocVerificationLog
} from './types'
import { useCool } from '/@/cool'
// 定义 store
export const useOpocStore = defineStore('opoc', function () {
	const { service } = useCool()

	// 获取验证记录列表
	async function getVerificationList(
		params: any = {}
	): Promise<{ list: OpocVerificationLog[]; pagination: any, [key: string]: any }> {
		return await service.opoc.verification.page(params)
	}

	// 获取验证记录详情
	async function getVerificationInfo(id: string): Promise<OpocVerificationLog> {
		return await service.opoc.verification.info({ id })
	}

	// 获取产品列表
	async function getProductList(
		params: any = {}
	): Promise<{ list: OpocProduct[]; pagination: any }> {
		return await service.opoc.product.page(params)
	}

	// 获取产品详情
	async function getProductInfo(id: string): Promise<OpocProduct> {
		return await service.opoc.product.info({ id })
	}

	// 获取防伪码列表
	async function getCodeList(params: any = {}): Promise<{ list: OpocCode[]; pagination: any }> {
		return await service.opoc.code.page(params)
	}

	// 获取防伪码详情
	async function getCodeInfo(id: string): Promise<OpocCode> {
		return await service.opoc.code.info({ id })
	}

	// 获取溯源记录列表
	async function getTraceabilityList(
		params: any = {}
	): Promise<{ list: OpocTraceability[]; pagination: any }> {
		return await service.opoc.traceability.page(params)
	}

	// 获取溯源记录详情
	async function getTraceabilityInfo(id: string): Promise<OpocTraceability> {
		return await service.opoc.traceability.info({ id })
	}

	// 添加溯源记录
	async function addTraceabilityNode(data: Partial<OpocTraceability>): Promise<any> {
		return await service.opoc.traceability.add(data)
	}

	// 更新溯源记录
	async function updateTraceabilityNode(data: Partial<OpocTraceability>): Promise<any> {
		return await service.opoc.traceability.update(data)
	}

	// 删除溯源记录
	async function deleteTraceabilityNode(ids: string[]): Promise<any> {
		return await service.opoc.traceability.delete({ ids })
	}

	// 获取溯源报告
	async function getTraceabilityReport(code: string): Promise<OpocTraceabilityReport> {
		return await service.opoc.traceability.report({ code })
	}

	// 获取溯源异常预警列表
	async function getTraceabilityWarningList(
		params: any = {}
	): Promise<{ list: OpocTraceability[]; pagination: any }> {
		return await service.opoc.traceability.warning.list(params)
	}

	// 获取溯源异常预警统计数据
	async function getTraceabilityWarningStats(): Promise<any> {
		return await service.opoc.traceability.warning.stats()
	}

	// 处理溯源异常预警
	async function handleTraceabilityWarning(params: any): Promise<any> {
		return await service.opoc.traceability.warning.handle(params)
	}

	return {
		getVerificationList,
		getVerificationInfo,
		getProductList,
		getProductInfo,
		getCodeList,
		getCodeInfo,
		getTraceabilityList,
		getTraceabilityInfo,
		addTraceabilityNode,
		updateTraceabilityNode,
		deleteTraceabilityNode,
		getTraceabilityReport,
		getTraceabilityWarningList,
		getTraceabilityWarningStats,
		handleTraceabilityWarning
	}
})

// 导出模块 API
export function useOpoc() {
	return {
		// 导出 store
		...useOpocStore(),

		// 模块名称
		moduleName: '一码通',

		// 模块描述
		description: '产品防伪溯源系统'
	}
}
