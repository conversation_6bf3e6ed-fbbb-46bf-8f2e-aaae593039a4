/**
 * 一码通模块类型定义
 */

// 产品类型
export interface OpocProduct {
	id: string;
	productCode: string;
	productName: string;
	skuCode: string;
	skuName: string;
	batchNo: string;
	produceDate: string;
	expireDate: string;
	status: number;
	createTime: string;
	updateTime?: string;
}

// 验证记录类型
export interface OpocVerificationLog {
	id: string;
	code: string;
	status: number;
	productName?: string;
	ip: string;
	location?: string;
	errorMessage?: string;
	deviceInfo?: string;
	createTime: string;
	otherInfo?: string | object;
}

// 防伪码类型
export interface OpocCode {
	id: string;
	code: string;
	productId?: string;
	productName?: string;
	status: number;
	createTime: string;
	updateTime?: string;
	expireTime?: string;
	useTime?: string;
}

// 溯源记录类型
export interface OpocTraceability {
	id: string;
	code: string;
	productId?: string;
	productName?: string;
	nodeType: string; // 节点类型：原材料、生产、检验、销售、物流、售后等
	nodeName: string; // 节点名称
	nodeTime: string; // 节点时间
	operator?: string; // 操作人
	location?: string; // 地点
	status: number; // 状态：0-正常，1-异常
	remark?: string; // 备注
	imageUrls?: string; // 图片URL，多个用逗号分隔
	createTime: string; // 创建时间
	updateTime?: string; // 更新时间
	extData?: string | object; // 扩展数据，JSON格式
}

// 溯源报告类型
export interface OpocTraceabilityReport {
	code: string; // 产品码
	productInfo: OpocProduct; // 产品信息
	traceNodes: OpocTraceability[]; // 溯源节点
	verifyCount: number; // 验证次数
	lastVerifyTime?: string; // 最后验证时间
}
