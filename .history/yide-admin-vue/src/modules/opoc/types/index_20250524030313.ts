/**
 * 一码通模块类型定义
 */

// 产品类型
export interface OpocProduct {
	id: string;
	productCode: string;
	productName: string;
	skuCode: string;
	skuName: string;
	batchNo: string;
	produceDate: string;
	expireDate: string;
	status: number;
	createTime: string;
	updateTime?: string;
}

// 验证记录类型
export interface OpocVerificationLog {
	id: string;
	code: string;
	status: number;
	productName?: string;
	ip: string;
	location?: string;
	errorMessage?: string;
	deviceInfo?: string;
	createTime: string;
	otherInfo?: string | object;
}

// 防伪码类型
export interface OpocCode {
	id: string;
	code: string;
	productId?: string;
	productName?: string;
	status: number;
	createTime: string;
	updateTime?: string;
	expireTime?: string;
	useTime?: string;
}

// 溯源记录类型
export interface OpocTraceability {
	id: string;
	code: string;
	productId?: string;
	productName?: string;
	nodeType: string; // 节点类型：原材料、生产、检验、销售、物流、售后等
	nodeName: string; // 节点名称
	nodeTime: string; // 节点时间
	operator?: string; // 操作人
	location?: string; // 地点
	status: number; // 状态：0-正常，1-异常
	remark?: string; // 备注
	imageUrls?: string; // 图片URL，多个用逗号分隔
	createTime: string; // 创建时间
	updateTime?: string; // 更新时间
	extData?: string | object; // 扩展数据，JSON格式
}

// 溯源报告类型
export interface OpocTraceabilityReport {
	code: string; // 产品码
	productInfo: OpocProduct; // 产品信息
	traceNodes: OpocTraceability[]; // 溯源节点
	verifyCount: number; // 验证次数
	lastVerifyTime?: string; // 最后验证时间
}

// 营销活动类型
export interface OpocMarketingActivity {
	id: string;
	activityName: string; // 活动名称
	activityType: number; // 活动类型（0:扫码抽奖, 1:集码兑奖, 2:分享活动, 3:其他）
	description?: string; // 活动描述
	startTime: string; // 活动开始时间
	endTime: string; // 活动结束时间
	rules?: string; // 活动规则
	status: number; // 活动状态（0:未开始, 1:进行中, 2:已结束, 3:已取消）
	participationLimit: number; // 参与限制（0:不限制, 1:每人一次, 2:每天一次, 3:自定义）
	customLimit?: number; // 自定义参与次数限制
	prizes?: any; // 奖品设置
	image?: string; // 活动图片
	activityUrl?: string; // 活动链接
	requireProductCode: number; // 是否需要验证产品码（0:不需要, 1:需要）
	creatorId?: string; // 创建人ID
	creatorName?: string; // 创建人姓名
	createTime: string; // 创建时间
	updateTime?: string; // 更新时间
}

// 活动参与记录类型
export interface OpocActivityRecord {
	id: string;
	activityId: string; // 活动ID
	userId?: string; // 用户ID
	userName?: string; // 用户名称
	userPhone?: string; // 用户手机号
	codeId?: string; // 产品码ID
	code?: string; // 产品码
	participationTime: string; // 参与时间
	result: number; // 参与结果（0:未中奖, 1:已中奖, 2:处理中, 3:已失效）
	prizeId?: string; // 奖品ID
	prizeName?: string; // 奖品名称
	prizeType?: number; // 奖品类型（0:实物, 1:优惠券, 2:积分, 3:其他）
	prizeQuantity?: number; // 奖品数量
	receiveStatus?: number; // 领取状态（0:未领取, 1:已领取, 2:已过期）
	receiveTime?: string; // 领取时间
	receiveAddress?: string; // 领取地址
	remark?: string; // 备注
	ip?: string; // IP地址
	deviceInfo?: string; // 设备信息
	location?: string; // 地理位置
	createTime: string; // 创建时间
}

// 活动统计数据类型
export interface OpocActivityStatistics {
	totalParticipants: number; // 总参与人次
	uniqueParticipants: number; // 独立参与人数
	totalPrizes: number; // 总发放奖品数
	prizesDistribution: Record<string, number>; // 奖品分布
	participationTrend: Array<{ date: string; count: number }>; // 参与趋势
}
