<template>
	<div class="opoc-code">
		<cl-crud ref="Crud">
			<cl-row>
				<cl-refresh-btn />
				<el-button type="primary" @click="openGenerateDialog">
					<el-icon>
						<plus />
					</el-icon>
					<span>生成产品码</span>
				</el-button>
				<el-button type="success" @click="openBindDialog()">
					<el-icon>
						<link />
					</el-icon>
					<span>绑定产品</span>
				</el-button>
				<el-button type="warning" @click="openExportDialog">
					<el-icon>
						<download />
					</el-icon>
					<span>批量导出</span>
				</el-button>
				<cl-flex1 />
				<cl-search-key placeholder="输入产品码搜索" />
			</cl-row>

			<cl-row>
				<cl-table ref="Table" />
			</cl-row>

			<cl-row>
				<cl-flex1 />
				<cl-pagination />
			</cl-row>
		</cl-crud>

		<!-- 生成产品码表单 -->
		<cl-form ref="GenerateForm"></cl-form>

		<!-- 绑定产品表单 -->
		<cl-form ref="BindForm">
		</cl-form>
	</div>
</template>

<script lang="ts" setup>
	import { useCrud, useForm, useTable } from '@cool-vue/crud'
import { Download, Plus } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, ref } from 'vue'
import { useCool } from '/@/cool'

	const { service, refs } = useCool()

	// crud实例
	const Crud = useCrud({
		service: service.opoc.code
	}, app => {
		app.refresh()
	})

	// 表格配置
	const Table = useTable({
		columns: [
			{ type: 'selection' },
			{
				label: '标识码',
				prop: 'code',
				minWidth: 200
			},
			{
				label: '产品码',
				prop: 'productId',
				minWidth: 150,
				showOverflowTooltip: true
			},
			{
				label: '产品',
				prop: 'productName',
				minWidth: 150,
				component: {
					name: 'cl-text',
					props: (scope: any) => {
						return {
							content: scope.row.productName || '未绑定',
							type: scope.row.productName ? 'primary' : 'info'
						}
					}
				}
			},
			{
				label: 'SKU编码',
				prop: 'skuCode',
				minWidth: 120,
				component: {
					name: 'cl-text',
					props: (scope: any) => {
						return {
							content: scope.row.skuCode || '—',
							type: 'info'
						}
					}
				}
			},
			{
				label: 'SKU名称',
				prop: 'skuName',
				minWidth: 120,
				component: {
					name: 'cl-text',
					props: (scope: any) => {
						return {
							content: scope.row.skuName || '—',
							type: 'info'
						}
					}
				}
			},
			{
				label: '状态',
				prop: 'status',
				width: 120,
				dict: [
					{ label: '未绑定', value: 0, type: 'info' },
					{ label: '已绑定', value: 1, type: 'warning' },
					{ label: '已激活', value: 2, type: 'success' }
				]
			},
			{
				label: '激活时间',
				prop: 'activateTime',
				minWidth: 180,
				component: {
					name: 'cl-text',
					props: (scope: any) => {
						return {
							content: scope.row.activateTime || '—',
							type: 'info'
						}
					}
				}
			},
			{ label: '创建时间', prop: 'createTime', minWidth: 180 },
			{
				label: '操作',
				type: 'op',
				width: 240,
				buttons({ scope }) {
					return [
						{
							label: '绑定产品',
							type: 'success',
							hidden: !(service.opoc.code._permission.bind && scope.row.status === 0),
							onClick({ scope }) {
								openBindDialog([scope.row.id])
							}
						},
						{
							label: '激活',
							type: 'success',
							hidden: !(service.opoc.code._permission.activate && scope.row.status === 1),
							onClick({ scope }) {
								activateCode(scope.row.id)
							}
						},
						{
							label: '详情',
							type: 'primary',
							onClick: (scope: any) => {
								ElMessage.info(`查看产品码详情：${scope.row.code}`)
							}
						}
					]
				}
			}
		]
	})

	// 产品列表
	const productList = ref<any[]>([])

	// 未绑定产品码列表
	const unboundCodeList = ref<any[]>([])

	// 选中的ID列表
	const selectedIds = computed(() => {
		return Table.value?.getSelectionIds() || []
	})

	// 查询参数
	const queryParams = computed(() => {
		return Crud.value?.getParams() || {}
	})

	// 生成产品码表单
	const GenerateForm = useForm()

	// 绑定产品表单
	const BindForm = useForm()

	// 打开生成产品码弹窗
	const openGenerateDialog = () => {
		GenerateForm.value?.open({
			title: '生成产品码',
			width: '550px',
			items: [
				{
					label: '数量',
					prop: 'quantity',
					value: 50,
					component: {
						name: 'el-input-number',
						props: {
							min: 1,
							max: 10000,
							controlsPosition: 'right'
						}
					},
					rules: {
						required: true,
						message: '请输入产品码数量'
					}
				},
				{
					label: '类型',
					prop: 'type',
					value: 'mixed',
					component: {
						name: 'el-select',
						props: {
							placeholder: '请选择产品码类型'
						},
						options: [
							{ label: '随机字符', value: 'random' },
							{ label: '数字编码', value: 'number' },
							{ label: '字母编码', value: 'letter' },
							{ label: '字母+数字', value: 'mixed' }
						]
					},
					rules: {
						required: true,
						message: '请选择产品码类型'
					}
				},
				{
					label: '长度',
					prop: 'length',
					value: 16,
					component: {
						name: 'el-input-number',
						props: {
							min: 6,
							max: 32,
							controlsPosition: 'right'
						}
					},
					rules: {
						required: true,
						message: '请输入产品码长度'
					}
				}
			],
			on: {
				submit: async (data, { done, close }) => {
					try {
						await service.opoc.code.generate(data)
						ElMessage.success(`成功生成${data.quantity}个产品码`)
						close()

						// 刷新列表
						Crud.value?.refresh()
					} catch (error: any) {
						ElMessage.error(error.message || '生成产品码失败')
						done()
					}
				}
			}
		})
	}

	// 激活产品码
	const activateCode = async (id: number) => {
		try {
			await ElMessageBox.confirm('确定要激活该产品码吗？', '提示', {
				type: 'warning',
				confirmButtonText: '确定',
				cancelButtonText: '取消'
			})

			await service.opoc.code.activate({ id })
			ElMessage.success('激活成功')

			// 刷新列表
			Crud.value?.refresh()
		} catch (error: any) {
			if (error !== 'cancel') {
				ElMessage.error(error.message || '激活失败')
			}
		}
	}

	// 远程搜索未绑定产品码
	const searchUnboundCodes = (query: string) => {
		service.opoc.code.page({ page: 1, size: 10, code: query, status: 0 }).then(res => {
			unboundCodeList.value = res.list || []
		})
	}

	// 打开绑定产品弹窗
	const openBindDialog = async (codeIds: string[] = []) => {

		BindForm.value?.open({
			title: '绑定产品',
			width: '650px',
			form: {
				codeIds
			},
			items: [
				{
					label: '产品',
					prop: 'productId',
					component: {
						name: 'el-select',
						props: {
							filterable: true,
							remote: true,
							remoteMethod: remoteMethod,
							placeholder: '请选择产品',
							clearable: true
						},
						options: computed(() => productList.value.map(item => ({
							label: `${item.productName} [${item.productCode || '-'}] (${item.skuCode || '-'}[${item.skuName || '-'}])`,
							value: item.id
						})))
					},
					rules: {
						required: true,
						message: '请选择产品'
					}
				},
				{
					label: '标识码',
					prop: 'codeIds',
					component: {
						name: 'el-select',
						props: {
							multiple: false,
							remote: true,
							placeholder: '请选择标识码',
							filterable: true,
							clearable: true,
							remoteMethod: searchUnboundCodes
						},
						options: computed(() => unboundCodeList.value.map(item => ({
							label: item.code,
							value: item.id
						})))
					},
					rules: {
						required: true,
						message: '请选择产品码'
					}
				}
			],
			on: {
				open: () => {
					console.log(refs)
				},
				submit: async (data, { done, close }) => {
					try {
						if (!data.codeIds.length) {
							ElMessage.warning('请选择产品码')
							return done()
						}

						await service.opoc.code.bind({
							productId: data.productId,
							codeIds: data.codeIds
						})

						ElMessage.success(`成功绑定${data.codeIds.length}个产品码`)
						close()

						// 刷新列表
						Crud.value?.refresh()
					} catch (error: any) {
						ElMessage.error(error.message || '绑定产品失败')
						done()
					}
				}
			}
		})
	}

	const remoteMethod = (query: string) => {
		service.opoc.product.page({ page: 1, size: 10, productCode: query, productName: query, skuCode: query, skuName: query, batchNo: query }).then(res => {
			productList.value = res.list || []
		})
	}

	// 打开导出对话框
	const openExportDialog = async () => {
		try {
			// 获取选中的ID列表
			const ids = selectedIds.value

			// 准备导出字段
			const fields = ['code', 'productName', 'status', 'createTime', 'activateTime', 'verifyCount', 'verifyLimit']

			// 准备表头
			const header = [
				'产品码', '产品名称', '状态', '创建时间', '激活时间', '验证次数', '验证次数限制'
			]

			// 获取数据
			let data = []

			if (ids.length > 0) {
				// 导出选中数据
				const result = await service.opoc.code.list({ ids })
				data = result || []
			} else {
				// 导出当前查询条件下的数据
				const result = await service.opoc.code.list(queryParams.value)
				data = result || []
			}

			// 格式化数据
			const exportData = data.map(item => {
				return [
					item.code || '',
					item.productName || '',
					formatStatus(item.status),
					item.createTime ? dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') : '',
					item.activateTime ? dayjs(item.activateTime).format('YYYY-MM-DD HH:mm:ss') : '',
					item.verifyCount || 0,
					item.verifyLimit || '无限制'
				]
			})

			// 导出Excel
			export_json_to_excel({
				header,
				data: exportData,
				filename: `产品码数据_${dayjs().format('YYYYMMDD_HHmmss')}`,
				autoWidth: true,
				bookType: 'xlsx'
			})

			ElMessage.success('导出成功')
		} catch (error) {
			ElMessage.error(`导出失败: ${error.message || '未知错误'}`)
		}
	}

	// 格式化状态
	const formatStatus = (status) => {
		switch (status) {
			case 0:
				return '未绑定'
			case 1:
				return '已绑定'
			case 2:
				return '已激活'
			case 3:
				return '已失效'
			default:
				return '未知'
		}
	}
</script>

<style lang="scss" scoped>
	.opoc-code {
		height: 100%;
	}
</style>
