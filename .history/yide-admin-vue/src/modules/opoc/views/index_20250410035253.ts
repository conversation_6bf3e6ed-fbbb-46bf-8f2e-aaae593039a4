import { RouteRecordRaw } from 'vue-router'

// 模块路由
export default {
  path: '/opoc',
  name: 'opoc',
  component: () => import('/$/layout/index.vue'),
  meta: {
    title: '一物一码',
    icon: 'icon-qrcode'
  },
  children: [
    {
      path: 'product',
      name: 'opoc-product',
      component: () => import('./product.vue'),
      meta: {
        title: '产品管理',
        keepAlive: true
      }
    },
    {
      path: 'code',
      name: 'opoc-code',
      component: () => import('./code.vue'),
      meta: {
        title: '防伪码管理',
        keepAlive: true
      }
    },
    {
      path: 'verification',
      name: 'opoc-verification',
      component: () => import('./verification.vue'),
      meta: {
        title: '验证记录',
        keepAlive: true
      }
    },
    {
      path: 'traceability',
      name: 'opoc-traceability',
      component: () => import('./traceability.vue'),
      meta: {
        title: '溯源管理',
        keepAlive: true
      }
    },
    {
      path: 'traceability-report',
      name: 'opoc-traceability-report',
      component: () => import('./traceability-report.vue'),
      meta: {
        title: '溯源报告',
        hidden: true
      }
    },
    {
      path: 'traceability-warning',
      name: 'opoc-traceability-warning',
      component: () => import('./traceability-warning.vue'),
      meta: {
        title: '异常预警',
        keepAlive: true
      }
    },
  ]
} as RouteRecordRaw; 