<template>
	<div class="opoc-product">
		<cl-crud ref="Crud">
			<cl-row>
				<cl-refresh-btn />
				<cl-add-btn />
				<batch-import />
				<cl-flex1 />
				<cl-search-key placeholder="输入产品名称/型号搜索" />
			</cl-row>

			<cl-row>
				<cl-table ref="Table" />
			</cl-row>

			<cl-row>
				<cl-flex1 />
				<cl-pagination />
			</cl-row>

			<cl-upsert ref="Upsert" />
		</cl-crud>
	</div>
</template>

<script lang="ts" setup>
	import { useCrud, useTable, useUpsert } from '@cool-vue/crud'
import { reactive } from 'vue'
import BatchImport from '../components/batch-import.vue'
import { service } from '/@/cool'

	// 定义组件名称
	defineOptions({
		name: 'opoc-product'
	})

	const options = reactive({
		status: [
			{ label: '正常', value: 1, type: 'success' },
			{ label: '已过期', value: 2, type: 'warning' },
			{ label: '已报废', value: 3, type: 'danger' }
		]
	})

	// crud实例
	const Crud = useCrud({
		service: service.opoc.product,

	}, (app) => {
		app.refresh()
	})

	// 表单配置
	const Upsert = useUpsert({
		dialog: {
			width: '600px'
		},
		items: [
			{
				prop: 'productCode',
				label: '产品编码',
				component: {
					name: 'el-input',
					props: {
						placeholder: '请输入产品编码'
					}
				},
				rules: {
					required: true,
					message: '请输入产品编码'
				}
			},
			{
				prop: 'productName',
				label: '产品名称',
				component: {
					name: 'el-input',
					props: {
						placeholder: '请输入产品名称'
					}
				},
				rules: {
					required: true,
					message: '请输入产品名称'
				}
			},
			{
				prop: 'skuCode',
				label: '产品SKU编码',
				component: {
					name: 'el-input',
					props: {
						placeholder: '请输入产品SKU编码'
					}
				},
				rules: {
					required: true,
					message: '请输入产品SKU编码'
				}
			},
			{
				prop: 'skuName',
				label: '产品SKU名称',
				component: {
					name: 'el-input',
					props: {
						placeholder: '请输入产品SKU名称'
					}
				},
				rules: {
					required: true,
					message: '请输入产品SKU名称'
				}
			},
			{
				prop: 'batchNo',
				label: '生产批次',
				component: {
					name: 'el-input',
					props: {
						placeholder: '请输入生产批次'
					}
				},
				rules: {
					required: true,
					message: '请输入生产批次'
				}
			},
			{
				prop: 'image',
				label: '产品图片',
				component: {
					name: 'cl-upload'
				}
			},
			{
				prop: 'produceDate',
				label: '生产日期',
				component: {
					name: 'el-date-picker',
					props: {
						type: 'datetime'
					}
				}
			},
			{
				prop: 'expireDate',
				label: '有效期至',
				component: {
					name: 'el-date-picker',
					props: {
						type: 'datetime'
					}
				}
			},
			{
				prop: 'status',
				label: '状态',
				value: 1,
				component: {
					name: 'el-radio-group',
					options: [
						{
							label: '正常',
							value: 1
						},
						{
							label: '已过期',
							value: 2
						},
						{
							label: '已报废',
							value: 3
						}
					]
				}
			}
		]
	})

	// 表格配置
	const Table = useTable({
		columns: [
			{
				prop: 'image',
				label: '产品图片',
				component: {
					name: 'cl-avatar'
				}
			},
			{
				label: '产品编码',
				prop: 'productCode',
				minWidth: 120
			},
			{
				label: '产品名称',
				prop: 'productName',
				minWidth: 120
			},
			{
				label: '产品SKU编码',
				prop: 'skuCode',
				minWidth: 120
			},
			{
				label: '产品SKU名称',
				prop: 'skuName',
				minWidth: 120
			},
			{
				label: '生产批次',
				prop: 'batchNo',
				minWidth: 120
			},
			{
				label: '生产日期',
				prop: 'produceDate',
				width: 120
			},
			{
				label: '有效期至',
				prop: 'expireDate',
				width: 120
			},
			{
				label: '状态',
				prop: 'status',
				width: 80,
				dict: options.status
			},
			{
				label: '创建时间',
				prop: 'createTime',
				width: 150
			},
			{
				type: 'op'
			}
		]
	})
</script>

<style lang="scss" scoped>
	.opoc-product {
		height: 100%;
	}
</style>
