<template>
	<div class="trace-report">
		<div class="report-header">
			<div class="title">溯源报告</div>
			<div class="product-info" v-if="report.productInfo">
				<el-descriptions :column="2" border>
					<el-descriptions-item label="产品名称">{{ report.productInfo.name || '-' }}</el-descriptions-item>
					<el-descriptions-item label="产品型号">{{ report.productInfo.model || '-' }}</el-descriptions-item>
					<el-descriptions-item label="产品码">{{ report.code || '-' }}</el-descriptions-item>
					<el-descriptions-item label="验证次数">{{ report.verifyCount || 0 }}次</el-descriptions-item>
					<el-descriptions-item label="产品描述" :span="2">{{ report.productInfo.description || '-' }}</el-descriptions-item>
				</el-descriptions>
			</div>
		</div>
		
		<div class="report-content" v-if="report.traceNodes && report.traceNodes.length">
			<div class="title">溯源记录</div>
			<el-timeline>
				<el-timeline-item
					v-for="(node, index) in report.traceNodes"
					:key="index"
					:type="node.status === 0 ? 'primary' : 'danger'"
					:icon="getNodeIcon(node.nodeType)"
					:timestamp="node.nodeTime">
					<el-card class="trace-node">
						<template #header>
							<div class="card-header">
								<span class="node-name">{{ node.nodeName }}</span>
								<el-tag :type="node.status === 0 ? 'success' : 'danger'">
									{{ node.status === 0 ? '正常' : '异常' }}
								</el-tag>
							</div>
						</template>
						<div class="node-content">
							<p><strong>节点类型:</strong> {{ getNodeTypeName(node.nodeType) }}</p>
							<p v-if="node.operator"><strong>操作人:</strong> {{ node.operator }}</p>
							<p v-if="node.location"><strong>位置:</strong> {{ node.location }}</p>
							<p v-if="node.remark"><strong>备注:</strong> {{ node.remark }}</p>
							<div v-if="node.imageUrls" class="node-images">
								<div class="image-title"><strong>图片资料:</strong></div>
								<div class="image-list">
									<el-image 
										v-for="(url, imgIndex) in node.imageUrls.split(',')" 
										:key="imgIndex"
										:src="url"
										fit="cover"
										class="node-image"
										:preview-src-list="node.imageUrls.split(',')">
									</el-image>
								</div>
							</div>
						</div>
					</el-card>
				</el-timeline-item>
			</el-timeline>
		</div>
		
		<div class="no-data" v-else>
			<el-empty description="暂无溯源记录"></el-empty>
		</div>
		
		<div class="actions">
			<el-button type="primary" @click="goBack">返回</el-button>
			<el-button type="success" @click="printReport">打印报告</el-button>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { onMounted, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useOpoc } from '../index';

const router = useRouter();
const route = useRoute();
const opoc = useOpoc();

// 溯源报告数据
const report = reactive({
	code: '',
	productInfo: null as any,
	traceNodes: [] as any[],
	verifyCount: 0,
	lastVerifyTime: ''
});

// 获取溯源报告
async function getReport() {
	try {
		const code = route.query.code as string;
		if (!code) {
			ElMessage.error('产品码不能为空');
			return;
		}
		
		const data = await opoc.getTraceabilityReport(code);
		report.code = data.code;
		report.productInfo = data.productInfo;
		report.traceNodes = data.traceNodes;
		report.verifyCount = data.verifyCount;
		report.lastVerifyTime = data.lastVerifyTime;
	} catch (error: any) {
		ElMessage.error(error.message || '获取溯源报告失败');
	}
}

// 获取节点图标
function getNodeIcon(nodeType: string) {
	const iconMap: Record<string, string> = {
		'material': 'Shopping',
		'production': 'SetUp',
		'inspection': 'Checked',
		'logistics': 'Ship',
		'sales': 'SoldOut',
		'after-sales': 'Service'
	};
	
	return iconMap[nodeType] || 'More';
}

// 获取节点类型名称
function getNodeTypeName(nodeType: string) {
	const typeMap: Record<string, string> = {
		'material': '原材料',
		'production': '生产',
		'inspection': '检验',
		'logistics': '物流',
		'sales': '销售',
		'after-sales': '售后'
	};
	
	return typeMap[nodeType] || nodeType;
}

// 返回上一页
function goBack() {
	router.back();
}

// 打印报告
function printReport() {
	window.print();
}

// 页面加载时获取报告
onMounted(() => {
	getReport();
});
</script>

<style lang="scss" scoped>
.trace-report {
	padding: 20px;
	
	.report-header {
		margin-bottom: 30px;
		
		.title {
			font-size: 22px;
			font-weight: bold;
			margin-bottom: 15px;
			color: #409EFF;
		}
	}
	
	.report-content {
		margin-bottom: 30px;
		
		.title {
			font-size: 18px;
			font-weight: bold;
			margin-bottom: 15px;
			color: #409EFF;
		}
		
		.trace-node {
			margin-bottom: 15px;
			
			.card-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				
				.node-name {
					font-weight: bold;
					font-size: 16px;
				}
			}
			
			.node-content {
				p {
					margin: 8px 0;
				}
				
				.node-images {
					margin-top: 15px;
					
					.image-title {
						margin-bottom: 10px;
					}
					
					.image-list {
						display: flex;
						flex-wrap: wrap;
						gap: 10px;
						
						.node-image {
							width: 100px;
							height: 100px;
							object-fit: cover;
							border-radius: 4px;
							border: 1px solid #eee;
						}
					}
				}
			}
		}
	}
	
	.no-data {
		margin: 50px 0;
		text-align: center;
	}
	
	.actions {
		margin-top: 30px;
		display: flex;
		justify-content: center;
		gap: 20px;
	}
}

@media print {
	.actions {
		display: none !important;
	}
}
</style>
