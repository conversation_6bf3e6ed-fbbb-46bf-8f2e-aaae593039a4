<template>
  <div class="traceability-visualization">
    <div class="visualization-header">
      <div class="search-box">
        <el-input
          v-model="searchCode"
          placeholder="请输入产品码"
          clearable
          @keyup.enter="loadData"
        >
          <template #append>
            <el-button @click="loadData">
              <el-icon><Search /></el-icon>
            </el-button>
          </template>
        </el-input>
      </div>
      
      <div class="actions">
        <el-button type="primary" @click="exportImage">导出图片</el-button>
        <el-button @click="resetView">重置视图</el-button>
      </div>
    </div>
    
    <div class="visualization-content">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="10" animated />
      </div>
      
      <div v-else-if="!graphData.nodes.length" class="empty-container">
        <el-empty description="暂无溯源数据" />
      </div>
      
      <div v-else class="graph-container">
        <div ref="graphRef" class="graph"></div>
      </div>
    </div>
    
    <div v-if="productInfo" class="product-info">
      <h3>产品信息</h3>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="产品码">{{ searchCode }}</el-descriptions-item>
        <el-descriptions-item label="产品名称">{{ productInfo.productName }}</el-descriptions-item>
        <el-descriptions-item label="产品编码">{{ productInfo.productCode }}</el-descriptions-item>
        <el-descriptions-item label="SKU编码">{{ productInfo.skuCode }}</el-descriptions-item>
        <el-descriptions-item label="批次号">{{ productInfo.batchNo }}</el-descriptions-item>
        <el-descriptions-item label="生产日期">{{ productInfo.produceDate }}</el-descriptions-item>
      </el-descriptions>
    </div>
    
    <el-drawer
      v-model="nodeDetailVisible"
      title="节点详情"
      size="30%"
      :with-header="true"
    >
      <div v-if="selectedNode" class="node-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="节点名称">{{ selectedNode.data.nodeName }}</el-descriptions-item>
          <el-descriptions-item label="节点类型">{{ getNodeTypeName(selectedNode.data.nodeType) }}</el-descriptions-item>
          <el-descriptions-item label="节点时间">{{ selectedNode.data.nodeTime }}</el-descriptions-item>
          <el-descriptions-item label="操作人">{{ selectedNode.data.operator || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="位置">{{ selectedNode.data.location || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedNode.data.status === 0 ? 'success' : 'danger'">
              {{ selectedNode.data.status === 0 ? '正常' : '异常' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="备注">{{ selectedNode.data.remark || '无' }}</el-descriptions-item>
        </el-descriptions>
        
        <div v-if="selectedNode.data.imageUrls" class="node-images">
          <h4>图片资料</h4>
          <div class="image-list">
            <el-image 
              v-for="(url, index) in selectedNode.data.imageUrls.split(',')" 
              :key="index"
              :src="url"
              fit="cover"
              class="node-image"
              :preview-src-list="selectedNode.data.imageUrls.split(',')"
            />
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { useCool } from '/@/cool';
import G6, { Graph } from '@antv/g6';
import { useOpoc } from '../index';

// 定义组件名称
defineOptions({
  name: 'traceability-visualization'
});

const { service } = useCool();
const opoc = useOpoc();

// 状态变量
const searchCode = ref('');
const loading = ref(false);
const graphRef = ref<HTMLElement | null>(null);
const graph = ref<Graph | null>(null);
const graphData = ref<{ nodes: any[]; edges: any[] }>({ nodes: [], edges: [] });
const productInfo = ref(null);
const nodeDetailVisible = ref(false);
const selectedNode = ref(null);

// 加载溯源数据
async function loadData() {
  if (!searchCode.value) {
    ElMessage.warning('请输入产品码');
    return;
  }
  
  loading.value = true;
  
  try {
    const res = await service.opoc.traceability.visualization.data({
      code: searchCode.value
    });
    
    if (res.code === 1000) {
      graphData.value = {
        nodes: res.data.nodes || [],
        edges: res.data.edges || []
      };
      productInfo.value = res.data.product;
      
      nextTick(() => {
        initGraph();
      });
    } else {
      ElMessage.error(res.message || '获取溯源数据失败');
    }
  } catch (error) {
    console.error('加载溯源数据失败', error);
    ElMessage.error('加载溯源数据失败');
  } finally {
    loading.value = false;
  }
}

// 初始化图表
function initGraph() {
  if (!graphRef.value) return;
  
  if (graph.value) {
    graph.value.destroy();
  }
  
  // 图表配置
  graph.value = new G6.Graph({
    container: graphRef.value,
    width: graphRef.value.clientWidth,
    height: 600,
    layout: {
      type: 'dagre',
      rankdir: 'LR',
      nodesep: 50,
      ranksep: 100
    },
    defaultNode: {
      type: 'rect',
      size: [120, 60],
      style: {
        radius: 8,
        stroke: '#1890FF',
        lineWidth: 2,
        fill: '#fff'
      },
      labelCfg: {
        style: {
          fill: '#000',
          fontSize: 14
        }
      }
    },
    defaultEdge: {
      type: 'polyline',
      style: {
        radius: 10,
        offset: 30,
        endArrow: true,
        lineWidth: 2,
        stroke: '#1890FF'
      }
    },
    modes: {
      default: ['drag-canvas', 'zoom-canvas', 'drag-node']
    },
    fitView: true,
    animate: true
  });
  
  // 注册节点点击事件
  graph.value.on('node:click', (evt) => {
    const node = evt.item;
    selectedNode.value = node.getModel();
    nodeDetailVisible.value = true;
  });
  
  // 渲染图表
  graph.value.data(graphData.value);
  graph.value.render();
}

// 导出图片
function exportImage() {
  if (!graph.value || graphData.value.nodes.length === 0) {
    ElMessage.warning('暂无溯源数据可导出');
    return;
  }
  
  graph.value.downloadFullImage('溯源流程图', 'image/png', {
    backgroundColor: '#fff',
    padding: [20, 20, 20, 20]
  });
}

// 重置视图
function resetView() {
  if (graph.value) {
    graph.value.fitView();
  }
}

// 获取节点类型名称
function getNodeTypeName(nodeType: string) {
  const typeMap: Record<string, string> = {
    'material': '原材料',
    'production': '生产',
    'inspection': '检验',
    'logistics': '物流',
    'sales': '销售',
    'after-sales': '售后'
  };
  
  return typeMap[nodeType] || nodeType;
}

// 窗口大小变化时调整图表大小
function handleResize() {
  if (graph.value && graphRef.value) {
    graph.value.changeSize(graphRef.value.clientWidth, 600);
    graph.value.fitView();
  }
}

// 组件挂载时
onMounted(() => {
  window.addEventListener('resize', handleResize);
});

// 组件卸载时
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (graph.value) {
    graph.value.destroy();
  }
});
</script>

<style lang="scss" scoped>
.traceability-visualization {
  padding: 20px;
  
  .visualization-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    
    .search-box {
      width: 300px;
    }
  }
  
  .visualization-content {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: 600px;
    
    .loading-container,
    .empty-container {
      height: 600px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .graph-container {
      width: 100%;
      height: 600px;
      
      .graph {
        width: 100%;
        height: 100%;
      }
    }
  }
  
  .product-info {
    margin-top: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
    
    h3 {
      margin-top: 0;
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: 500;
    }
  }
  
  .node-detail {
    .node-images {
      margin-top: 20px;
      
      h4 {
        margin-top: 0;
        margin-bottom: 10px;
        font-size: 14px;
        font-weight: 500;
      }
      
      .image-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        
        .node-image {
          width: 100px;
          height: 100px;
          border-radius: 4px;
          object-fit: cover;
        }
      }
    }
  }
}
</style>
