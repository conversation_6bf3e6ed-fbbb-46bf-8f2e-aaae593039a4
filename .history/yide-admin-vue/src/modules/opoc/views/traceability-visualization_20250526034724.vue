<template>
  <div class="traceability-visualization">
    <div class="visualization-header">
      <div class="search-box">
        <el-input
          v-model="searchCode"
          placeholder="请输入产品码"
          clearable
          @keyup.enter="loadData"
        >
          <template #append>
            <el-button @click="loadData">
              <el-icon><search /></el-icon>
            </el-button>
          </template>
        </el-input>
      </div>

      <div class="actions">
        <el-button type="primary" @click="exportImage">
          <el-icon><download /></el-icon>导出图片
        </el-button>
        <el-button @click="resetView">
          <el-icon><refresh-right /></el-icon>重置视图
        </el-button>
        <el-button type="success" @click="printTraceability">
          <el-icon><printer /></el-icon>打印溯源报告
        </el-button>
      </div>
    </div>

    <div class="visualization-content">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="10" animated />
      </div>

      <div v-else-if="!graphData.nodes.length" class="empty-container">
        <el-empty description="暂无溯源数据">
          <template #image>
            <el-icon class="empty-icon"><document-delete /></el-icon>
          </template>
          <template #description>
            <p>暂无溯源数据，请检查产品码是否正确</p>
          </template>
          <el-button @click="showScanDialog">扫描产品码</el-button>
        </el-empty>
      </div>

      <div v-else class="graph-container">
        <div class="graph-toolbar">
          <el-button-group>
            <el-tooltip content="放大">
              <el-button @click="zoomIn" size="small">
                <el-icon><zoom-in /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="缩小">
              <el-button @click="zoomOut" size="small">
                <el-icon><zoom-out /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="适应画布">
              <el-button @click="resetView" size="small">
                <el-icon><full-screen /></el-icon>
              </el-button>
            </el-tooltip>
          </el-button-group>

          <el-radio-group v-model="layoutDirection" size="small" @change="changeLayout">
            <el-radio-button label="LR">水平布局</el-radio-button>
            <el-radio-button label="TB">垂直布局</el-radio-button>
          </el-radio-group>
        </div>

        <div ref="graphRef" class="graph"></div>

        <div class="graph-legend">
          <div class="legend-item">
            <div class="legend-color" style="background-color: #1890FF;"></div>
            <span>正常节点</span>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background-color: #FF4D4F;"></div>
            <span>异常节点</span>
          </div>
        </div>
      </div>
    </div>

    <div v-if="productInfo" class="product-info">
      <el-card shadow="hover">
        <template #header>
          <div class="card-header">
            <span>产品信息</span>
            <el-button v-if="productInfo.id" type="text" @click="viewProductDetail">
              查看详情
            </el-button>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="产品码">{{ searchCode }}</el-descriptions-item>
          <el-descriptions-item label="产品名称">{{ productInfo.productName }}</el-descriptions-item>
          <el-descriptions-item label="产品编码">{{ productInfo.productCode }}</el-descriptions-item>
          <el-descriptions-item label="SKU编码">{{ productInfo.skuCode }}</el-descriptions-item>
          <el-descriptions-item label="批次号">{{ productInfo.batchNo }}</el-descriptions-item>
          <el-descriptions-item label="生产日期">{{ productInfo.produceDate }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>

    <el-drawer
      v-model="nodeDetailVisible"
      title="节点详情"
      size="30%"
      :with-header="true"
    >
      <div v-if="selectedNode" class="node-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="节点名称">{{ selectedNode.data.nodeName }}</el-descriptions-item>
          <el-descriptions-item label="节点类型">{{ getNodeTypeName(selectedNode.data.nodeType) }}</el-descriptions-item>
          <el-descriptions-item label="节点时间">{{ selectedNode.data.nodeTime }}</el-descriptions-item>
          <el-descriptions-item label="操作人">{{ selectedNode.data.operator || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="位置">{{ selectedNode.data.location || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedNode.data.status === 0 ? 'success' : 'danger'">
              {{ selectedNode.data.status === 0 ? '正常' : '异常' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="备注">{{ selectedNode.data.remark || '无' }}</el-descriptions-item>
        </el-descriptions>

        <div v-if="selectedNode.data.imageUrls" class="node-images">
          <h4>图片资料</h4>
          <div class="image-list">
            <el-image
              v-for="(url, index) in selectedNode.data.imageUrls.split(',')"
              :key="index"
              :src="url"
              fit="cover"
              class="node-image"
              :preview-src-list="selectedNode.data.imageUrls.split(',')"
            />
          </div>
        </div>

        <div class="node-actions">
          <el-button type="primary" @click="viewNodeTimeline">查看时间线</el-button>
        </div>
      </div>
    </el-drawer>

    <el-dialog v-model="scanDialogVisible" title="扫描产品码" width="30%">
      <div class="scan-dialog-content">
        <p>请使用扫码设备扫描产品上的二维码</p>
        <el-input v-model="scanInput" placeholder="或手动输入产品码" clearable />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="scanDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmScan">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { nextTick, onMounted, onUnmounted, ref } from 'vue';
import { useCool } from '/@/cool';

// 定义接口类型
interface ProductInfo {
  id?: string | number;
  productName?: string;
  productCode?: string;
  skuCode?: string;
  batchNo?: string;
  produceDate?: string;
}

interface NodeData {
  nodeName: string;
  nodeType: string;
  nodeTime: string;
  operator?: string;
  location?: string;
  status: number;
  remark?: string;
  imageUrls?: string;
}

interface GraphNode {
  id: string;
  data: NodeData;
}

interface GraphEdge {
  source: string;
  target: string;
}

// 定义组件名称
defineOptions({
  name: 'traceability-visualization'
});

const { service } = useCool();

// 状态变量
const searchCode = ref('');
const loading = ref(false);
const graphRef = ref<HTMLElement | null>(null);
const graph = ref<any>(null); // 暂时使用 any，因为 @antv/g6 可能未安装
const graphData = ref<{ nodes: GraphNode[]; edges: GraphEdge[] }>({ nodes: [], edges: [] });
const productInfo = ref<ProductInfo | null>(null);
const nodeDetailVisible = ref(false);
const selectedNode = ref<GraphNode | null>(null);
const layoutDirection = ref('LR');
const scanDialogVisible = ref(false);
const scanInput = ref('');

// 加载溯源数据
async function loadData() {
  if (!searchCode.value) {
    ElMessage.warning('请输入产品码');
    return;
  }

  loading.value = true;

  try {
    const res = await service.opoc.traceability.visualization.data({
      code: searchCode.value
    });

    if (res.code === 1000) {
      graphData.value = {
        nodes: res.data.nodes || [],
        edges: res.data.edges || []
      };
      productInfo.value = res.data.product;

      nextTick(() => {
        initGraph();
      });
    } else {
      ElMessage.error(res.message || '获取溯源数据失败');
    }
  } catch (error) {
    console.error('加载溯源数据失败', error);
    ElMessage.error('加载溯源数据失败');
  } finally {
    loading.value = false;
  }
}

// 初始化图表
function initGraph() {
  if (!graphRef.value) return;

  if (graph.value) {
    graph.value.destroy();
  }

  // 检查是否有图表库可用
  if (typeof window !== 'undefined' && (window as any).G6) {
    const G6 = (window as any).G6;

    // 图表配置
    graph.value = new G6.Graph({
      container: graphRef.value,
      width: graphRef.value.clientWidth,
      height: 600,
      layout: {
        type: 'dagre',
        rankdir: 'LR',
        nodesep: 50,
        ranksep: 100
      },
      defaultNode: {
        type: 'rect',
        size: [120, 60],
        style: {
          radius: 8,
          stroke: '#1890FF',
          lineWidth: 2,
          fill: '#fff'
        },
        labelCfg: {
          style: {
            fill: '#000',
            fontSize: 14
          }
        }
      },
      defaultEdge: {
        type: 'polyline',
        style: {
          radius: 10,
          offset: 30,
          endArrow: true,
          lineWidth: 2,
          stroke: '#1890FF'
        }
      },
      modes: {
        default: ['drag-canvas', 'zoom-canvas', 'drag-node']
      },
      fitView: true,
      animate: true
    });

    // 注册节点点击事件
    graph.value.on('node:click', (evt: any) => {
      const node = evt.item;
      selectedNode.value = node.getModel();
      nodeDetailVisible.value = true;
    });

    // 渲染图表
    graph.value.data(graphData.value);
    graph.value.render();
  } else {
    ElMessage.error('图表库未加载，请检查依赖');
  }
}

// 导出图片
function exportImage() {
  if (!graph.value || graphData.value.nodes.length === 0) {
    ElMessage.warning('暂无溯源数据可导出');
    return;
  }

  graph.value.downloadFullImage('溯源流程图', 'image/png', {
    backgroundColor: '#fff',
    padding: [20, 20, 20, 20]
  });
}

// 重置视图
function resetView() {
  if (graph.value) {
    graph.value.fitView();
  }
}

// 获取节点类型名称
function getNodeTypeName(nodeType: string) {
  const typeMap: Record<string, string> = {
    'material': '原材料',
    'production': '生产',
    'inspection': '检验',
    'logistics': '物流',
    'sales': '销售',
    'after-sales': '售后'
  };

  return typeMap[nodeType] || nodeType;
}

// 窗口大小变化时调整图表大小
function handleResize() {
  if (graph.value && graphRef.value) {
    graph.value.changeSize(graphRef.value.clientWidth, 600);
    graph.value.fitView();
  }
}

// 组件挂载时
onMounted(() => {
  window.addEventListener('resize', handleResize);
});

// 放大视图
function zoomIn() {
  if (graph.value) {
    const currentZoom = graph.value.getZoom();
    graph.value.zoomTo(currentZoom * 1.2);
  }
}

// 缩小视图
function zoomOut() {
  if (graph.value) {
    const currentZoom = graph.value.getZoom();
    graph.value.zoomTo(currentZoom * 0.8);
  }
}

// 更改布局方向
function changeLayout() {
  if (graph.value) {
    graph.value.updateLayout({
      rankdir: layoutDirection.value
    });
    graph.value.fitView();
  }
}

// 打印溯源报告
function printTraceability() {
  if (!graphData.value.nodes.length) {
    ElMessage.warning('暂无溯源数据可打印');
    return;
  }

  // 准备打印内容
  const printWindow = window.open('', '_blank');
  if (!printWindow) {
    ElMessage.error('打印窗口被阻止，请允许弹出窗口');
    return;
  }

  // 构建打印内容
  const printContent = `
    <html>
    <head>
      <title>溯源报告 - ${productInfo.value?.productName || '未知产品'}</title>
      <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        h1 { text-align: center; }
        .product-info { margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .node-list { margin-top: 20px; }
        .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #999; }
      </style>
    </head>
    <body>
      <h1>产品溯源报告</h1>
      <div class="product-info">
        <h2>产品信息</h2>
        <table>
          <tr><th>产品码</th><td>${searchCode.value}</td></tr>
          <tr><th>产品名称</th><td>${productInfo.value?.productName || '未知'}</td></tr>
          <tr><th>产品编码</th><td>${productInfo.value?.productCode || '未知'}</td></tr>
          <tr><th>SKU编码</th><td>${productInfo.value?.skuCode || '未知'}</td></tr>
          <tr><th>批次号</th><td>${productInfo.value?.batchNo || '未知'}</td></tr>
          <tr><th>生产日期</th><td>${productInfo.value?.produceDate || '未知'}</td></tr>
        </table>
      </div>
      <div class="node-list">
        <h2>溯源节点信息</h2>
        <table>
          <tr>
            <th>节点名称</th>
            <th>节点类型</th>
            <th>节点时间</th>
            <th>操作人</th>
            <th>位置</th>
            <th>状态</th>
          </tr>
          ${graphData.value.nodes.map(node => `
            <tr>
              <td>${node.data.nodeName}</td>
              <td>${getNodeTypeName(node.data.nodeType)}</td>
              <td>${node.data.nodeTime}</td>
              <td>${node.data.operator || '未知'}</td>
              <td>${node.data.location || '未知'}</td>
              <td>${node.data.status === 0 ? '正常' : '异常'}</td>
            </tr>
          `).join('')}
        </table>
      </div>
      <div class="footer">
        <p>打印时间: ${new Date().toLocaleString()}</p>
        <p>一物一码溯源系统</p>
      </div>
    </body>
    </html>
  `;

  // 使用现代方法写入内容
  printWindow.document.open();
  printWindow.document.write(printContent);
  printWindow.document.close();

  // 等待图片加载完成后打印
  setTimeout(() => {
    printWindow.print();
    printWindow.close();
  }, 500);
}

// 查看产品详情
function viewProductDetail() {
  if (productInfo.value && productInfo.value.id) {
    // 跳转到产品详情页面
    window.open(`/opoc/product-info?id=${productInfo.value.id}`, '_blank');
  }
}

// 查看节点时间线
function viewNodeTimeline() {
  if (selectedNode.value && selectedNode.value.data) {
    // 可以实现查看节点时间线的功能
    ElMessage.info('节点时间线功能开发中');
  }
}

// 显示扫描对话框
function showScanDialog() {
  scanInput.value = '';
  scanDialogVisible.value = true;
}

// 确认扫描结果
function confirmScan() {
  if (scanInput.value) {
    searchCode.value = scanInput.value;
    scanDialogVisible.value = false;
    loadData();
  } else {
    ElMessage.warning('请输入产品码');
  }
}

// 组件卸载时
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (graph.value) {
    graph.value.destroy();
  }
});
</script>

<style lang="scss" scoped>
.traceability-visualization {
  padding: 20px;

  .visualization-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .search-box {
      width: 300px;
    }

    .actions {
      display: flex;
      gap: 10px;
    }
  }

  .visualization-content {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: 600px;
    position: relative;

    .loading-container,
    .empty-container {
      height: 600px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .empty-icon {
        font-size: 60px;
        color: #909399;
        margin-bottom: 20px;
      }

      p {
        color: #909399;
        margin-bottom: 20px;
      }
    }

    .graph-container {
      width: 100%;
      height: 600px;
      position: relative;

      .graph-toolbar {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 10;
        display: flex;
        gap: 10px;
        background-color: rgba(255, 255, 255, 0.8);
        padding: 5px;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }

      .graph {
        width: 100%;
        height: 100%;
      }

      .graph-legend {
        position: absolute;
        bottom: 10px;
        left: 10px;
        z-index: 10;
        background-color: rgba(255, 255, 255, 0.8);
        padding: 10px;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

        .legend-item {
          display: flex;
          align-items: center;
          margin-bottom: 5px;

          &:last-child {
            margin-bottom: 0;
          }

          .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
            margin-right: 8px;
          }
        }
      }
    }
  }

  .product-info {
    margin-top: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .node-detail {
    .node-images {
      margin-top: 20px;

      h4 {
        margin-top: 0;
        margin-bottom: 10px;
        font-size: 14px;
        font-weight: 500;
      }

      .image-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .node-image {
          width: 100px;
          height: 100px;
          border-radius: 4px;
          object-fit: cover;
        }
      }
    }

    .node-actions {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }

  .scan-dialog-content {
    text-align: center;

    p {
      margin-bottom: 20px;
    }
  }
}
</style>
