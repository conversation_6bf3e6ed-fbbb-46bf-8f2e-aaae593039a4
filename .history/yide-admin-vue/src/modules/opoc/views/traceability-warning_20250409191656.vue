<template>
	<div class="warning-container">
		<div class="warning-header">
			<h2>溯源异常预警</h2>
			<div class="header-desc">显示所有状态为异常的溯源记录，及时发现产品质量和供应链问题</div>
		</div>
		
		<cl-crud @load="onLoad">
			<cl-row justify="between">
				<cl-flex1>
					<cl-search-key placeholder="输入产品码或产品名称搜索" />
				</cl-flex1>
				
				<cl-flex>
					<el-date-picker
						v-model="dateRange"
						type="daterange"
						range-separator="至"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
						format="YYYY-MM-DD"
						value-format="YYYY-MM-DD"
						@change="onDateChange"
					/>
				</cl-flex>
			</cl-row>
			
			<el-row>
				<el-col :span="24">
					<cl-table :props="table.props" :cols="table.cols" />
				</el-col>
			</el-row>
			
			<!-- 详情对话框 -->
			<el-dialog
				v-model="detailVisible"
				title="异常详情"
				width="700px"
				destroy-on-close
			>
				<el-descriptions :column="2" border v-if="currentNode">
					<el-descriptions-item label="产品码" :span="2">{{ currentNode.code }}</el-descriptions-item>
					<el-descriptions-item label="产品名称">{{ currentNode.productName || '-' }}</el-descriptions-item>
					<el-descriptions-item label="节点类型">{{ getNodeTypeName(currentNode.nodeType) }}</el-descriptions-item>
					<el-descriptions-item label="节点名称">{{ currentNode.nodeName }}</el-descriptions-item>
					<el-descriptions-item label="节点时间">{{ currentNode.nodeTime }}</el-descriptions-item>
					<el-descriptions-item label="操作人">{{ currentNode.operator || '-' }}</el-descriptions-item>
					<el-descriptions-item label="位置">{{ currentNode.location || '-' }}</el-descriptions-item>
					<el-descriptions-item label="备注" :span="2">{{ currentNode.remark || '-' }}</el-descriptions-item>
				</el-descriptions>
				
				<div class="warning-images" v-if="currentNode && currentNode.imageUrls">
					<div class="image-title">图片资料：</div>
					<div class="image-list">
						<el-image
							v-for="(url, index) in currentNode.imageUrls.split(',')"
							:key="index"
							:src="url"
							:preview-src-list="currentNode.imageUrls.split(',')"
							fit="cover"
							class="warning-image"
						/>
					</div>
				</div>
				
				<template #footer>
					<el-button @click="detailVisible = false">关闭</el-button>
					<el-button type="primary" @click="viewTraceReport">查看完整溯源报告</el-button>
				</template>
			</el-dialog>
		</cl-crud>
	</div>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import { useRouter } from "vue-router";
import { useOpoc } from "../index";
import { useCool } from "/@/cool";

const { service } = useCool();
const opoc = useOpoc();
const router = useRouter();

// 日期范围
const dateRange = ref(null);

// 详情对话框
const detailVisible = ref(false);
const currentNode = ref(null);

// 表格配置
const table = reactive({
	props: {
		border: true,
		highlightCurrentRow: true,
		'row-key': 'id'
	},
	cols: [
		{ label: "产品码", prop: "code", width: 120 },
		{ label: "产品名称", prop: "productName", width: 150, showOverflowTooltip: true },
		{ label: "节点类型", prop: "nodeType", width: 120, 
			dict: [
				{ label: "原材料", value: "material" },
				{ label: "生产", value: "production" },
				{ label: "检验", value: "inspection" },
				{ label: "物流", value: "logistics" },
				{ label: "销售", value: "sales" },
				{ label: "售后", value: "after-sales" }
			]
		},
		{ label: "节点名称", prop: "nodeName", width: 150, showOverflowTooltip: true },
		{ label: "节点时间", prop: "nodeTime", width: 170 },
		{ label: "操作人", prop: "operator", width: 100, showOverflowTooltip: true },
		{ label: "创建时间", prop: "createTime", width: 170 },
		{ 
			label: "异常等级", 
			prop: "status", 
			width: 100,
			slots: {
				default: ({ row }: any) => {
					return `<el-tag type="danger">异常</el-tag>`;
				}
			}
		},
		{
			type: "op",
			label: "操作",
			width: 160,
			buttons: [
				{ 
					label: "详情", 
					onClick: (row: any) => {
						currentNode.value = row;
						detailVisible.value = true;
					} 
				},
				{
					label: "溯源报告",
					onClick: (row: any) => {
						router.push({
							path: "/opoc/traceability-report",
							query: { code: row.code }
						});
					}
				}
			]
		}
	]
});

// 获取节点类型名称
function getNodeTypeName(nodeType: string) {
	const typeMap: Record<string, string> = {
		'material': '原材料',
		'production': '生产',
		'inspection': '检验',
		'logistics': '物流',
		'sales': '销售',
		'after-sales': '售后'
	};
	
	return typeMap[nodeType] || nodeType;
}

// 加载列表
function onLoad({ ctx, app }: any) {
	// 渲染之前
	ctx.service({
		page: service.opoc.traceability.warning
	});

	// 绑定搜索
	app.refs["cl-search-key"].onSearch = (val: string) => {
		app.refresh({
			params: {
				...app.params,
				keyWord: val
			}
		});
	};
}

// 日期变更
function onDateChange(val: any) {
	if (val && val.length === 2) {
		useCool().emit("opoc.traceability.warning.refresh", {
			startTime: val[0],
			endTime: val[1]
		});
	} else {
		useCool().emit("opoc.traceability.warning.refresh");
	}
}

// 查看完整溯源报告
function viewTraceReport() {
	if (currentNode.value) {
		router.push({
			path: "/opoc/traceability-report",
			query: { code: currentNode.value.code }
		});
	}
}
</script>

<style lang="scss" scoped>
.warning-container {
	padding: 20px;
	
	.warning-header {
		margin-bottom: 20px;
		
		h2 {
			margin: 0 0 10px 0;
			font-size: 18px;
			color: #f56c6c;
		}
		
		.header-desc {
			color: #909399;
			font-size: 14px;
		}
	}
	
	.warning-images {
		margin-top: 20px;
		
		.image-title {
			font-weight: bold;
			margin-bottom: 10px;
		}
		
		.image-list {
			display: flex;
			flex-wrap: wrap;
			gap: 10px;
			
			.warning-image {
				width: 100px;
				height: 100px;
				object-fit: cover;
				border-radius: 4px;
				border: 1px solid #eee;
			}
		}
	}
}
</style>
