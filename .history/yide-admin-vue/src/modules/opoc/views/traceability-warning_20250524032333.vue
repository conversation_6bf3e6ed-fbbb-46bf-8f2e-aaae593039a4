<template>
	<div class="warning-container">
		<div class="warning-header">
			<h2>溯源异常预警</h2>
			<div class="header-desc">显示所有状态为异常的溯源记录，及时发现产品质量和供应链问题</div>
		</div>

		<!-- 统计卡片 -->
		<el-row :gutter="20" class="stats-cards">
			<el-col :span="8">
				<el-card shadow="hover" class="stats-card">
					<template #header>
						<div class="card-header">
							<span>总异常数</span>
						</div>
					</template>
					<div class="stats-value">
						<span class="number">{{ stats.total || 0 }}</span>
						<el-icon color="#f56c6c" class="icon"><WarningFilled /></el-icon>
					</div>
				</el-card>
			</el-col>
			<el-col :span="8">
				<el-card shadow="hover" class="stats-card">
					<template #header>
						<div class="card-header">
							<span>今日异常</span>
						</div>
					</template>
					<div class="stats-value">
						<span class="number">{{ stats.today || 0 }}</span>
						<el-icon color="#e6a23c" class="icon"><Calendar /></el-icon>
					</div>
				</el-card>
			</el-col>
			<el-col :span="8">
				<el-card shadow="hover" class="stats-card">
					<template #header>
						<div class="card-header">
							<span>待处理异常</span>
						</div>
					</template>
					<div class="stats-value">
						<span class="number">{{ stats.pending || 0 }}</span>
						<el-icon color="#409eff" class="icon"><Loading /></el-icon>
					</div>
				</el-card>
			</el-col>
		</el-row>

		<!-- 图表区域 -->
		<el-row :gutter="20" class="stats-charts" v-if="showCharts">
			<el-col :span="12">
				<el-card shadow="hover" class="chart-card">
					<template #header>
						<div class="card-header">
							<span>异常趋势 (近30天)</span>
							<el-button type="text" @click="refreshStats">刷新</el-button>
						</div>
					</template>
					<div class="chart-container" ref="trendChartRef"></div>
				</el-card>
			</el-col>
			<el-col :span="12">
				<el-card shadow="hover" class="chart-card">
					<template #header>
						<div class="card-header">
							<span>异常分布 (按节点类型)</span>
							<el-button type="text" @click="refreshStats">刷新</el-button>
						</div>
					</template>
					<div class="chart-container" ref="nodeTypeChartRef"></div>
				</el-card>
			</el-col>
		</el-row>

		<el-divider content-position="center">异常记录列表</el-divider>

		<cl-crud @load="onLoad">
			<cl-row justify="between">
				<cl-flex1>
					<cl-search-key placeholder="输入产品码或产品名称搜索" />
				</cl-flex1>

				<cl-flex>
					<el-select v-model="filterStatus" placeholder="异常等级" clearable @change="onStatusChange">
						<el-option label="全部异常" :value="null" />
						<el-option label="轻微异常" :value="1" />
						<el-option label="严重异常" :value="2" />
						<el-option label="致命异常" :value="3" />
					</el-select>

					<el-select v-model="filterNodeType" placeholder="节点类型" clearable @change="onNodeTypeChange" style="margin-left: 10px;">
						<el-option label="全部节点" :value="null" />
						<el-option v-for="item in nodeTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>

					<el-date-picker
						v-model="dateRange"
						type="daterange"
						range-separator="至"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
						format="YYYY-MM-DD"
						value-format="YYYY-MM-DD"
						@change="onDateChange"
						style="margin-left: 10px;"
					/>
				</cl-flex>
			</cl-row>

			<el-row>
				<el-col :span="24">
					<cl-table :props="table.props" :cols="table.cols" />
				</el-col>
			</el-row>

			<!-- 详情对话框 -->
			<el-dialog
				v-model="detailVisible"
				title="异常详情"
				width="700px"
				destroy-on-close
			>
				<el-descriptions :column="2" border v-if="currentNode">
					<el-descriptions-item label="产品码" :span="2">{{ currentNode.code }}</el-descriptions-item>
					<el-descriptions-item label="产品名称">{{ currentNode.productName || '-' }}</el-descriptions-item>
					<el-descriptions-item label="节点类型">{{ getNodeTypeName(currentNode.nodeType) }}</el-descriptions-item>
					<el-descriptions-item label="节点名称">{{ currentNode.nodeName }}</el-descriptions-item>
					<el-descriptions-item label="节点时间">{{ currentNode.nodeTime }}</el-descriptions-item>
					<el-descriptions-item label="操作人">{{ currentNode.operator || '-' }}</el-descriptions-item>
					<el-descriptions-item label="位置">{{ currentNode.location || '-' }}</el-descriptions-item>
					<el-descriptions-item label="异常等级">{{ getStatusLabel(currentNode.status) }}</el-descriptions-item>
					<el-descriptions-item label="创建时间">{{ currentNode.createTime }}</el-descriptions-item>
					<el-descriptions-item label="备注" :span="2">{{ currentNode.remark || '-' }}</el-descriptions-item>
				</el-descriptions>

				<div class="warning-images" v-if="currentNode && currentNode.imageUrls">
					<div class="image-title">图片资料：</div>
					<div class="image-list">
						<el-image
							v-for="(url, index) in currentNode.imageUrls.split(',')"
							:key="index"
							:src="url"
							:preview-src-list="currentNode.imageUrls.split(',')"
							fit="cover"
							class="warning-image"
						/>
					</div>
				</div>

				<div class="handle-form" v-if="currentNode && currentNode.status !== 0">
					<el-divider content-position="center">异常处理</el-divider>
					<el-form :model="handleForm" label-width="100px">
						<el-form-item label="处理结果">
							<el-select v-model="handleForm.handleResult" placeholder="请选择处理结果">
								<el-option label="已解决" value="resolved" />
								<el-option label="误报" value="false_alarm" />
								<el-option label="需要跟进" value="follow_up" />
							</el-select>
						</el-form-item>
						<el-form-item label="处理说明">
							<el-input v-model="handleForm.handleNote" type="textarea" rows="3" placeholder="请输入处理说明" />
						</el-form-item>
					</el-form>
				</div>

				<template #footer>
					<el-button @click="detailVisible = false">关闭</el-button>
					<el-button type="success" @click="handleWarning" v-if="currentNode && currentNode.status !== 0">标记为已处理</el-button>
					<el-button type="primary" @click="viewTraceReport">查看完整溯源报告</el-button>
				</template>
			</el-dialog>
		</cl-crud>
	</div>
</template>

<script lang="ts" setup>
import { Calendar, Loading, WarningFilled } from '@element-plus/icons-vue';
import * as echarts from 'echarts';
import { nextTick, onMounted, reactive, ref } from "vue";
import { useRouter } from "vue-router";
import { useOpoc } from "../index";
import { useCool } from "/@/cool";

const { service } = useCool();
const opoc = useOpoc();
const router = useRouter();

// 日期范围
const dateRange = ref(null);

// 详情对话框
const detailVisible = ref(false);
const currentNode = ref(null);

// 筛选条件
const filterStatus = ref(null);
const filterNodeType = ref(null);

// 节点类型选项
const nodeTypeOptions = [
	{ label: "原材料", value: "material" },
	{ label: "生产", value: "production" },
	{ label: "检验", value: "inspection" },
	{ label: "物流", value: "logistics" },
	{ label: "销售", value: "sales" },
	{ label: "售后", value: "after-sales" }
];

// 处理表单
const handleForm = reactive({
	handleResult: 'resolved',
	handleNote: ''
});

// 统计数据
const stats = reactive({
	total: 0,
	today: 0,
	pending: 0,
	statusCounts: [],
	nodeTypeCounts: [],
	dailyTrend: []
});

// 图表相关
const showCharts = ref(true);
const trendChartRef = ref(null);
const nodeTypeChartRef = ref(null);
let trendChart = null;
let nodeTypeChart = null;

// 表格配置
const table = reactive({
	props: {
		border: true,
		highlightCurrentRow: true,
		'row-key': 'id'
	},
	cols: [
		{ label: "产品码", prop: "code", width: 120 },
		{ label: "产品名称", prop: "productName", width: 150, showOverflowTooltip: true },
		{ label: "节点类型", prop: "nodeType", width: 120,
			dict: nodeTypeOptions
		},
		{ label: "节点名称", prop: "nodeName", width: 150, showOverflowTooltip: true },
		{ label: "节点时间", prop: "nodeTime", width: 170 },
		{ label: "操作人", prop: "operator", width: 100, showOverflowTooltip: true },
		{ label: "创建时间", prop: "createTime", width: 170 },
		{
			label: "异常等级",
			prop: "status",
			width: 100,
			slots: {
				default: ({ row }: any) => {
					if (row.status === 1) {
						return `<el-tag type="warning">轻微异常</el-tag>`;
					} else if (row.status === 2) {
						return `<el-tag type="danger">严重异常</el-tag>`;
					} else if (row.status === 3) {
						return `<el-tag type="danger" effect="dark">致命异常</el-tag>`;
					} else {
						return `<el-tag type="info">已处理</el-tag>`;
					}
				}
			}
		},
		{
			type: "op",
			label: "操作",
			width: 180,
			buttons: [
				{
					label: "详情",
					onClick: (row: any) => {
						currentNode.value = row;
						handleForm.handleResult = 'resolved';
						handleForm.handleNote = '';
						detailVisible.value = true;
					}
				},
				{
					label: "处理",
					type: "primary",
					hidden: ({ row }: any) => row.status === 0,
					onClick: (row: any) => {
						currentNode.value = row;
						handleForm.handleResult = 'resolved';
						handleForm.handleNote = '';
						detailVisible.value = true;
					}
				},
				{
					label: "溯源报告",
					onClick: (row: any) => {
						router.push({
							path: "/opoc/traceability-report",
							query: { code: row.code }
						});
					}
				}
			]
		}
	]
});

// 获取节点类型名称
function getNodeTypeName(nodeType: string) {
	const typeMap: Record<string, string> = {
		'material': '原材料',
		'production': '生产',
		'inspection': '检验',
		'logistics': '物流',
		'sales': '销售',
		'after-sales': '售后'
	};

	return typeMap[nodeType] || nodeType;
}

// 获取状态标签
function getStatusLabel(status: number) {
	const statusMap: Record<number, string> = {
		0: '已处理',
		1: '轻微异常',
		2: '严重异常',
		3: '致命异常'
	};

	return statusMap[status] || '未知状态';
}

// 加载列表
function onLoad({ ctx, app }: any) {
	// 渲染之前
	ctx.service({
		page: service.opoc.traceability.warning.list
	});

	// 绑定搜索
	app.refs["cl-search-key"].onSearch = (val: string) => {
		app.refresh({
			params: {
				...app.params,
				keyWord: val
			}
		});
	};

	// 加载统计数据
	loadStats();
}

// 加载统计数据
async function loadStats() {
	try {
		const res = await service.opoc.traceability.warning.stats();
		if (res) {
			// 处理统计数据
			stats.statusCounts = res.statusCounts || [];
			stats.nodeTypeCounts = res.nodeTypeCounts || [];
			stats.dailyTrend = res.dailyTrend || [];

			// 计算总数
			stats.total = stats.statusCounts.reduce((total, item) => total + parseInt(item.count), 0);

			// 计算今日数据
			const today = new Date().toISOString().split('T')[0];
			const todayData = stats.dailyTrend.find(item => item.date === today);
			stats.today = todayData ? parseInt(todayData.count) : 0;

			// 计算待处理数
			stats.pending = stats.total;

			// 初始化图表
			nextTick(() => {
				initCharts();
			});
		}
	} catch (error) {
		console.error('加载统计数据失败', error);
	}
}

// 初始化图表
function initCharts() {
	// 初始化趋势图表
	if (trendChartRef.value) {
		trendChart = echarts.init(trendChartRef.value);
		const dates = stats.dailyTrend.map(item => item.date);
		const counts = stats.dailyTrend.map(item => parseInt(item.count));

		trendChart.setOption({
			tooltip: {
				trigger: 'axis'
			},
			xAxis: {
				type: 'category',
				data: dates
			},
			yAxis: {
				type: 'value'
			},
			series: [{
				data: counts,
				type: 'line',
				smooth: true,
				areaStyle: {
					color: {
						type: 'linear',
						x: 0,
						y: 0,
						x2: 0,
						y2: 1,
						colorStops: [{
							offset: 0, color: 'rgba(245, 108, 108, 0.5)'
						}, {
							offset: 1, color: 'rgba(245, 108, 108, 0.1)'
						}]
					}
				},
				itemStyle: {
					color: '#f56c6c'
				},
				lineStyle: {
					color: '#f56c6c'
				}
			}]
		});
	}

	// 初始化节点类型图表
	if (nodeTypeChartRef.value) {
		nodeTypeChart = echarts.init(nodeTypeChartRef.value);
		const data = stats.nodeTypeCounts.map(item => {
			return {
				name: getNodeTypeName(item.nodeType),
				value: parseInt(item.count)
			};
		});

		nodeTypeChart.setOption({
			tooltip: {
				trigger: 'item',
				formatter: '{a} <br/>{b}: {c} ({d}%)'
			},
			legend: {
				orient: 'vertical',
				right: 10,
				top: 'center',
				data: data.map(item => item.name)
			},
			series: [
				{
					name: '异常分布',
					type: 'pie',
					radius: ['50%', '70%'],
					avoidLabelOverlap: false,
					itemStyle: {
						borderRadius: 10,
						borderColor: '#fff',
						borderWidth: 2
					},
					label: {
						show: false,
						position: 'center'
					},
					emphasis: {
						label: {
							show: true,
							fontSize: '14',
							fontWeight: 'bold'
						}
					},
					labelLine: {
						show: false
					},
					data: data
				}
			]
		});
	}
}

// 刷新统计数据
function refreshStats() {
	loadStats();
}

// 日期变更
function onDateChange(val: any) {
	if (val && val.length === 2) {
		useCool().emit("opoc.traceability.warning.refresh", {
			startTime: val[0],
			endTime: val[1]
		});
	} else {
		useCool().emit("opoc.traceability.warning.refresh");
	}
}

// 状态变更
function onStatusChange(val: any) {
	useCool().emit("opoc.traceability.warning.refresh", {
		status: val
	});
}

// 节点类型变更
function onNodeTypeChange(val: any) {
	useCool().emit("opoc.traceability.warning.refresh", {
		nodeType: val
	});
}

// 处理异常
async function handleWarning() {
	if (!currentNode.value) return;

	try {
		await service.opoc.traceability.warning.handle({
			id: currentNode.value.id,
			handleResult: handleForm.handleResult,
			handleNote: handleForm.handleNote
		});

		useCool().message.success('处理成功');
		detailVisible.value = false;
		useCool().emit("opoc.traceability.warning.refresh");
		loadStats(); // 刷新统计数据
	} catch (error) {
		console.error('处理异常失败', error);
		useCool().message.error('处理失败');
	}
}

// 查看完整溯源报告
function viewTraceReport() {
	if (currentNode.value) {
		router.push({
			path: "/opoc/traceability-report",
			query: { code: currentNode.value.code }
		});
	}
}

// 监听窗口大小变化，重绘图表
window.addEventListener('resize', () => {
	if (trendChart) trendChart.resize();
	if (nodeTypeChart) nodeTypeChart.resize();
});

// 组件卸载时销毁图表
onMounted(() => {
	// 初始化图表
	nextTick(() => {
		if (stats.dailyTrend.length > 0) {
			initCharts();
		}
	});
});
</script>

<style lang="scss" scoped>
.warning-container {
	padding: 20px;

	.warning-header {
		margin-bottom: 20px;

		h2 {
			margin: 0 0 10px 0;
			font-size: 18px;
			color: #f56c6c;
		}

		.header-desc {
			color: #909399;
			font-size: 14px;
		}
	}

	.stats-cards {
		margin-bottom: 20px;

		.stats-card {
			.card-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-weight: bold;
			}

			.stats-value {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 10px 0;

				.number {
					font-size: 28px;
					font-weight: bold;
					color: #303133;
				}

				.icon {
					font-size: 32px;
				}
			}
		}
	}

	.stats-charts {
		margin-bottom: 20px;

		.chart-card {
			.card-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-weight: bold;
			}

			.chart-container {
				height: 300px;
				width: 100%;
			}
		}
	}

	.warning-images {
		margin-top: 20px;

		.image-title {
			font-weight: bold;
			margin-bottom: 10px;
		}

		.image-list {
			display: flex;
			flex-wrap: wrap;
			gap: 10px;

			.warning-image {
				width: 100px;
				height: 100px;
				object-fit: cover;
				border-radius: 4px;
				border: 1px solid #eee;
			}
		}
	}

	.handle-form {
		margin-top: 20px;
		padding-top: 10px;
	}
}
</style>
