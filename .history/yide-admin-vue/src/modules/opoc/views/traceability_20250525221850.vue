<template>
	<div class="opoc-traceability">
		<cl-crud @load="onLoad">
			<cl-row justify="space-between">
				<cl-flex1>
					<cl-search-key placeholder="输入产品码或产品名称搜索" />
				</cl-flex1>

				<cl-flex>
					<el-button type="primary" @click="addNode">添加溯源节点</el-button>
				</cl-flex>
			</cl-row>

			<el-row>
				<el-col :span="24">
					<cl-table :props="table.props" :cols="table.cols" />
				</el-col>
			</el-row>

			<cl-upsert ref="upsertRef" :items="upsert.items" :props="upsert.props" :on-submit="onSubmit" />
		</cl-crud>
	</div>
</template>

<script lang="ts" setup>
import { ElMessage, ElMessageBox } from "element-plus";
import { reactive, ref } from "vue";
import { useOpoc } from "../index";
import { useCool } from "/@/cool";

const { service } = useCool();
const opoc = useOpoc();
const upsertRef = ref();

// 表格配置
const table = reactive({
	props: {
		border: true,
		highlightCurrentRow: true,
		'row-key': 'id'
	},
	cols: [
		{ type: "expand", width: 50,
			slots: {
				default: ({ row }: any) => {
					return `
						<div style="padding: 10px">
							<p><strong>产品码:</strong> ${row.code}</p>
							<p><strong>产品名称:</strong> ${row.productName || '未关联产品'}</p>
							<p><strong>操作人:</strong> ${row.operator || '未知'}</p>
							<p><strong>位置:</strong> ${row.location || '未记录'}</p>
							<p><strong>备注:</strong> ${row.remark || '无'}</p>
							<p><strong>创建时间:</strong> ${row.createTime}</p>
							${row.imageUrls ? `<p><strong>图片:</strong> <div style="display: flex; gap: 10px; margin-top: 5px">${row.imageUrls.split(',').map((url: string) => `<el-image style="width: 100px; height: 100px" :src="${url}" :preview-src-list="[${url}]"></el-image>`).join('')}</div></p>` : ''}
						</div>
					`;
				}
			}
		},
		{ label: "ID", prop: "id", width: 180, showOverflowTooltip: true },
		{ label: "产品码", prop: "code", width: 120 },
		{ label: "产品名称", prop: "productName", width: 150, showOverflowTooltip: true },
		{ label: "节点类型", prop: "nodeType", width: 120,
			dict: [
				{ label: "原材料", value: "material" },
				{ label: "生产", value: "production" },
				{ label: "检验", value: "inspection" },
				{ label: "物流", value: "logistics" },
				{ label: "销售", value: "sales" },
				{ label: "售后", value: "after-sales" }
			]
		},
		{ label: "节点名称", prop: "nodeName", width: 150, showOverflowTooltip: true },
		{ label: "节点时间", prop: "nodeTime", width: 170 },
		{ label: "状态", prop: "status", width: 100,
			dict: [
				{ label: "正常", value: 0, type: "success" },
				{ label: "异常", value: 1, type: "danger" }
			]
		},
		{
			type: "op",
			label: "操作",
			width: 160,
			buttons: [
				{ label: "编辑", onClick: (row: any) => upsertRef.value?.open("编辑溯源节点", row) },
				{
					label: "删除",
					onClick: (row: any) => {
						ElMessageBox.confirm(`确认删除该溯源节点?`, "提示", {
							type: "warning"
						})
							.then(() => {
								service.opoc.traceability
									.delete({ ids: [row.id] })
									.then(() => {
										ElMessage.success("删除成功");
										refresh();
									})
									.catch((err) => {
										ElMessage.error(err.message);
									});
							})
							.catch(() => null);
					}
				}
			]
		}
	]
});

// 表单配置
const upsert = reactive({
	items: [
		{ label: "产品码", prop: "code", required: true, component: "el-input" },
		{
			label: "节点类型",
			prop: "nodeType",
			required: true,
			component: {
				name: "el-select",
				options: [
					{ label: "原材料", value: "material" },
					{ label: "生产", value: "production" },
					{ label: "检验", value: "inspection" },
					{ label: "物流", value: "logistics" },
					{ label: "销售", value: "sales" },
					{ label: "售后", value: "after-sales" }
				]
			}
		},
		{ label: "节点名称", prop: "nodeName", required: true, component: "el-input" },
		{
			label: "节点时间",
			prop: "nodeTime",
			required: true,
			value: new Date(),
			component: {
				name: "el-date-picker",
				props: {
					type: "datetime",
					valueFormat: "YYYY-MM-DD HH:mm:ss"
				}
			}
		},
		{ label: "操作人", prop: "operator", component: "el-input" },
		{ label: "位置", prop: "location", component: "el-input" },
		{
			label: "状态",
			prop: "status",
			value: 0,
			component: {
				name: "el-select",
				options: [
					{ label: "正常", value: 0 },
					{ label: "异常", value: 1 }
				]
			}
		},
		{
			label: "备注",
			prop: "remark",
			component: {
				name: "el-input",
				props: {
					type: "textarea",
					rows: 3
				}
			}
		},
		{
			label: "图片",
			prop: "imageUrls",
			component: {
				name: "cl-upload",
				props: {
					multiple: true,
					limit: 5
				}
			}
		}
	],
	props: {
		width: "600px"
	}
});

// 加载列表
function onLoad({ ctx, app }: any) {
	// 渲染之前
	ctx.service({
		page: service.opoc.traceability.page
	});

	// 绑定搜索
	app.refs["cl-search-key"].onSearch = (val: string) => {
		app.refresh({
			params: {
				...app.params,
				keyWord: val
			}
		});
	};
}

// 添加节点
function addNode() {
	upsertRef.value?.open("添加溯源节点");
}

// 提交
async function onSubmit(data: any, { done, close }: any) {
	if (data.id) {
		await service.opoc.traceability.update(data);
	} else {
		await service.opoc.traceability.add(data);
	}

	ElMessage.success(data.id ? "修改成功" : "添加成功");

	done();
	close();
	refresh();
}

// 刷新
function refresh() {
	useCool().emit("opoc.traceability.refresh");
}
</script>

<style lang="scss">
.expand-form {
	padding: 10px;

	.form-item {
		margin-bottom: 10px;

		.label {
			font-weight: bold;
			margin-right: 10px;
		}
	}

	.images {
		display: flex;
		flex-wrap: wrap;
		gap: 10px;
		margin-top: 5px;

		.image {
			width: 100px;
			height: 100px;
			object-fit: cover;
		}
	}
}
</style>
