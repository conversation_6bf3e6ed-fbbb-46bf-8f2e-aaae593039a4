import { Base<PERSON>ontroller, CoolController } from '@cool-midway/core';
import { Body, Get, Inject, Post, Provide, Query } from '@midwayjs/core';
import { OpocActivityRecordEntity } from '../../entity/activity-record';
import { OpocActivityRecordService } from '../../service/activity-record';

/**
 * 活动参与记录管理
 */
@Provide()
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: OpocActivityRecordEntity,
  service: OpocActivityRecordService,
  pageQueryOp: {
    keyWordLikeFields: ['userName', 'userPhone', 'code', 'prizeName'],
    fieldEq: ['activityId', 'result', 'receiveStatus'],
    addOrderBy: {
      participationTime: 'DESC',
    },
  },
})
export class AdminOpocActivityRecordController extends BaseController {
  @Inject()
  opocActivityRecordService: OpocActivityRecordService;

  /**
   * 获取活动参与记录
   * @param activityId 活动ID
   * @param page 页码
   * @param size 每页数量
   */
  @Get('/activity-records')
  async getActivityRecords(
    @Query('activityId') activityId: number,
    @Query('page') page: number = 1,
    @Query('size') size: number = 20
  ) {
    try {
      const result = await this.opocActivityRecordService.getActivityRecords(
        activityId,
        page,
        size
      );
      return this.ok(result);
    } catch (error) {
      return this.fail(error.message || '获取活动参与记录失败');
    }
  }

  /**
   * 更新参与记录
   * @param id 记录ID
   * @param params 更新参数
   */
  @Post('/update-record')
  async updateRecord(@Body('id') id: number, @Body() params: any) {
    try {
      const result = await this.opocActivityRecordService.update(id, params);
      return this.ok(result);
    } catch (error) {
      return this.fail(error.message || '更新参与记录失败');
    }
  }
}
