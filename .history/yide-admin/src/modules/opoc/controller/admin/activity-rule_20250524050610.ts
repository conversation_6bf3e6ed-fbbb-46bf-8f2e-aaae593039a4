import { Base<PERSON>ontroller, CoolController } from '@cool-midway/core'
import { Body, Get, Inject, Post, Provide, Query } from '@midwayjs/core'
import { OpocActivityRuleService } from '../../service/activity-rule'
import { ActivityRuleParams } from '../../typings/opoc'

/**
 * 活动规则配置
 */
@Provide()
@CoolController({
  api: [],
  prefix: '/admin/opoc/activity/rule'
})
export class AdminActivityRuleController extends BaseController {
  @Inject()
  opocActivityRuleService: OpocActivityRuleService;

  /**
   * 获取活动规则
   * @param activityId 活动ID
   */
  @Get('/get', { summary: '获取活动规则' })
  async getRule(@Query('activityId') activityId: number) {
    return this.ok(await this.opocActivityRuleService.getRule(activityId));
  }

  /**
   * 保存活动规则
   * @param params 活动规则参数
   */
  @Post('/save', { summary: '保存活动规则' })
  async saveRule(@Body() params: ActivityRuleParams) {
    return this.ok(await this.opocActivityRuleService.saveRule(params));
  }
}
