import { <PERSON><PERSON><PERSON>roller, CoolController } from '@cool-midway/core'
import { Body, Inject, Post, Provide } from '@midwayjs/core'
import { OpocProductCodeEntity } from '../../entity/product-code'
import { OpocProductCodeService } from '../../service/product-code'
import { ExportParams, GenerateCodeParams } from '../../typings/opoc'

/**
 * 产品码管理
 */
@Provide()
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: OpocProductCodeEntity,
  service: OpocProductCodeService,
  pageQueryOp: {
    keyWordLikeFields: ['code'],
    fieldEq: ['status', 'productId'],
  },
})
export class AdminOpocCodeController extends BaseController {
  @Inject()
  opocProductCodeService: OpocProductCodeService;

  /**
   * 生成产品码
   * @param count 数量
   */
  @Post('/generate')
  async generate(@Body() body: GenerateCodeParams) {
    await this.opocProductCodeService.generate(body);
    return this.ok();
  }

  /**
   * 绑定产品码
   */
  @Post('/bind')
  async bind(@Body() body: any) {
    const { codeId, productId } = body;
    return this.ok(await this.opocProductCodeService.bind(codeId, productId));
  }

  /**
   * 激活产品码
   */
  @Post('/activate')
  async activate(@Body() body: { id: number }) {
    return this.ok(await this.opocProductCodeService.activate(body.id));
  }

  /**
   * 批量绑定产品码
   */
  @Post('/batchBind')
  async batchBind(@Body() body: BatchBindParams) {
    return this.ok(await this.opocProductCodeService.batchBind(body));
  }

  /**
   * 批量导出产品码
   */
  @Post('/export')
  async export(@Body() body: ExportParams) {
    return this.ok(await this.opocProductCodeService.exportCodes(body));
  }
}
