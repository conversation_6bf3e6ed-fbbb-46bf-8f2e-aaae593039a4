import { Base<PERSON><PERSON>roller, CoolController } from '@cool-midway/core';
import { Body, Get, Inject, Post, Provide, Query } from '@midwayjs/core';
import { OpocMarketingActivityEntity } from '../../entity/marketing-activity';
import { OpocMarketingActivityService } from '../../service/marketing-activity';
import { ActivityStatisticsParams, CreateActivityParams } from '../../typings/opoc';

/**
 * 营销活动管理
 */
@Provide()
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: OpocMarketingActivityEntity,
  service: OpocMarketingActivityService,
  pageQueryOp: {
    keyWordLikeFields: ['activityName', 'description'],
    fieldEq: ['status', 'activityType', 'requireProductCode'],
    addOrderBy: {
      createTime: 'DESC',
    },
  },
})
export class AdminOpocMarketingActivityController extends BaseController {
  @Inject()
  opocMarketingActivityService: OpocMarketingActivityService;

  @Inject()
  ctx: Context;

  /**
   * 创建活动
   * @param params 活动参数
   */
  @Post('/create')
  async create(@Body() params: CreateActivityParams) {
    try {
      // 从上下文中获取当前用户信息
      const { userId, username } = this.ctx.admin || {};
      const result = await this.opocMarketingActivityService.create(params, userId, username);
      return this.ok(result);
    } catch (error) {
      return this.fail(error.message || '创建活动失败');
    }
  }

  /**
   * 获取活动统计数据
   * @param params 统计参数
   */
  @Get('/statistics')
  async getStatistics(@Query() params: ActivityStatisticsParams) {
    try {
      const result = await this.opocMarketingActivityService.getStatistics(params);
      return this.ok(result);
    } catch (error) {
      return this.fail(error.message || '获取活动统计数据失败');
    }
  }
}
