import { Base<PERSON>ontroller, CoolController } from '@cool-midway/core'
import { Get, Inject, Provide, Query } from '@midwayjs/core'
import { OpocProductInfoEntity } from '../../entity/product-info'
import { OpocProductInfoService } from '../../service/product-info'

/**
 * 产品信息管理
 */
@Provide()
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: OpocProductInfoEntity,
  service: OpocProductInfoService,
  pageQueryOp: {
    keyWordLikeFields: ['productCode', 'productName', 'skuCode', 'skuName', 'batchNo'],
    fieldEq: ['batchNo'],
  },
})
export class AdminOpocProductController extends BaseController {
  @Inject()
  opocProductInfoService: OpocProductInfoService;

  /**
   * 获取产品溯源信息
   */
  @Get('/trace')
  async getTraceInfo(@Query('code') code: string) {
    return this.ok(await this.opocProductInfoService.getTraceInfo(code));
  }
}
