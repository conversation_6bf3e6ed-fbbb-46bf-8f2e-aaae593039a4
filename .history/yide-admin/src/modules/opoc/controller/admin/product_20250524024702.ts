import { BaseController, CoolController } from '@cool-midway/core';
import { Body, Get, Inject, Post, Provide, Query } from '@midwayjs/core';
import { OpocProductInfoEntity } from '../../entity/product-info';
import { OpocProductInfoService } from '../../service/product-info';

/**
 * 产品信息管理
 */
@Provide()
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: OpocProductInfoEntity,
  service: OpocProductInfoService,
  pageQueryOp: {
    keyWordLikeFields: ['productCode', 'productName', 'skuCode', 'skuName', 'batchNo'],
    fieldEq: ['batchNo'],
  },
})
export class AdminOpocProductController extends BaseController {
  @Inject()
  opocProductInfoService: OpocProductInfoService;

  /**
   * 获取产品溯源信息
   */
  @Get('/trace')
  async getTraceInfo(@Query('code') code: string) {
    return this.ok(await this.opocProductInfoService.getTraceInfo(code));
  }

  /**
   * 批量导入产品信息
   * @param file 文件
   */
  @Post('/import')
  async batchImport(@Body() body: { fileData: string }) {
    try {
      // 解析Base64编码的文件数据
      const fileData = body.fileData;
      if (!fileData) {
        return this.fail('请上传文件');
      }

      // 从Base64字符串中提取文件数据
      const base64Data = fileData.split(';base64,').pop();
      if (!base64Data) {
        return this.fail('无效的文件格式');
      }

      // 转换为Buffer
      const fileBuffer = Buffer.from(base64Data, 'base64');

      // 调用服务进行批量导入
      const result = await this.opocProductInfoService.batchImport(fileBuffer);

      return this.ok(result);
    } catch (error) {
      return this.fail(error.message || '批量导入失败');
    }
  }
}
