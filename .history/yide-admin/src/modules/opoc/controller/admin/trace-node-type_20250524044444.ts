import { Body, Get, Inject, Post, Provide, Query } from '@midwayjs/core';
import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { OpocTraceNodeTypeEntity } from '../../entity/trace-node-type';
import { OpocTraceNodeTypeService } from '../../service/trace-node-type';

/**
 * 溯源节点类型
 */
@Provide()
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: OpocTraceNodeTypeEntity,
  service: OpocTraceNodeTypeService,
  pageQueryOp: {
    fieldEq: ['status'],
    keyWordLikeFields: ['nodeType', 'nodeName', 'description']
  }
})
export class AdminTraceNodeTypeController extends BaseController {
  @Inject()
  opocTraceNodeTypeService: OpocTraceNodeTypeService;

  /**
   * 获取所有节点类型（用于下拉选择）
   */
  @Get('/listAll')
  async listAll() {
    return this.ok(await this.opocTraceNodeTypeService.listAll());
  }
}
