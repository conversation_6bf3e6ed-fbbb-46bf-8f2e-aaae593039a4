import { Base<PERSON>ontroller, CoolController } from '@cool-midway/core';
import { Get, Inject, Query } from '@midwayjs/core';
import { OpocTraceabilityVisualizationService } from '../../service/traceability-visualization';

/**
 * 溯源流程可视化
 */
@CoolController({
  api: [],
  prefix: '/admin/opoc/traceability/visualization',
})
export class AdminTraceabilityVisualizationController extends BaseController {
  @Inject()
  opocTraceabilityVisualizationService: OpocTraceabilityVisualizationService;

  /**
   * 获取溯源流程可视化数据
   */
  @Get('/data', { summary: '获取溯源流程可视化数据' })
  async getData(@Query() query: any) {
    return this.ok(await this.opocTraceabilityVisualizationService.getData(query));
  }
}
