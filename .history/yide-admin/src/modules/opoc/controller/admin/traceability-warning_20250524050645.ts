import { BaseController, CoolController } from '@cool-midway/core';
import { Body, Get, Inject, Post } from '@midwayjs/core';
import { OpocTraceabilityWarningService } from '../../service/traceability-warning';

/**
 * 溯源异常预警
 */
@Provide()
@CoolController({
  api: [],
  prefix: '/admin/opoc/traceability/warning',
})
export class AdminTraceabilityWarningController extends BaseController {
  @Inject()
  opocTraceabilityWarningService: OpocTraceabilityWarningService;

  /**
   * 获取异常溯源记录
   */
  @Post('/list', { summary: '获取异常溯源记录列表' })
  async getWarnings(@Body() body: any) {
    return this.ok(await this.opocTraceabilityWarningService.getWarnings(body));
  }

  /**
   * 获取异常统计数据
   */
  @Get('/stats', { summary: '获取异常统计数据' })
  async getWarningStats() {
    return this.ok(await this.opocTraceabilityWarningService.getWarningStats());
  }

  /**
   * 处理异常记录
   */
  @Post('/handle', { summary: '处理异常记录' })
  async handleWarning(@Body() body: any) {
    return this.ok(await this.opocTraceabilityWarningService.handleWarning(body));
  }
}
