import { BaseController, CoolController } from '@cool-midway/core';
import { Body, Inject, Post } from '@midwayjs/core';
import { OpocTraceabilityEntity } from '../../entity/traceability';
import { OpocTraceabilityService } from '../../service/traceability';

/**
 * 溯源管理
 */
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: OpocTraceabilityEntity,
  service: OpocTraceabilityService,
  pageQueryOp: {
    keyWordLikeFields: [
      'productCode',
      'productName',
      'nodeName',
      'operator',
      'location',
    ],
    fieldEq: ['status', 'nodeType'],
    addOrderBy: {
      nodeTime: 'DESC',
    },
  },
})
export class OpocTraceabilityController extends BaseController {
  @Inject()
  traceabilityService: OpocTraceabilityService;

  /**
   * 获取溯源报告
   */
  @Post('/report', { summary: '获取溯源报告' })
  async report(@Body() body: any) {
    return this.ok(await this.traceabilityService.report(body));
  }

  /**
   * 获取异常溯源记录
   */
  @Post('/warning', { summary: '获取异常溯源记录' })
  async warning(@Body() body: any) {
    return this.ok(await this.traceabilityService.warning(body));
  }
}
