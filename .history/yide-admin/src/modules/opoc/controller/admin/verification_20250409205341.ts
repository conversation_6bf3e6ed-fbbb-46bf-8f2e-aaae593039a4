import { BaseController, CoolController } from '@cool-midway/core';
import { Body, Get, Inject, Post, Provide } from '@midwayjs/core';
import { OpocVerificationLogEntity } from '../../entity/verification-log';
import { OpocVerificationService } from '../../service/verification';

/**
 * 验证管理
 */
@Provide()
@CoolController({
  api: ['info', 'list', 'page'],
  entity: OpocVerificationLogEntity,
  service: OpocVerificationService,
  pageQueryOp: {
    select: ['a.*', 'b.code'],
    join: [
      {
        entity: require('../../entity/product-code').OpocProductCodeEntity,
        alias: 'b',
        condition: 'a.codeId = b.id',
        type: 'leftJoin',
      },
    ],
    fieldEq: ['verifyResult'],
  },
})
export class AdminOpocVerificationController extends BaseController {
  @Inject()
  opocVerificationService: OpocVerificationService;

  /**
   * 验证产品真伪
   */
  @Post('/verify')
  async verify(@Body() body: any) {
    const { code, location } = body;
    return this.ok(await this.opocVerificationService.verify(code, location));
  }

  /**
   * 获取验证统计
   */
  @Get('/stats')
  async getStats() {
    return this.ok(await this.opocVerificationService.getStats());
  }
}
