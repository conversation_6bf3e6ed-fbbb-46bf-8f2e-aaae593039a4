import { Base<PERSON><PERSON>roller, CoolController } from '@cool-midway/core';
import { Body, Get, Inject, Post, Provide } from '@midwayjs/core';
import { OpocVerificationLogEntity } from '../../entity/verification-log';
import { OpocProductCodeService } from '../../service/product-code';
import { OpocVerificationService } from '../../service/verification';
import { VerifyCodeParams } from '../../typings/opoc';

/**
 * 验证管理
 */
@Provide()
@CoolController({
  api: ['info', 'list', 'page'],
  entity: OpocVerificationLogEntity,
  service: OpocVerificationService,
  pageQueryOp: {
    select: ['a.*', 'b.code'],
    join: [
      {
        entity: require('../../entity/product-code').OpocProductCodeEntity,
        alias: 'b',
        condition: 'a.codeId = b.id',
        type: 'leftJoin',
      },
    ],
    fieldEq: ['verifyResult'],
  },
})
export class AdminOpocVerificationController extends BaseController {
  @Inject()
  opocVerificationService: OpocVerificationService;

  @Inject()
  opocProductCodeService: OpocProductCodeService;

  /**
   * 验证产品真伪
   */
  @Post('/verify')
  async verify(@Body() body: VerifyCodeParams) {
    const { code, location } = body;
    return this.ok(await this.opocVerificationService.verify(code, location?.address));
  }

  /**
   * 获取验证统计
   */
  @Get('/stats')
  async getStats() {
    return this.ok(await this.opocVerificationService.getStats());
  }

  /**
   * 获取验证次数限制配置
   */
  @Get('/verifyLimitConfig')
  async getVerifyLimitConfig() {
    return this.ok({
      defaultLimit: this.opocVerificationService.codeConfig?.verifyLimit || 10
    });
  }
}
