import { <PERSON><PERSON><PERSON>roller, CoolController } from '@cool-midway/core';
import { Body, Get, Inject, Post, Provide, Query } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { OpocActivityRecordService } from '../../service/activity-record';
import { OpocMarketingActivityService } from '../../service/marketing-activity';

/**
 * APP端活动接口
 */
@Provide()
@CoolController({
  api: [],
  prefix: '/app/opoc/activity',
})
export class AppOpocActivityController extends BaseController {
  @Inject()
  opocMarketingActivityService: OpocMarketingActivityService;

  @Inject()
  opocActivityRecordService: OpocActivityRecordService;

  @Inject()
  ctx: Context;

  /**
   * 获取活动列表
   */
  @Get('/list')
  async list() {
    try {
      // 只返回进行中的活动
      const activities = await this.opocMarketingActivityService.opocMarketingActivityEntity.find({
        where: { status: 1 },
        order: { createTime: 'DESC' },
      });
      return this.ok(activities);
    } catch (error) {
      return this.fail(error.message || '获取活动列表失败');
    }
  }

  /**
   * 获取活动详情
   * @param id 活动ID
   */
  @Get('/info')
  async info(@Query('id') id: number) {
    try {
      const activity = await this.opocMarketingActivityService.opocMarketingActivityEntity.findOneBy({ id });
      if (!activity) {
        return this.fail('活动不存在');
      }
      return this.ok(activity);
    } catch (error) {
      return this.fail(error.message || '获取活动详情失败');
    }
  }

  /**
   * 参与活动
   * @param params 参与参数
   */
  @Post('/participate')
  async participate(@Body() params: any) {
    try {
      // 从上下文中获取当前用户信息
      const { id: userId, username, phone } = this.ctx?.user || {};

      // 添加用户信息
      params.userId = userId;
      params.userName = username;
      params.userPhone = phone;

      // 添加设备信息
      const userAgent = this.ctx?.request?.headers['user-agent'];
      params.deviceInfo = userAgent;

      // 添加IP信息
      params.ip = this.ctx?.request?.ip;

      const result = await this.opocActivityRecordService.create(params);
      return this.ok(result);
    } catch (error) {
      return this.fail(error.message || '参与活动失败');
    }
  }

  /**
   * 获取用户参与记录
   * @param activityId 活动ID
   */
  @Get('/user-records')
  async getUserRecords(@Query('activityId') activityId?: number) {
    try {
      // 从上下文中获取当前用户信息
      const { id: userId } = this.ctx?.user || {};
      if (!userId) {
        return this.fail('用户未登录');
      }

      const records = await this.opocActivityRecordService.getUserRecords(userId, activityId);
      return this.ok(records);
    } catch (error) {
      return this.fail(error.message || '获取参与记录失败');
    }
  }
}
