import { BaseService } from '@cool-midway/core';
import { Provide } from '@midwayjs/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { OpocActivityRecordEntity } from '../entity/activity-record';
import { OpocMarketingActivityEntity } from '../entity/marketing-activity';
import { OpocProductCodeEntity } from '../entity/product-code';

/**
 * 活动参与记录服务
 */
@Provide()
export class OpocActivityRecordService extends BaseService {
  @InjectEntityModel(OpocActivityRecordEntity)
  opocActivityRecordEntity: Repository<OpocActivityRecordEntity>;

  @InjectEntityModel(OpocMarketingActivityEntity)
  opocMarketingActivityEntity: Repository<OpocMarketingActivityEntity>;

  @InjectEntityModel(OpocProductCodeEntity)
  opocProductCodeEntity: Repository<OpocProductCodeEntity>;

  /**
   * 创建活动参与记录
   * @param params 参与记录参数
   */
  async create(params: any) {
    // 检查活动是否存在
    const activity = await this.opocMarketingActivityEntity.findOneBy({
      id: params.activityId,
    });
    if (!activity) {
      throw new Error('活动不存在');
    }

    // 检查活动状态
    if (activity.status !== 1) {
      throw new Error('活动未开始或已结束');
    }

    // 检查参与限制
    if (activity.participationLimit > 0 && params.userId) {
      const count = await this.checkParticipationLimit(
        activity.id,
        params.userId,
        activity.participationLimit,
        activity.customLimit
      );
      if (count >= (activity.participationLimit === 3 ? activity.customLimit : 1)) {
        throw new Error('已达到活动参与次数限制');
      }
    }

    // 如果需要验证产品码
    if (activity.requireProductCode === 1 && params.code) {
      const codeInfo = await this.opocProductCodeEntity.findOneBy({ code: params.code });
      if (!codeInfo) {
        throw new Error('产品码不存在');
      }
      if (codeInfo.status !== 1 && codeInfo.status !== 2) {
        throw new Error('产品码未绑定或已失效');
      }
      params.codeId = codeInfo.id;
    }

    // 创建参与记录
    const record = this.opocActivityRecordEntity.create({
      ...params,
      participationTime: new Date(),
    });

    return await this.opocActivityRecordEntity.save(record);
  }

  /**
   * 检查参与限制
   * @param activityId 活动ID
   * @param userId 用户ID
   * @param limitType 限制类型
   * @param customLimit 自定义限制次数
   */
  private async checkParticipationLimit(
    activityId: number,
    userId: number,
    limitType: number,
    customLimit?: number
  ): Promise<number> {
    const queryBuilder = this.opocActivityRecordEntity
      .createQueryBuilder('record')
      .where('record.activityId = :activityId', { activityId })
      .andWhere('record.userId = :userId', { userId });

    // 根据限制类型添加条件
    if (limitType === 2) {
      // 每天一次
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      queryBuilder.andWhere('record.participationTime >= :today', { today });
      queryBuilder.andWhere('record.participationTime < :tomorrow', { tomorrow });
    }

    return await queryBuilder.getCount();
  }

  /**
   * 更新参与记录
   * @param param 更新参数
   */
  async update(param: any): Promise<void> {
    const { id, ...params } = param;
    const record = await this.opocActivityRecordEntity.findOneBy({ id });
    if (!record) {
      throw new Error('参与记录不存在');
    }

    await this.opocActivityRecordEntity.update(id, params);
  }

  /**
   * 自定义更新参与记录
   * @param id 记录ID
   * @param params 更新参数
   */
  async updateRecord(id: number, params: any): Promise<any> {
    const record = await this.opocActivityRecordEntity.findOneBy({ id });
    if (!record) {
      throw new Error('参与记录不存在');
    }

    const result = await this.opocActivityRecordEntity.update(id, params);
    return result.affected ? { success: true } : { success: false };
  }

  /**
   * 获取用户参与记录
   * @param userId 用户ID
   * @param activityId 活动ID
   */
  async getUserRecords(userId: number, activityId?: number) {
    const queryBuilder = this.opocActivityRecordEntity
      .createQueryBuilder('record')
      .where('record.userId = :userId', { userId });

    if (activityId) {
      queryBuilder.andWhere('record.activityId = :activityId', { activityId });
    }

    queryBuilder.orderBy('record.participationTime', 'DESC');

    return await queryBuilder.getMany();
  }

  /**
   * 获取活动参与记录
   * @param activityId 活动ID
   * @param page 页码
   * @param size 每页数量
   */
  async getActivityRecords(activityId: number, page = 1, size = 20) {
    const [records, total] = await this.opocActivityRecordEntity
      .createQueryBuilder('record')
      .where('record.activityId = :activityId', { activityId })
      .orderBy('record.participationTime', 'DESC')
      .skip((page - 1) * size)
      .take(size)
      .getManyAndCount();

    return {
      list: records,
      pagination: {
        page,
        size,
        total,
      },
    };
  }
}
