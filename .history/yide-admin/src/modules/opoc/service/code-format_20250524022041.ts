import { BaseService, CoolCommException } from '@cool-midway/core'
import { Config, Inject, Provide } from '@midwayjs/core'
import { InjectEntityModel } from '@midwayjs/typeorm'
import { Repository } from 'typeorm'
import { BaseSysParamService } from '../../base/service/sys/param'

/**
 * 码格式选择服务
 */
@Provide()
export class OpocCodeFormatService extends BaseService {
  @Inject()
  baseSysParamService: BaseSysParamService;

  @Config('module.opoc.config.code')
  codeConfig: {
    length: number;
    prefix: string;
    verifyLimit: number;
  };

  /**
   * 保存码格式配置
   * @param params 码格式参数
   */
  async saveFormat(params: {
    type: 'random' | 'number' | 'letter' | 'mixed' | 'custom';
    length: number;
    includeLetters?: boolean;
    includeNumbers?: boolean;
    includeSpecialChars?: boolean;
    prefix?: string;
  }) {
    // 验证参数
    if (params.length < 4 || params.length > 32) {
      throw new CoolCommException('码长度必须在4-32之间');
    }

    if (params.type === 'custom') {
      if (!params.includeLetters && !params.includeNumbers && !params.includeSpecialChars) {
        throw new CoolCommException('自定义码类型至少需要包含一种字符类型');
      }
    }

    // 保存到系统参数表
    await this.baseSysParamService.updateValue('opocCodeFormat', JSON.stringify(params));

    return params;
  }

  /**
   * 获取码格式配置
   */
  async getFormat() {
    try {
      const formatStr = await this.baseSysParamService.dataByKey('opocCodeFormat');
      if (formatStr) {
        return JSON.parse(formatStr);
      }
    } catch (error) {
      // 如果没有配置或解析错误，返回默认配置
    }

    // 默认配置
    return {
      type: 'mixed',
      length: this.codeConfig?.length || 8,
      includeLetters: true,
      includeNumbers: true,
      includeSpecialChars: false,
      prefix: this.codeConfig?.prefix || '',
    };
  }

  /**
   * 生成预览码
   * @param params 码格式参数
   */
  async generatePreview(params: {
    type: 'random' | 'number' | 'letter' | 'mixed' | 'custom';
    length: number;
    includeLetters?: boolean;
    includeNumbers?: boolean;
    includeSpecialChars?: boolean;
    prefix?: string;
  }) {
    let code = '';
    const prefix = params.prefix || '';
    
    // 根据类型生成不同的码
    switch (params.type) {
      case 'random':
        code = this.generateRandomCode(params.length);
        break;
      case 'number':
        code = this.generateNumberCode(params.length);
        break;
      case 'letter':
        code = this.generateLetterCode(params.length);
        break;
      case 'mixed':
        code = this.generateMixedCode(params.length);
        break;
      case 'custom':
        code = this.generateCustomCode(
          params.length,
          params.includeLetters || false,
          params.includeNumbers || false,
          params.includeSpecialChars || false
        );
        break;
      default:
        code = this.generateMixedCode(params.length);
    }
    
    return {
      code: prefix + code,
      format: params
    };
  }

  /**
   * 生成随机字符码
   */
  private generateRandomCode(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+';
    return this.generateCodeFromChars(chars, length);
  }

  /**
   * 生成数字码
   */
  private generateNumberCode(length: number): string {
    const chars = '0123456789';
    return this.generateCodeFromChars(chars, length);
  }

  /**
   * 生成字母码
   */
  private generateLetterCode(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    return this.generateCodeFromChars(chars, length);
  }

  /**
   * 生成混合码（字母+数字）
   */
  private generateMixedCode(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    return this.generateCodeFromChars(chars, length);
  }

  /**
   * 生成自定义码
   */
  private generateCustomCode(
    length: number,
    includeLetters: boolean,
    includeNumbers: boolean,
    includeSpecialChars: boolean
  ): string {
    let chars = '';
    
    if (includeLetters) {
      chars += 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    }
    
    if (includeNumbers) {
      chars += '0123456789';
    }
    
    if (includeSpecialChars) {
      chars += '!@#$%^&*()_+-=[]{}|;:,.<>?';
    }
    
    // 如果没有选择任何字符类型，默认使用字母+数字
    if (!chars) {
      chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    }
    
    return this.generateCodeFromChars(chars, length);
  }

  /**
   * 从字符集生成随机码
   */
  private generateCodeFromChars(chars: string, length: number): string {
    let result = '';
    const charsLength = chars.length;
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * charsLength));
    }
    
    return result;
  }
}
