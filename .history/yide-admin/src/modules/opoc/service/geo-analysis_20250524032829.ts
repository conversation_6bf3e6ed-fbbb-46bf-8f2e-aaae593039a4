import { BaseService } from '@cool-midway/core';
import { Inject, Provide } from '@midwayjs/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { OpocVerificationLogEntity } from '../entity/verification-log';
import { GeoAnalysisUtil, GeoPoint, HeatMapPoint } from '../util/geo-analysis';
import { getTimeRange, TimeRangeType } from '../util/statistics';
import * as moment from 'moment';

/**
 * 地理位置分析服务
 */
@Provide()
export class OpocGeoAnalysisService extends BaseService {
  @InjectEntityModel(OpocVerificationLogEntity)
  verificationLogEntity: Repository<OpocVerificationLogEntity>;

  /**
   * 按地理位置分组统计验证次数
   * @param timeRangeType 时间范围类型
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  async getGeoStat(timeRangeType: TimeRangeType, startTime?: string, endTime?: string) {
    // 获取时间范围
    const { startTime: start, endTime: end } = getTimeRange(timeRangeType, startTime, endTime);
    
    // 查询地理分布数据
    const geoData = await this.verificationLogEntity
      .createQueryBuilder()
      .select('verifyLocation')
      .addSelect('COUNT(*) as count')
      .addSelect('AVG(longitude) as longitude')
      .addSelect('AVG(latitude) as latitude')
      .where('verifyTime BETWEEN :start AND :end', { start, end })
      .andWhere('verifyLocation IS NOT NULL')
      .andWhere('longitude IS NOT NULL')
      .andWhere('latitude IS NOT NULL')
      .groupBy('verifyLocation')
      .orderBy('count', 'DESC')
      .limit(50)
      .getRawMany();
    
    return geoData.map(item => ({
      location: item.verifyLocation || '未知',
      count: parseInt(item.count) || 0,
      longitude: parseFloat(item.longitude) || 0,
      latitude: parseFloat(item.latitude) || 0
    }));
  }

  /**
   * 检测地理位置异常
   * @param codeId 产品码ID
   * @param timeThreshold 时间阈值（毫秒），默认为1小时
   * @param speedThreshold 速度阈值（米/秒），默认为100米/秒
   */
  async detectGeoAnomaly(codeId: number, timeThreshold: number = 3600000, speedThreshold: number = 100) {
    // 查询验证记录
    const logs = await this.verificationLogEntity.find({
      where: { codeId },
      order: { verifyTime: 'ASC' }
    });
    
    if (!logs || logs.length < 2) {
      return {
        hasAnomaly: false,
        anomalies: []
      };
    }
    
    // 转换为GeoPoint格式
    const points = logs.map(log => ({
      longitude: log.longitude,
      latitude: log.latitude,
      time: log.verifyTime,
      location: log.verifyLocation,
      id: log.id
    })).filter(point => point.longitude && point.latitude);
    
    // 检测异常
    const anomalies = GeoAnalysisUtil.detectAnomalies(points, timeThreshold, speedThreshold);
    
    return {
      hasAnomaly: anomalies.length > 0,
      anomalies: anomalies.map(anomaly => ({
        id: anomaly.point.id,
        verifyTime: anomaly.point.time,
        verifyLocation: anomaly.point.location,
        prevTime: anomaly.prevPoint.time,
        prevLocation: anomaly.prevPoint.location,
        distance: Math.round(anomaly.distance), // 四舍五入到整数米
        timeDiff: Math.round(anomaly.timeDiff / 1000), // 转换为秒
        speed: Math.round(anomaly.speed) // 四舍五入到整数米/秒
      }))
    };
  }

  /**
   * 获取热力图数据
   * @param timeRangeType 时间范围类型
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  async getHeatMapData(timeRangeType: TimeRangeType, startTime?: string, endTime?: string): Promise<HeatMapPoint[]> {
    // 获取时间范围
    const { startTime: start, endTime: end } = getTimeRange(timeRangeType, startTime, endTime);
    
    // 查询验证记录
    const logs = await this.verificationLogEntity
      .createQueryBuilder()
      .select(['longitude', 'latitude'])
      .where('verifyTime BETWEEN :start AND :end', { start, end })
      .andWhere('longitude IS NOT NULL')
      .andWhere('latitude IS NOT NULL')
      .getRawMany();
    
    // 转换为GeoPoint格式
    const points = logs.map(log => ({
      longitude: parseFloat(log.longitude),
      latitude: parseFloat(log.latitude)
    })).filter(point => !isNaN(point.longitude) && !isNaN(point.latitude));
    
    // 生成热力图数据
    return GeoAnalysisUtil.generateHeatMapData(points);
  }

  /**
   * 获取轨迹数据
   * @param codeId 产品码ID
   */
  async getTrajectoryData(codeId: number) {
    // 查询验证记录
    const logs = await this.verificationLogEntity.find({
      where: { codeId },
      order: { verifyTime: 'ASC' }
    });
    
    return logs.map(log => ({
      id: log.id,
      verifyTime: log.verifyTime,
      verifyLocation: log.verifyLocation || '未知',
      longitude: log.longitude,
      latitude: log.latitude
    })).filter(point => point.longitude && point.latitude);
  }

  /**
   * 获取地理位置分布统计
   * @param timeRangeType 时间范围类型
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  async getGeoDistribution(timeRangeType: TimeRangeType, startTime?: string, endTime?: string) {
    // 获取时间范围
    const { startTime: start, endTime: end } = getTimeRange(timeRangeType, startTime, endTime);
    
    // 查询地理分布数据
    const geoData = await this.verificationLogEntity
      .createQueryBuilder()
      .select('verifyLocation')
      .addSelect('COUNT(*) as count')
      .where('verifyTime BETWEEN :start AND :end', { start, end })
      .andWhere('verifyLocation IS NOT NULL')
      .groupBy('verifyLocation')
      .getRawMany();
    
    const locations = geoData.map(item => ({
      location: item.verifyLocation || '未知',
      count: parseInt(item.count) || 0
    }));
    
    // 按地区分组统计
    return GeoAnalysisUtil.groupByRegion(locations);
  }
}
