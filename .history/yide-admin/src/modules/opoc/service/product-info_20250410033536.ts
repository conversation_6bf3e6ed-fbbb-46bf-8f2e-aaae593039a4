import { BaseService } from '@cool-midway/core'
import { Provide } from '@midwayjs/core'
import { InjectEntityModel } from '@midwayjs/typeorm'
import { Repository } from 'typeorm'
import { OpocProductCodeEntity } from '../entity/product-code'
import { OpocProductInfoEntity } from '../entity/product-info'

/**
 * 产品信息服务
 */
@Provide()
export class OpocProductInfoService extends BaseService {
  @InjectEntityModel(OpocProductInfoEntity)
  opocProductInfoEntity: Repository<OpocProductInfoEntity>;

  @InjectEntityModel(OpocProductCodeEntity)
  opocProductCodeEntity: Repository<OpocProductCodeEntity>;

  /**
   * 获取产品溯源信息
   * @param code 产品码
   */
  async getTraceInfo(code: string) {
    const codeInfo = await this.opocProductCodeEntity.findOneBy({ code });
    if (!codeInfo) {
      throw new Error('产品码不存在');
    }

    if (!codeInfo.productId) {
      throw new Error('该产品码未绑定产品');
    }

    // 获取产品信息
    const product = await this.opocProductInfoEntity.findOneBy({
      id: codeInfo.productId,
    });
    if (!product) {
      throw new Error('产品信息不存在');
    }

    return {
      code,
      product,
    };
  }
}
