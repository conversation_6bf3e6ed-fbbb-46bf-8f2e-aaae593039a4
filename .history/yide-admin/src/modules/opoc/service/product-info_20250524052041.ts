import { BaseService } from '@cool-midway/core'
import { Provide } from '@midwayjs/core'
import { InjectEntityModel } from '@midwayjs/typeorm'
import { Repository } from 'typeorm'
import { OpocProductCodeEntity } from '../entity/product-code'
import { OpocProductInfoEntity } from '../entity/product-info'
import { ExcelUtil } from '../util/excel'

/**
 * 产品信息服务
 */
@Provide()
export class OpocProductInfoService extends BaseService {
  @InjectEntityModel(OpocProductInfoEntity)
  opocProductInfoEntity: Repository<OpocProductInfoEntity>;

  @InjectEntityModel(OpocProductCodeEntity)
  opocProductCodeEntity: Repository<OpocProductCodeEntity>;

  /**
   * 获取产品溯源信息
   * @param code 产品码
   */
  async getTraceInfo(code: string) {
    const codeInfo = await this.opocProductCodeEntity.findOneBy({ code });
    if (!codeInfo) {
      throw new Error('产品码不存在');
    }

    if (!codeInfo.productId) {
      throw new Error('该产品码未绑定产品');
    }

    // 获取产品信息
    const product = await this.opocProductInfoEntity.findOneBy({
      id: codeInfo.productId,
    });
    if (!product) {
      throw new Error('产品信息不存在');
    }

    return {
      code,
      product,
    };
  }

  /**
   * 批量导入产品信息
   * @param file 文件Buffer
   * @returns 导入结果
   */
  async batchImport(fileBuffer: Buffer) {
    try {
      // 解析Excel文件
      const data = ExcelUtil.parseExcel(fileBuffer, {
        headerRow: 1
      });

      if (!data || data.length === 0) {
        throw new BadRequestError('Excel文件中没有数据');
      }

      // 验证数据
      const validationResult = ExcelUtil.validateExcelData(data, {
        productCode: { required: true, type: 'string' },
        productName: { required: true, type: 'string' },
        skuCode: { required: true, type: 'string' },
        skuName: { type: 'string' },
        batchNo: { type: 'string' },
        produceDate: { type: 'date' },
        expireDate: { type: 'date' },
        status: { type: 'number' },
        description: { type: 'string' }
      });

      if (!validationResult.valid) {
        return {
          success: false,
          message: '数据验证失败',
          errors: validationResult.errors
        };
      }

      // 检查产品编码是否重复
      const productCodes = data.map(item => item.productCode);
      const existingProducts = await this.opocProductInfoEntity.find({
        where: {
          productCode: productCodes
        },
        select: ['productCode']
      });

      const existingProductCodes = existingProducts.map(p => p.productCode);
      const duplicateProductCodes = productCodes.filter(code =>
        existingProductCodes.includes(code)
      );

      if (duplicateProductCodes.length > 0) {
        return {
          success: false,
          message: '存在重复的产品编码',
          duplicates: duplicateProductCodes
        };
      }

      // 转换日期格式
      const productsToSave = data.map(item => {
        const product: any = { ...item };

        // 转换日期字段
        if (product.produceDate) {
          product.produceDate = new Date(product.produceDate);
        }

        if (product.expireDate) {
          product.expireDate = new Date(product.expireDate);
        }

        // 设置默认状态
        if (!product.status) {
          product.status = 1; // 默认为正常状态
        }

        return product;
      });

      // 批量保存产品信息
      const savedProducts = await this.opocProductInfoEntity.save(productsToSave);

      return {
        success: true,
        message: `成功导入${savedProducts.length}条产品信息`,
        data: savedProducts
      };
    } catch (error) {
      console.error('批量导入产品信息失败:', error);
      throw new BadRequestError(`批量导入产品信息失败: ${error.message}`);
    }
  }
}
