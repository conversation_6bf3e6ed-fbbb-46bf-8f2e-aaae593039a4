import { BaseService, CoolCommException } from '@cool-midway/core';
import { Provide } from '@midwayjs/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { OpocTraceNodeTypeEntity } from '../entity/trace-node-type';

/**
 * 溯源节点类型服务
 */
@Provide()
export class OpocTraceNodeTypeService extends BaseService {
  @InjectEntityModel(OpocTraceNodeTypeEntity)
  traceNodeTypeEntity: Repository<OpocTraceNodeTypeEntity>;

  /**
   * 新增溯源节点类型
   * @param param 节点类型信息
   */
  async add(param: any) {
    // 检查节点类型是否已存在
    const exists = await this.traceNodeTypeEntity.findOne({
      where: { nodeType: param.nodeType }
    });

    if (exists) {
      throw new CoolCommException('节点类型已存在');
    }

    return await this.traceNodeTypeEntity.save(param);
  }

  /**
   * 更新溯源节点类型
   * @param param 节点类型信息
   */
  async update(param: any) {
    const { id } = param;
    if (!id) {
      throw new CoolCommException('ID不能为空');
    }

    // 检查节点类型是否已存在（排除当前记录）
    const exists = await this.traceNodeTypeEntity.findOne({
      where: { nodeType: param.nodeType, id: { $ne: id } }
    });

    if (exists) {
      throw new CoolCommException('节点类型已存在');
    }

    return await this.traceNodeTypeEntity.update(id, param);
  }

  /**
   * 删除溯源节点类型
   * @param ids 节点类型ID数组
   */
  async delete(ids: number[]) {
    // 检查是否有溯源记录使用了该节点类型
    // TODO: 实现检查逻辑

    return await this.traceNodeTypeEntity.delete(ids);
  }

  /**
   * 获取所有节点类型（用于下拉选择）
   */
  async listAll() {
    return await this.traceNodeTypeEntity.find({
      where: { status: 1 },
      order: { sort: 'ASC' }
    });
  }
}
