import { BaseService, CoolCommException } from '@cool-midway/core';
import { Provide } from '@midwayjs/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { isEmpty } from 'lodash';
import { Repository } from 'typeorm';
import { OpocProductCodeEntity } from '../entity/product-code';
import { OpocProductInfoEntity } from '../entity/product-info';
import { OpocTraceabilityEntity } from '../entity/traceability';

/**
 * 溯源管理
 */
@Provide()
export class OpocTraceabilityService extends BaseService {
  @InjectEntityModel(OpocTraceabilityEntity)
  traceabilityEntity: Repository<OpocTraceabilityEntity>;

  @InjectEntityModel(OpocProductInfoEntity)
  productInfoEntity: Repository<OpocProductInfoEntity>;

  @InjectEntityModel(OpocProductCodeEntity)
  codeEntity: Repository<OpocProductCodeEntity>;

  /**
   * 添加溯源节点
   */
  async add(param) {
    if (!param.productCode) {
      throw new CoolCommException('产品编码不能为空');
    }
    return this.traceabilityEntity.save(param);
  }

  /**
   * 更新溯源节点数据
   */
  async updateData(param) {
    if (!param.id) {
      throw new CoolCommException('ID不能为空');
    }

    await this.modifyBefore(param);
    return this.traceabilityEntity.save(param);
  }

  /**
   * 删除溯源数据
   */
  async deleteData(ids) {
    if (isEmpty(ids)) {
      throw new CoolCommException('参数不能为空');
    }

    return this.traceabilityEntity.delete(ids);
  }

  /**
   * 查询溯源报告
   */
  async report(param) {
    if (!param.productId) {
      throw new CoolCommException('产品编码不能为空');
    }

    const result = await this.traceabilityEntity.find({
      where: {
        productId: param.productId,
      },
      order: {
        nodeTime: 'DESC',
      },
    });

    return result;
  }

  /**
   * 查询异常溯源记录
   */
  async warning(param) {
    const { startTime, endTime } = param;

    const queryBuilder =
      this.traceabilityEntity.createQueryBuilder('traceability');

    if (startTime && endTime) {
      queryBuilder.where(
        'traceability.nodeTime BETWEEN :startTime AND :endTime',
        {
          startTime,
          endTime,
        }
      );
    } else if (startTime) {
      queryBuilder.where('traceability.nodeTime >= :startTime', { startTime });
    }

    queryBuilder.andWhere('traceability.status != 0');

    return queryBuilder.getMany();
  }

  /**
   * 修改前的检查
   */
  public async modifyBefore(param) {
    const entity = await this.traceabilityEntity.findOne({
      where: {
        id: Number(param.id),
      },
    });

    if (!entity) {
      throw new CoolCommException('溯源记录不存在');
    }

    // 如果更新了节点时间或状态，记录变更
    if (param.nodeTime !== entity.nodeTime || param.status !== entity.status) {
      console.log(`溯源记录状态变更: ID=${entity.id}, 节点=${entity.nodeName}`);
    }
  }
}
