import { BaseService, CoolCommException } from '@cool-midway/core';
import { Provide } from '@midwayjs/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { isEmpty } from 'lodash';
import { Repository } from 'typeorm';
import { OpocProductCodeEntity } from '../entity/product-code';
import { OpocProductInfoEntity } from '../entity/product-info';
import { OpocTraceabilityEntity } from '../entity/traceability';

/**
 * 溯源管理
 */
@Provide()
export class OpocTraceabilityService extends BaseService {
  @InjectEntityModel(OpocTraceabilityEntity)
  traceabilityEntity: Repository<OpocTraceabilityEntity>;

  @InjectEntityModel(OpocProductInfoEntity)
  productInfoEntity: Repository<OpocProductInfoEntity>;

  @InjectEntityModel(OpocProductCodeEntity)
  codeEntity: Repository<OpocProductCodeEntity>;

  /**
   * 添加溯源节点
   */
  async add(param) {
    if (!param.code) {
      throw new CoolCommException('产品码不能为空');
    }
    return this.traceabilityEntity.save(param);
  }

  /**
   * 更新溯源节点数据
   */
  async updateData(param) {
    if (!param.id) {
      throw new CoolCommException('ID不能为空');
    }

    await this.modifyBefore(param, 'update');
    return this.traceabilityEntity.save(param);
  }

  /**
   * 删除溯源数据
   */
  async deleteData(ids) {
    if (isEmpty(ids)) {
      throw new CoolCommException('参数不能为空');
    }

    return this.traceabilityEntity.delete(ids);
  }

  /**
   * 查询溯源报告
   */
  async report(param) {
    if (!param.code) {
      throw new CoolCommException('产品码不能为空');
    }

    // 查询产品码信息
    const productCode = await this.codeEntity.findOne({
      where: { code: param.code }
    });

    if (!productCode) {
      throw new CoolCommException('产品码不存在');
    }

    // 查询产品信息
    let productInfo = null;
    if (productCode.productId) {
      productInfo = await this.productInfoEntity.findOne({
        where: { id: productCode.productId }
      });
    }

    // 查询溯源记录
    const traceNodes = await this.traceabilityEntity.find({
      where: {
        code: param.code,
      },
      order: {
        nodeTime: 'ASC',
      },
    });

    return {
      code: param.code,
      productInfo: productInfo ? {
        name: productInfo.productName,
        model: productInfo.skuName,
        description: productInfo.productName
      } : null,
      traceNodes: traceNodes || [],
      verifyCount: productCode.verifyCount || 0,
      lastVerifyTime: productCode.lastVerifyTime
    };
  }

  /**
   * 获取异常溯源记录
   */
  async warning(param) {
    const result = await this.traceabilityEntity.find({
      where: {
        status: 1, // 异常状态
      },
      order: {
        nodeTime: 'DESC',
      },
    });

    return result;
  }
}
