import { BaseService, CoolCommException } from '@cool-midway/core';
import { Provide } from '@midwayjs/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { OpocProductCodeEntity } from '../entity/product-code';
import { OpocProductInfoEntity } from '../entity/product-info';
import { OpocTraceabilityEntity } from '../entity/traceability';
import { OpocVerificationLogEntity } from '../entity/verification-log';

/**
 * 溯源管理服务
 */
@Provide()
export class OpocTraceabilityService extends BaseService {
  @InjectEntityModel(OpocTraceabilityEntity)
  traceabilityEntity: Repository<OpocTraceabilityEntity>;

  @InjectEntityModel(OpocProductInfoEntity)
  productInfoEntity: Repository<OpocProductInfoEntity>;

  @InjectEntityModel(OpocProductCodeEntity)
  codeEntity: Repository<OpocProductCodeEntity>;

  @InjectEntityModel(OpocVerificationLogEntity)
  verificationLogEntity: Repository<OpocVerificationLogEntity>;

  /**
   * 添加溯源节点
   */
  async add(param) {
    if (!param.code) {
      throw new CoolCommException('产品码不能为空');
    }
    return this.traceabilityEntity.save(param);
  }

  /**
   * 更新溯源节点数据
   */
  async update(param) {
    if (!param.id) {
      throw new CoolCommException('ID不能为空');
    }

    await this.modifyBefore(param, 'update');
    return this.traceabilityEntity.save(param);
  }

  /**
   * 删除溯源节点
   */
  async delete(ids) {
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      throw new CoolCommException('请选择要删除的记录');
    }
    await this.modifyBefore(ids, 'delete');
    await this.traceabilityEntity.delete(ids);
  }

  /**
   * 获取溯源报告
   */
  async report(param) {
    const { code } = param;
    if (!code) {
      throw new CoolCommException('产品码不能为空');
    }

    // 查询产品码信息
    const codeInfo = await this.codeEntity.findOne({
      where: { code },
      relations: ['productInfo']
    });

    if (!codeInfo) {
      throw new CoolCommException('产品码不存在');
    }

    // 查询产品信息
    let productInfo = null;
    if (codeInfo.productId) {
      productInfo = await this.productInfoEntity.findOne({
        where: { id: codeInfo.productId }
      });
    }

    // 查询溯源记录
    const traceNodes = await this.traceabilityEntity.find({
      where: { code },
      order: { nodeTime: 'ASC' }
    });

    // 查询验证次数
    const verifyCount = await this.verificationLogEntity.count({
      where: { codeId: codeInfo.id }
    });

    // 查询最后验证时间
    const lastVerification = await this.verificationLogEntity.findOne({
      where: { codeId: codeInfo.id },
      order: { verifyTime: 'DESC' }
    });

    return {
      code,
      productInfo,
      traceNodes,
      verifyCount,
      lastVerifyTime: lastVerification?.verifyTime
    };
  }

  /**
   * 获取异常溯源记录
   */
  async warning(param) {
    const { page = 1, size = 20, status, nodeType, keyWord } = param;

    const queryBuilder = this.traceabilityEntity.createQueryBuilder('traceability');

    // 默认查询异常状态
    if (status !== undefined) {
      queryBuilder.andWhere('traceability.status = :status', { status });
    } else {
      queryBuilder.andWhere('traceability.status != 0');
    }

    // 节点类型筛选
    if (nodeType) {
      queryBuilder.andWhere('traceability.nodeType = :nodeType', { nodeType });
    }

    // 关键词搜索
    if (keyWord) {
      queryBuilder.andWhere(
        '(traceability.code LIKE :keyWord OR traceability.productName LIKE :keyWord OR traceability.nodeName LIKE :keyWord)',
        { keyWord: `%${keyWord}%` }
      );
    }

    // 分页
    const [list, total] = await queryBuilder
      .orderBy('traceability.nodeTime', 'DESC')
      .skip((page - 1) * size)
      .take(size)
      .getManyAndCount();

    return {
      list,
      pagination: {
        page,
        size,
        total
      }
    };
  }
}
