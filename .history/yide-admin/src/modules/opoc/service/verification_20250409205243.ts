import { BaseService } from '@cool-midway/core'
import { Provide } from '@midwayjs/core'
import { InjectEntityModel } from '@midwayjs/typeorm'
import { Repository } from 'typeorm'
import { OpocProductCodeEntity } from '../entity/product-code'
import { OpocVerificationLogEntity } from '../entity/verification-log'

/**
 * 验证服务
 */
@Provide()
export class OpocVerificationService extends BaseService {
  @InjectEntityModel(OpocVerificationLogEntity)
  opocVerificationLogEntity: Repository<OpocVerificationLogEntity>;

  @InjectEntityModel(OpocProductCodeEntity)
  opocProductCodeEntity: Repository<OpocProductCodeEntity>;

  /**
   * 验证产品真伪
   * @param code 标识码
   * @param location 地理位置
   */
  async verify(code: string, location?: string) {
    const codeInfo = await this.opocProductCodeEntity.findOneBy({ code });

    // 验证结果 0:真 1:假
    let result = 1;
    if (codeInfo && codeInfo.status > 0) {
      result = 0;
    }

    // 记录验证日志
    await this.opocVerificationLogEntity.save({
      codeId: codeInfo?.id || 0,
      verifyTime: new Date(),
      verifyResult: result,
      verifyLocation: location,
    });

    return {
      isValid: result === 0,
      message: result === 0 ? '产品验证成功' : '产品验证失败，可能是假冒产品',
    };
  }

  /**
   * 获取验证统计
   */
  async getStats() {
    // 这里可以实现验证统计逻辑，如统计每天的验证次数、真假比例等
    return this.nativeQuery(`
      SELECT 
        DATE_FORMAT(verify_time, '%Y-%m-%d') as date,
        COUNT(*) as total,
        SUM(CASE WHEN verify_result = 0 THEN 1 ELSE 0 END) as valid,
        SUM(CASE WHEN verify_result = 1 THEN 1 ELSE 0 END) as invalid
      FROM opoc_verification_log
      GROUP BY DATE_FORMAT(verify_time, '%Y-%m-%d')
      ORDER BY date DESC
      LIMIT 30
    `);
  }
}
