import { Provide } from '@midwayjs/core';
import { createCanvas } from 'canvas';
import * as fs from 'fs';
import * as JsBarcode from 'jsbarcode';
import * as path from 'path';

/**
 * 条形码生成工具类
 */
@Provide()
export class BarcodeUtil {
  /**
   * 生成条形码
   * @param text 文本内容
   * @param options 选项
   * @returns 条形码图片的 base64 字符串
   */
  generate(text: string, options?: JsBarcode.Options): string {
    try {
      // 创建Canvas
      const canvas = createCanvas(300, 100);

      // 默认选项
      const defaultOptions: JsBarcode.Options = {
        format: 'CODE128',
        width: 2,
        height: 100,
        displayValue: true,
        text: text, // 显示的文本
        fontOptions: '',
        font: 'monospace',
        textAlign: 'center',
        textPosition: 'bottom',
        textMargin: 2,
        fontSize: 20,
        background: '#ffffff',
        lineColor: '#000000',
        margin: 10,
        marginTop: undefined,
        marginBottom: undefined,
        marginLeft: undefined,
        marginRight: undefined,
        valid: function(valid) {
          if (!valid) {
            throw new Error('条形码内容无效');
          }
        }
      };

      // 合并选项
      const mergedOptions = { ...defaultOptions, ...options };

      // 生成条形码
      JsBarcode(canvas, text, mergedOptions);

      // 返回base64
      return canvas.toDataURL('image/png');
    } catch (error) {
      console.error('生成条形码失败:', error);
      throw new Error('生成条形码失败');
    }
  }

  /**
   * 生成条形码并返回 Buffer
   * @param text 文本内容
   * @param options 选项
   * @returns 条形码图片的 Buffer
   */
  generateBuffer(text: string, options?: JsBarcode.Options): Buffer {
    try {
      // 创建Canvas
      const canvas = createCanvas(300, 100);

      // 默认选项
      const defaultOptions: JsBarcode.Options = {
        format: 'CODE128',
        width: 2,
        height: 100,
        displayValue: true,
        text: text, // 显示的文本
        fontOptions: '',
        font: 'monospace',
        textAlign: 'center',
        textPosition: 'bottom',
        textMargin: 2,
        fontSize: 20,
        background: '#ffffff',
        lineColor: '#000000',
        margin: 10,
        marginTop: undefined,
        marginBottom: undefined,
        marginLeft: undefined,
        marginRight: undefined,
        valid: function(valid) {
          if (!valid) {
            throw new Error('条形码内容无效');
          }
        }
      };

      // 合并选项
      const mergedOptions = { ...defaultOptions, ...options };

      // 生成条形码
      JsBarcode(canvas, text, mergedOptions);

      // 返回buffer
      return canvas.toBuffer('image/png');
    } catch (error) {
      console.error('生成条形码失败:', error);
      throw new Error('生成条形码失败');
    }
  }

  /**
   * 生成条形码并保存到文件
   * @param text 文本内容
   * @param filePath 文件保存路径
   * @param options 选项
   * @returns 保存的文件路径
   */
  generateToFile(text: string, filePath: string, options?: JsBarcode.Options): string {
    try {
      // 确保目录存在
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // 生成条形码Buffer
      const buffer = this.generateBuffer(text, options);

      // 写入文件
      fs.writeFileSync(filePath, buffer);

      return filePath;
    } catch (error) {
      console.error('生成条形码并保存到文件失败:', error);
      throw new Error('生成条形码并保存到文件失败');
    }
  }

  /**
   * 批量生成条形码
   * @param textList 文本内容列表
   * @param options 选项
   * @returns 条形码图片的 base64 字符串列表
   */
  batchGenerate(textList: string[], options?: JsBarcode.Options): string[] {
    try {
      const results: string[] = [];
      for (const text of textList) {
        const barcode = this.generate(text, options);
        results.push(barcode);
      }
      return results;
    } catch (error) {
      console.error('批量生成条形码失败:', error);
      throw new Error('批量生成条形码失败');
    }
  }

  /**
   * 批量生成条形码并保存到文件
   * @param textList 文本内容列表
   * @param dirPath 保存目录路径
   * @param fileNamePrefix 文件名前缀
   * @param options 选项
   * @returns 保存的文件路径列表
   */
  batchGenerateToFile(
    textList: string[],
    dirPath: string,
    fileNamePrefix: string = 'barcode',
    options?: JsBarcode.Options
  ): string[] {
    try {
      // 确保目录存在
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }

      const filePaths: string[] = [];
      for (let i = 0; i < textList.length; i++) {
        const filePath = path.join(dirPath, `${fileNamePrefix}_${i + 1}.png`);
        this.generateToFile(textList[i], filePath, options);
        filePaths.push(filePath);
      }
      return filePaths;
    } catch (error) {
      console.error('批量生成条形码并保存到文件失败:', error);
      throw new Error('批量生成条形码并保存到文件失败');
    }
  }

  /**
   * 生成EAN-13条形码
   * @param text 13位数字
   * @param options 选项
   * @returns 条形码图片的 base64 字符串
   */
  generateEAN13(text: string, options?: JsBarcode.Options): string {
    // 验证输入是否为13位数字
    if (!/^\d{13}$/.test(text)) {
      throw new Error('EAN-13条形码必须是13位数字');
    }

    const ean13Options: JsBarcode.Options = {
      ...options,
      format: 'EAN13'
    };

    return this.generate(text, ean13Options);
  }

  /**
   * 生成UPC-A条形码
   * @param text 12位数字
   * @param options 选项
   * @returns 条形码图片的 base64 字符串
   */
  generateUPCA(text: string, options?: JsBarcode.Options): string {
    // 验证输入是否为12位数字
    if (!/^\d{12}$/.test(text)) {
      throw new Error('UPC-A条形码必须是12位数字');
    }

    const upcaOptions: JsBarcode.Options = {
      ...options,
      format: 'UPC'
    };

    return this.generate(text, upcaOptions);
  }

  /**
   * 生成CODE39条形码
   * @param text 文本内容
   * @param options 选项
   * @returns 条形码图片的 base64 字符串
   */
  generateCODE39(text: string, options?: JsBarcode.Options): string {
    const code39Options: JsBarcode.Options = {
      ...options,
      format: 'CODE39'
    };

    return this.generate(text, code39Options);
  }
}
