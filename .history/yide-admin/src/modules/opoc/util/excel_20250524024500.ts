import { Readable } from 'stream';
import * as XLSX from 'xlsx';
import { BadRequestError } from '@midwayjs/core';

/**
 * Excel工具类
 */
export class ExcelUtil {
  /**
   * 解析Excel文件
   * @param buffer 文件Buffer
   * @param options 解析选项
   * @returns 解析结果
   */
  static parseExcel(buffer: Buffer, options?: {
    sheetIndex?: number;
    headerRow?: number;
    dateNF?: string;
  }): any[] {
    try {
      // 设置默认选项
      const opts = {
        sheetIndex: 0,
        headerRow: 1,
        dateNF: 'yyyy-mm-dd',
        ...options
      };

      // 解析Excel文件
      const workbook = XLSX.read(buffer, { type: 'buffer', cellDates: true, dateNF: opts.dateNF });
      
      // 获取第一个工作表
      const sheetNames = workbook.SheetNames;
      if (sheetNames.length === 0 || opts.sheetIndex >= sheetNames.length) {
        throw new BadRequestError('无效的Excel文件或工作表不存在');
      }
      
      const worksheet = workbook.Sheets[sheetNames[opts.sheetIndex]];
      
      // 转换为JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
        header: 1,
        raw: false,
        defval: ''
      });
      
      // 如果文件为空，返回空数组
      if (!jsonData || jsonData.length === 0) {
        return [];
      }
      
      // 获取表头
      const headers = jsonData[opts.headerRow - 1] as string[];
      if (!headers || headers.length === 0) {
        throw new BadRequestError('无效的Excel文件格式，找不到表头');
      }
      
      // 转换数据
      const result = [];
      for (let i = opts.headerRow; i < jsonData.length; i++) {
        const row = jsonData[i] as any[];
        if (!row || row.length === 0) continue;
        
        const item = {};
        for (let j = 0; j < headers.length; j++) {
          if (headers[j]) {
            item[headers[j]] = row[j] !== undefined ? row[j] : '';
          }
        }
        result.push(item);
      }
      
      return result;
    } catch (error) {
      console.error('解析Excel文件失败:', error);
      throw new BadRequestError(`解析Excel文件失败: ${error.message}`);
    }
  }

  /**
   * 生成Excel文件
   * @param data 数据
   * @param options 生成选项
   * @returns Buffer
   */
  static generateExcel(data: any[], options?: {
    sheetName?: string;
    header?: string[];
    dateNF?: string;
  }): Buffer {
    try {
      // 设置默认选项
      const opts = {
        sheetName: 'Sheet1',
        dateNF: 'yyyy-mm-dd',
        ...options
      };

      // 创建工作簿
      const workbook = XLSX.utils.book_new();
      
      // 创建工作表
      const worksheet = XLSX.utils.json_to_sheet(data, { 
        header: opts.header,
        dateNF: opts.dateNF
      });
      
      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, opts.sheetName);
      
      // 生成Excel文件
      const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      
      return excelBuffer;
    } catch (error) {
      console.error('生成Excel文件失败:', error);
      throw new BadRequestError(`生成Excel文件失败: ${error.message}`);
    }
  }

  /**
   * 验证Excel数据
   * @param data 数据
   * @param schema 验证模式
   * @returns 验证结果
   */
  static validateExcelData(data: any[], schema: Record<string, {
    required?: boolean;
    type?: 'string' | 'number' | 'boolean' | 'date';
    validator?: (value: any) => boolean | string;
  }>): { valid: boolean; errors: { row: number; field: string; message: string }[] } {
    const errors = [];
    
    // 遍历数据
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      
      // 遍历模式
      for (const field in schema) {
        const rule = schema[field];
        const value = row[field];
        
        // 检查必填字段
        if (rule.required && (value === undefined || value === null || value === '')) {
          errors.push({ row: i + 1, field, message: `${field}不能为空` });
          continue;
        }
        
        // 如果值为空且非必填，跳过后续验证
        if (value === undefined || value === null || value === '') {
          continue;
        }
        
        // 检查类型
        if (rule.type) {
          let typeValid = true;
          
          switch (rule.type) {
            case 'string':
              typeValid = typeof value === 'string';
              break;
            case 'number':
              typeValid = !isNaN(Number(value));
              break;
            case 'boolean':
              typeValid = typeof value === 'boolean' || value === 'true' || value === 'false';
              break;
            case 'date':
              typeValid = value instanceof Date || !isNaN(Date.parse(value));
              break;
          }
          
          if (!typeValid) {
            errors.push({ row: i + 1, field, message: `${field}类型不正确，应为${rule.type}` });
            continue;
          }
        }
        
        // 自定义验证器
        if (rule.validator) {
          const result = rule.validator(value);
          if (result !== true) {
            errors.push({ row: i + 1, field, message: typeof result === 'string' ? result : `${field}验证失败` });
          }
        }
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
}
