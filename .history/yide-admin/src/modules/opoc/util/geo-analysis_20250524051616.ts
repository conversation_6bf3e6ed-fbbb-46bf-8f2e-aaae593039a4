/**
 * 地理位置分析工具类
 */

/**
 * 地球半径（单位：米）
 */
const EARTH_RADIUS = 6371000;

/**
 * 坐标点
 */
export interface GeoPoint {
  longitude: number;
  latitude: number;
  id?: number;
  time?: Date;
  location?: string;
}

/**
 * 热力图点
 */
export interface HeatMapPoint {
  lng: number;
  lat: number;
  count: number;
}

/**
 * 地理位置分析工具类
 */
export class GeoAnalysisUtil {
  /**
   * 将角度转换为弧度
   * @param degree 角度
   * @returns 弧度
   */
  static degreeToRadian(degree: number): number {
    return (degree * Math.PI) / 180;
  }

  /**
   * 计算两个坐标点之间的距离（单位：米）
   * 使用Haversine公式计算球面距离
   * @param point1 坐标点1
   * @param point2 坐标点2
   * @returns 距离（米）
   */
  static calculateDistance(point1: GeoPoint, point2: GeoPoint): number {
    if (!point1 || !point2 || !point1.latitude || !point1.longitude || !point2.latitude || !point2.longitude) {
      return -1;
    }

    const lat1 = this.degreeToRadian(point1.latitude);
    const lng1 = this.degreeToRadian(point1.longitude);
    const lat2 = this.degreeToRadian(point2.latitude);
    const lng2 = this.degreeToRadian(point2.longitude);

    const dLat = lat2 - lat1;
    const dLng = lng2 - lng1;

    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(lat1) * Math.cos(lat2) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = EARTH_RADIUS * c;

    return distance;
  }

  /**
   * 检测地理位置异常
   * 判断条件：
   * 1. 两次验证时间间隔过短，但距离过远（超过正常人类移动速度）
   * 2. 同一产品码在不同地区短时间内多次验证
   * @param points 坐标点数组，包含时间信息
   * @param timeThreshold 时间阈值（毫秒），默认为1小时
   * @param speedThreshold 速度阈值（米/秒），默认为100米/秒（360公里/小时）
   * @returns 异常点数组
   */
  static detectAnomalies(
    points: Array<GeoPoint & { time: Date }>,
    timeThreshold: number = 3600000,
    speedThreshold: number = 100
  ): Array<{
    point: GeoPoint & { time: Date };
    prevPoint: GeoPoint & { time: Date };
    distance: number;
    timeDiff: number;
    speed: number;
  }> {
    if (!points || points.length < 2) {
      return [];
    }

    // 按时间排序
    const sortedPoints = [...points].sort((a, b) => a.time.getTime() - b.time.getTime());
    const anomalies = [];

    for (let i = 1; i < sortedPoints.length; i++) {
      const prevPoint = sortedPoints[i - 1];
      const currentPoint = sortedPoints[i];

      // 计算时间差（毫秒）
      const timeDiff = currentPoint.time.getTime() - prevPoint.time.getTime();

      // 如果时间差小于阈值，检查距离
      if (timeDiff < timeThreshold) {
        const distance = this.calculateDistance(prevPoint, currentPoint);

        // 如果距离有效
        if (distance > 0) {
          // 计算速度（米/秒）
          const speed = distance / (timeDiff / 1000);

          // 如果速度超过阈值，判定为异常
          if (speed > speedThreshold) {
            anomalies.push({
              point: currentPoint,
              prevPoint,
              distance,
              timeDiff,
              speed
            });
          }
        }
      }
    }

    return anomalies;
  }

  /**
   * 生成热力图数据
   * @param points 坐标点数组
   * @returns 热力图数据
   */
  static generateHeatMapData(points: Array<GeoPoint>): HeatMapPoint[] {
    if (!points || points.length === 0) {
      return [];
    }

    // 统计每个坐标点出现的次数
    const pointCountMap = new Map<string, { lng: number; lat: number; count: number }>();

    points.forEach(point => {
      if (point.longitude && point.latitude) {
        // 为了处理浮点数精度问题，保留6位小数
        const lng = parseFloat(point.longitude.toFixed(6));
        const lat = parseFloat(point.latitude.toFixed(6));
        const key = `${lng},${lat}`;

        if (pointCountMap.has(key)) {
          pointCountMap.get(key).count++;
        } else {
          pointCountMap.set(key, { lng, lat, count: 1 });
        }
      }
    });

    return Array.from(pointCountMap.values());
  }

  /**
   * 按地区分组统计
   * @param locations 地理位置数组
   * @returns 分组统计结果
   */
  static groupByRegion(locations: Array<{ location: string; count?: number }>): Array<{ name: string; value: number }> {
    if (!locations || locations.length === 0) {
      return [];
    }

    const regionCountMap = new Map<string, number>();

    locations.forEach(item => {
      if (item.location) {
        // 提取省市信息
        const region = this.extractRegion(item.location);
        const count = item.count || 1;

        if (regionCountMap.has(region)) {
          regionCountMap.set(region, regionCountMap.get(region) + count);
        } else {
          regionCountMap.set(region, count);
        }
      }
    });

    // 转换为echarts所需的格式
    return Array.from(regionCountMap.entries()).map(([name, value]) => ({ name, value }));
  }

  /**
   * 从地址中提取省市信息
   * @param address 地址
   * @returns 省市信息
   */
  static extractRegion(address: string): string {
    if (!address) return '未知';

    // 简单的省市提取逻辑，可根据实际需求调整
    const provincePattern = /(北京|上海|天津|重庆|河北|山西|辽宁|吉林|黑龙江|江苏|浙江|安徽|福建|江西|山东|河南|湖北|湖南|广东|海南|四川|贵州|云南|陕西|甘肃|青海|台湾|内蒙古|广西|西藏|宁夏|新疆|香港|澳门)(省|市|自治区|特别行政区)?/;
    const cityPattern = /([^省市自治区特别行政区]+)(市|地区|自治州)/;

    let province = '';
    let city = '';

    const provinceMatch = address.match(provincePattern);
    if (provinceMatch) {
      province = provinceMatch[1];
    }

    const cityMatch = address.match(cityPattern);
    if (cityMatch && cityMatch[1] !== province) {
      city = cityMatch[1];
    }

    if (province && city) {
      return `${province}${city}`;
    } else if (province) {
      return province;
    } else if (city) {
      return city;
    }

    return address;
  }
}
