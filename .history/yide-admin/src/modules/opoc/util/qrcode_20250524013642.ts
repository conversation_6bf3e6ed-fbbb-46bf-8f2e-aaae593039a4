import { Provide } from '@midwayjs/core';
import * as QRCode from 'qrcode';

/**
 * 二维码生成工具类
 */
@Provide()
export class QRCodeUtil {
  /**
   * 生成二维码
   * @param text 文本内容
   * @param options 选项
   * @returns 二维码图片的 base64 字符串
   */
  async generate(text: string, options?: QRCode.QRCodeToDataURLOptions): Promise<string> {
    try {
      // 默认选项
      const defaultOptions: QRCode.QRCodeToDataURLOptions = {
        errorCorrectionLevel: 'H', // 高容错率
        type: 'image/png',
        quality: 0.92,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        width: 200
      };

      // 合并选项
      const mergedOptions = { ...defaultOptions, ...options };

      // 生成二维码
      return await QRCode.toDataURL(text, mergedOptions);
    } catch (error) {
      console.error('生成二维码失败:', error);
      throw new Error('生成二维码失败');
    }
  }

  /**
   * 生成二维码并返回 Buffer
   * @param text 文本内容
   * @param options 选项
   * @returns 二维码图片的 Buffer
   */
  async generateBuffer(text: string, options?: QRCode.QRCodeToBufferOptions): Promise<Buffer> {
    try {
      // 默认选项
      const defaultOptions: QRCode.QRCodeToBufferOptions = {
        errorCorrectionLevel: 'H', // 高容错率
        type: 'png',
        quality: 0.92,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        width: 200
      };

      // 合并选项
      const mergedOptions = { ...defaultOptions, ...options };

      // 生成二维码
      return await QRCode.toBuffer(text, mergedOptions);
    } catch (error) {
      console.error('生成二维码失败:', error);
      throw new Error('生成二维码失败');
    }
  }

  /**
   * 生成带有Logo的二维码
   * @param text 文本内容
   * @param logoUrl Logo的URL
   * @param options 选项
   * @returns 带有Logo的二维码图片的 base64 字符串
   */
  async generateWithLogo(text: string, logoUrl: string, options?: QRCode.QRCodeToDataURLOptions): Promise<string> {
    // 这个功能需要在前端实现，因为需要使用Canvas来绘制Logo
    // 在后端可以生成普通二维码，然后在前端添加Logo
    return await this.generate(text, options);
  }
}
