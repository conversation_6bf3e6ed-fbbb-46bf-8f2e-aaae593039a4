import { Provide } from '@midwayjs/core';
import * as fs from 'fs';
import * as path from 'path';
import * as QRCode from 'qrcode';

/**
 * 二维码生成工具类
 */
@Provide()
export class QRCodeUtil {
  /**
   * 生成二维码
   * @param text 文本内容
   * @param options 选项
   * @returns 二维码图片的 base64 字符串
   */
  async generate(text: string, options?: QRCode.QRCodeToDataURLOptions): Promise<string> {
    try {
      // 默认选项
      const defaultOptions: QRCode.QRCodeToDataURLOptions = {
        errorCorrectionLevel: 'H', // 高容错率
        type: 'image/png',
        quality: 0.92,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        width: 200
      };

      // 合并选项
      const mergedOptions = { ...defaultOptions, ...options };

      // 生成二维码
      return await QRCode.toDataURL(text, mergedOptions);
    } catch (error) {
      console.error('生成二维码失败:', error);
      throw new Error('生成二维码失败');
    }
  }

  /**
   * 生成二维码并返回 Buffer
   * @param text 文本内容
   * @param options 选项
   * @returns 二维码图片的 Buffer
   */
  async generateBuffer(text: string, options?: QRCode.QRCodeToBufferOptions): Promise<Buffer> {
    try {
      // 默认选项
      const defaultOptions: QRCode.QRCodeToBufferOptions = {
        errorCorrectionLevel: 'H', // 高容错率
        type: 'png',
        quality: 0.92,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        width: 200
      };

      // 合并选项
      const mergedOptions = { ...defaultOptions, ...options };

      // 生成二维码
      return await QRCode.toBuffer(text, mergedOptions);
    } catch (error) {
      console.error('生成二维码失败:', error);
      throw new Error('生成二维码失败');
    }
  }

  /**
   * 生成二维码并保存到文件
   * @param text 文本内容
   * @param filePath 文件保存路径
   * @param options 选项
   * @returns 保存的文件路径
   */
  async generateToFile(text: string, filePath: string, options?: QRCode.QRCodeToFileOptions): Promise<string> {
    try {
      // 确保目录存在
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // 默认选项
      const defaultOptions: QRCode.QRCodeToFileOptions = {
        errorCorrectionLevel: 'H', // 高容错率
        type: 'png',
        quality: 0.92,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        width: 200
      };

      // 合并选项
      const mergedOptions = { ...defaultOptions, ...options };

      // 生成二维码并保存到文件
      await QRCode.toFile(filePath, text, mergedOptions);
      return filePath;
    } catch (error) {
      console.error('生成二维码并保存到文件失败:', error);
      throw new Error('生成二维码并保存到文件失败');
    }
  }

  /**
   * 生成带有Logo的二维码
   * @param text 文本内容
   * @param logoUrl Logo的URL
   * @param options 选项
   * @returns 带有Logo的二维码图片的 base64 字符串
   */
  async generateWithLogo(text: string, logoUrl: string, options?: QRCode.QRCodeToDataURLOptions): Promise<string> {
    // 这个功能需要在前端实现，因为需要使用Canvas来绘制Logo
    // 在后端可以生成普通二维码，然后在前端添加Logo
    return await this.generate(text, options);
  }

  /**
   * 批量生成二维码
   * @param textList 文本内容列表
   * @param options 选项
   * @returns 二维码图片的 base64 字符串列表
   */
  async batchGenerate(textList: string[], options?: QRCode.QRCodeToDataURLOptions): Promise<string[]> {
    try {
      const results: string[] = [];
      for (const text of textList) {
        const qrcode = await this.generate(text, options);
        results.push(qrcode);
      }
      return results;
    } catch (error) {
      console.error('批量生成二维码失败:', error);
      throw new Error('批量生成二维码失败');
    }
  }

  /**
   * 批量生成二维码并保存到文件
   * @param textList 文本内容列表
   * @param dirPath 保存目录路径
   * @param fileNamePrefix 文件名前缀
   * @param options 选项
   * @returns 保存的文件路径列表
   */
  async batchGenerateToFile(
    textList: string[],
    dirPath: string,
    fileNamePrefix: string = 'qrcode',
    options?: QRCode.QRCodeToFileOptions
  ): Promise<string[]> {
    try {
      // 确保目录存在
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }

      const filePaths: string[] = [];
      for (let i = 0; i < textList.length; i++) {
        const filePath = path.join(dirPath, `${fileNamePrefix}_${i + 1}.png`);
        await this.generateToFile(textList[i], filePath, options);
        filePaths.push(filePath);
      }
      return filePaths;
    } catch (error) {
      console.error('批量生成二维码并保存到文件失败:', error);
      throw new Error('批量生成二维码并保存到文件失败');
    }
  }
}
