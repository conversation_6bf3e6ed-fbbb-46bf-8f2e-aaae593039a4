{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Cool Admin",
            "type": "node",
            "request": "launch",
            "cwd": "${workspaceRoot}/yide-admin",
            "runtimeExecutable": "npm",
            "windows": {
                "runtimeExecutable": "npm.cmd"
            },
            "runtimeArgs": [
                "run",
                "dev"
            ],
            "env": {
                "NODE_ENV": "local"
            },
            "console": "integratedTerminal",
            "restart": true,
            "autoAttachChildProcesses": true
        },
        {
            "name": "Launch Chrome",
            "request": "launch",
            "type": "chrome",
            "url": "http://localhost:9000",
            "webRoot": "${workspaceFolder}/yide-admin-vue"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "dev",
            // pnpm 调试
            "runtimeExecutable": "pnpm",
            "runtimeArgs": ["dev"],
            "cwd": "${workspaceRoot}/yide-admin-vue",
            "skipFiles": ["<node_internals>/**"]
        }
    ]
}