import{_ as e,a as t,o as r,ah as o}from"./chunks/framework.BMq9nYrq.js";const m=JSON.parse('{"title":"介绍","description":"","frontmatter":{},"headers":[],"relativePath":"README.md","filePath":"README.md","lastUpdated":1720922603000}'),n={name:"README.md"};function l(s,a,c,d,i,_){return r(),t("div",null,a[0]||(a[0]=[o('<h1 id="介绍" tabindex="-1">介绍 <a class="header-anchor" href="#介绍" aria-label="Permalink to &quot;介绍&quot;">​</a></h1><p>这是 Cool Admin Vue 的在线文档仓库，我们会尽力完善文档。也希望广大开发者粉丝帮助完善文档，积极提交 Pull requests，共同参与开源，你的贡献可能给其他人代码巨大的帮助</p><h1 id="地址" tabindex="-1">地址 <a class="header-anchor" href="#地址" aria-label="Permalink to &quot;地址&quot;">​</a></h1><p>文档：<a href="https://vue.cool-admin.com" target="_blank" rel="noreferrer">https://vue.cool-admin.com</a></p><p>官网：<a href="https://cool-admin.com" target="_blank" rel="noreferrer">https://cool-admin.com</a></p>',5)]))}const h=e(n,[["render",l]]);export{m as __pageData,h as default};
