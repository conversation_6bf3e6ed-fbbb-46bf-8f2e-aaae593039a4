var Ot=Object.defineProperty;var Ft=(a,e,t)=>e in a?Ot(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t;var Ae=(a,e,t)=>Ft(a,typeof e!="symbol"?e+"":e,t);import{V as Rt,r as ie,f as ge,b2 as tt,b3 as Ct,b4 as At,b5 as Mt,m as $e,b6 as Lt,d as Dt,B as xe,b7 as nt,b8 as zt,b9 as Pt,n as jt,ba as Vt,p as Me,P as fe,O as _e,bb as $t,bc as Bt,W as Wt,$ as Kt,c as Jt,o as H,g as _,a0 as Ut,aj as qt,j as L,aN as Gt,aG as Ht,a as Z,b as Se,z as st,A as it,F as rt,I as he,h as pe,av as Qt,R as Yt,aQ as at,aW as Zt,a6 as Xt,ab as en,bd as tn,_ as nn}from"./framework.BMq9nYrq.js";import{u as sn,c as rn}from"./theme.9FcTwSv5.js";const an={root:()=>Rt(()=>import("./@localSearchIndexroot.Bo_C2rlf.js"),[])};/*!
* tabbable 6.2.0
* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
*/var gt=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"],Ne=gt.join(","),vt=typeof Element>"u",ae=vt?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,Oe=!vt&&Element.prototype.getRootNode?function(a){var e;return a==null||(e=a.getRootNode)===null||e===void 0?void 0:e.call(a)}:function(a){return a==null?void 0:a.ownerDocument},Fe=function a(e,t){var n;t===void 0&&(t=!0);var s=e==null||(n=e.getAttribute)===null||n===void 0?void 0:n.call(e,"inert"),r=s===""||s==="true",i=r||t&&e&&a(e.parentNode);return i},on=function(e){var t,n=e==null||(t=e.getAttribute)===null||t===void 0?void 0:t.call(e,"contenteditable");return n===""||n==="true"},bt=function(e,t,n){if(Fe(e))return[];var s=Array.prototype.slice.apply(e.querySelectorAll(Ne));return t&&ae.call(e,Ne)&&s.unshift(e),s=s.filter(n),s},yt=function a(e,t,n){for(var s=[],r=Array.from(e);r.length;){var i=r.shift();if(!Fe(i,!1))if(i.tagName==="SLOT"){var o=i.assignedElements(),l=o.length?o:i.children,c=a(l,!0,n);n.flatten?s.push.apply(s,c):s.push({scopeParent:i,candidates:c})}else{var f=ae.call(i,Ne);f&&n.filter(i)&&(t||!e.includes(i))&&s.push(i);var g=i.shadowRoot||typeof n.getShadowRoot=="function"&&n.getShadowRoot(i),h=!Fe(g,!1)&&(!n.shadowRootFilter||n.shadowRootFilter(i));if(g&&h){var b=a(g===!0?i.children:g.children,!0,n);n.flatten?s.push.apply(s,b):s.push({scopeParent:i,candidates:b})}else r.unshift.apply(r,i.children)}}return s},wt=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},re=function(e){if(!e)throw new Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||on(e))&&!wt(e)?0:e.tabIndex},ln=function(e,t){var n=re(e);return n<0&&t&&!wt(e)?0:n},cn=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},xt=function(e){return e.tagName==="INPUT"},un=function(e){return xt(e)&&e.type==="hidden"},dn=function(e){var t=e.tagName==="DETAILS"&&Array.prototype.slice.apply(e.children).some(function(n){return n.tagName==="SUMMARY"});return t},fn=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]},hn=function(e){if(!e.name)return!0;var t=e.form||Oe(e),n=function(o){return t.querySelectorAll('input[type="radio"][name="'+o+'"]')},s;if(typeof window<"u"&&typeof window.CSS<"u"&&typeof window.CSS.escape=="function")s=n(window.CSS.escape(e.name));else try{s=n(e.name)}catch(i){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",i.message),!1}var r=fn(s,e.form);return!r||r===e},pn=function(e){return xt(e)&&e.type==="radio"},mn=function(e){return pn(e)&&!hn(e)},gn=function(e){var t,n=e&&Oe(e),s=(t=n)===null||t===void 0?void 0:t.host,r=!1;if(n&&n!==e){var i,o,l;for(r=!!((i=s)!==null&&i!==void 0&&(o=i.ownerDocument)!==null&&o!==void 0&&o.contains(s)||e!=null&&(l=e.ownerDocument)!==null&&l!==void 0&&l.contains(e));!r&&s;){var c,f,g;n=Oe(s),s=(c=n)===null||c===void 0?void 0:c.host,r=!!((f=s)!==null&&f!==void 0&&(g=f.ownerDocument)!==null&&g!==void 0&&g.contains(s))}}return r},ot=function(e){var t=e.getBoundingClientRect(),n=t.width,s=t.height;return n===0&&s===0},vn=function(e,t){var n=t.displayCheck,s=t.getShadowRoot;if(getComputedStyle(e).visibility==="hidden")return!0;var r=ae.call(e,"details>summary:first-of-type"),i=r?e.parentElement:e;if(ae.call(i,"details:not([open]) *"))return!0;if(!n||n==="full"||n==="legacy-full"){if(typeof s=="function"){for(var o=e;e;){var l=e.parentElement,c=Oe(e);if(l&&!l.shadowRoot&&s(l)===!0)return ot(e);e.assignedSlot?e=e.assignedSlot:!l&&c!==e.ownerDocument?e=c.host:e=l}e=o}if(gn(e))return!e.getClientRects().length;if(n!=="legacy-full")return!0}else if(n==="non-zero-area")return ot(e);return!1},bn=function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if(t.tagName==="FIELDSET"&&t.disabled){for(var n=0;n<t.children.length;n++){var s=t.children.item(n);if(s.tagName==="LEGEND")return ae.call(t,"fieldset[disabled] *")?!0:!s.contains(e)}return!0}t=t.parentElement}return!1},Re=function(e,t){return!(t.disabled||Fe(t)||un(t)||vn(t,e)||dn(t)||bn(t))},Be=function(e,t){return!(mn(t)||re(t)<0||!Re(e,t))},yn=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},wn=function a(e){var t=[],n=[];return e.forEach(function(s,r){var i=!!s.scopeParent,o=i?s.scopeParent:s,l=ln(o,i),c=i?a(s.candidates):o;l===0?i?t.push.apply(t,c):t.push(o):n.push({documentOrder:r,tabIndex:l,item:s,isScope:i,content:c})}),n.sort(cn).reduce(function(s,r){return r.isScope?s.push.apply(s,r.content):s.push(r.content),s},[]).concat(t)},xn=function(e,t){t=t||{};var n;return t.getShadowRoot?n=yt([e],t.includeContainer,{filter:Be.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:yn}):n=bt(e,t.includeContainer,Be.bind(null,t)),wn(n)},_n=function(e,t){t=t||{};var n;return t.getShadowRoot?n=yt([e],t.includeContainer,{filter:Re.bind(null,t),flatten:!0,getShadowRoot:t.getShadowRoot}):n=bt(e,t.includeContainer,Re.bind(null,t)),n},oe=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return ae.call(e,Ne)===!1?!1:Be(t,e)},Sn=gt.concat("iframe").join(","),Le=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return ae.call(e,Sn)===!1?!1:Re(t,e)};/*!
* focus-trap 7.6.4
* @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
*/function We(a,e){(e==null||e>a.length)&&(e=a.length);for(var t=0,n=Array(e);t<e;t++)n[t]=a[t];return n}function En(a){if(Array.isArray(a))return We(a)}function Tn(a,e,t){return(e=Fn(e))in a?Object.defineProperty(a,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):a[e]=t,a}function In(a){if(typeof Symbol<"u"&&a[Symbol.iterator]!=null||a["@@iterator"]!=null)return Array.from(a)}function kn(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function lt(a,e){var t=Object.keys(a);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(a);e&&(n=n.filter(function(s){return Object.getOwnPropertyDescriptor(a,s).enumerable})),t.push.apply(t,n)}return t}function ct(a){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?lt(Object(t),!0).forEach(function(n){Tn(a,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(t)):lt(Object(t)).forEach(function(n){Object.defineProperty(a,n,Object.getOwnPropertyDescriptor(t,n))})}return a}function Nn(a){return En(a)||In(a)||Rn(a)||kn()}function On(a,e){if(typeof a!="object"||!a)return a;var t=a[Symbol.toPrimitive];if(t!==void 0){var n=t.call(a,e);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(a)}function Fn(a){var e=On(a,"string");return typeof e=="symbol"?e:e+""}function Rn(a,e){if(a){if(typeof a=="string")return We(a,e);var t={}.toString.call(a).slice(8,-1);return t==="Object"&&a.constructor&&(t=a.constructor.name),t==="Map"||t==="Set"?Array.from(a):t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?We(a,e):void 0}}var ut={activateTrap:function(e,t){if(e.length>0){var n=e[e.length-1];n!==t&&n._setPausedState(!0)}var s=e.indexOf(t);s===-1||e.splice(s,1),e.push(t)},deactivateTrap:function(e,t){var n=e.indexOf(t);n!==-1&&e.splice(n,1),e.length>0&&!e[e.length-1]._isManuallyPaused()&&e[e.length-1]._setPausedState(!1)}},Cn=function(e){return e.tagName&&e.tagName.toLowerCase()==="input"&&typeof e.select=="function"},An=function(e){return(e==null?void 0:e.key)==="Escape"||(e==null?void 0:e.key)==="Esc"||(e==null?void 0:e.keyCode)===27},ve=function(e){return(e==null?void 0:e.key)==="Tab"||(e==null?void 0:e.keyCode)===9},Mn=function(e){return ve(e)&&!e.shiftKey},Ln=function(e){return ve(e)&&e.shiftKey},dt=function(e){return setTimeout(e,0)},me=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),s=1;s<t;s++)n[s-1]=arguments[s];return typeof e=="function"?e.apply(void 0,n):e},Ee=function(e){return e.target.shadowRoot&&typeof e.composedPath=="function"?e.composedPath()[0]:e.target},Dn=[],zn=function(e,t){var n=(t==null?void 0:t.document)||document,s=(t==null?void 0:t.trapStack)||Dn,r=ct({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0,isKeyForward:Mn,isKeyBackward:Ln},t),i={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,manuallyPaused:!1,delayInitialFocusTimer:void 0,recentNavEvent:void 0},o,l=function(u,d,v){return u&&u[d]!==void 0?u[d]:r[v||d]},c=function(u,d){var v=typeof(d==null?void 0:d.composedPath)=="function"?d.composedPath():void 0;return i.containerGroups.findIndex(function(E){var T=E.container,O=E.tabbableNodes;return T.contains(u)||(v==null?void 0:v.includes(T))||O.find(function(S){return S===u})})},f=function(u){var d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},v=d.hasFallback,E=v===void 0?!1:v,T=d.params,O=T===void 0?[]:T,S=r[u];if(typeof S=="function"&&(S=S.apply(void 0,Nn(O))),S===!0&&(S=void 0),!S){if(S===void 0||S===!1)return S;throw new Error("`".concat(u,"` was specified but was not a node, or did not return a node"))}var R=S;if(typeof S=="string"){try{R=n.querySelector(S)}catch(m){throw new Error("`".concat(u,'` appears to be an invalid selector; error="').concat(m.message,'"'))}if(!R&&!E)throw new Error("`".concat(u,"` as selector refers to no known node"))}return R},g=function(){var u=f("initialFocus",{hasFallback:!0});if(u===!1)return!1;if(u===void 0||u&&!Le(u,r.tabbableOptions))if(c(n.activeElement)>=0)u=n.activeElement;else{var d=i.tabbableGroups[0],v=d&&d.firstTabbableNode;u=v||f("fallbackFocus")}else u===null&&(u=f("fallbackFocus"));if(!u)throw new Error("Your focus-trap needs to have at least one focusable element");return u},h=function(){if(i.containerGroups=i.containers.map(function(u){var d=xn(u,r.tabbableOptions),v=_n(u,r.tabbableOptions),E=d.length>0?d[0]:void 0,T=d.length>0?d[d.length-1]:void 0,O=v.find(function(m){return oe(m)}),S=v.slice().reverse().find(function(m){return oe(m)}),R=!!d.find(function(m){return re(m)>0});return{container:u,tabbableNodes:d,focusableNodes:v,posTabIndexesFound:R,firstTabbableNode:E,lastTabbableNode:T,firstDomTabbableNode:O,lastDomTabbableNode:S,nextTabbableNode:function(p){var I=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,F=d.indexOf(p);return F<0?I?v.slice(v.indexOf(p)+1).find(function(z){return oe(z)}):v.slice(0,v.indexOf(p)).reverse().find(function(z){return oe(z)}):d[F+(I?1:-1)]}}}),i.tabbableGroups=i.containerGroups.filter(function(u){return u.tabbableNodes.length>0}),i.tabbableGroups.length<=0&&!f("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times");if(i.containerGroups.find(function(u){return u.posTabIndexesFound})&&i.containerGroups.length>1)throw new Error("At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.")},b=function(u){var d=u.activeElement;if(d)return d.shadowRoot&&d.shadowRoot.activeElement!==null?b(d.shadowRoot):d},y=function(u){if(u!==!1&&u!==b(document)){if(!u||!u.focus){y(g());return}u.focus({preventScroll:!!r.preventScroll}),i.mostRecentlyFocusedNode=u,Cn(u)&&u.select()}},x=function(u){var d=f("setReturnFocus",{params:[u]});return d||(d===!1?!1:u)},w=function(u){var d=u.target,v=u.event,E=u.isBackward,T=E===void 0?!1:E;d=d||Ee(v),h();var O=null;if(i.tabbableGroups.length>0){var S=c(d,v),R=S>=0?i.containerGroups[S]:void 0;if(S<0)T?O=i.tabbableGroups[i.tabbableGroups.length-1].lastTabbableNode:O=i.tabbableGroups[0].firstTabbableNode;else if(T){var m=i.tabbableGroups.findIndex(function(j){var k=j.firstTabbableNode;return d===k});if(m<0&&(R.container===d||Le(d,r.tabbableOptions)&&!oe(d,r.tabbableOptions)&&!R.nextTabbableNode(d,!1))&&(m=S),m>=0){var p=m===0?i.tabbableGroups.length-1:m-1,I=i.tabbableGroups[p];O=re(d)>=0?I.lastTabbableNode:I.lastDomTabbableNode}else ve(v)||(O=R.nextTabbableNode(d,!1))}else{var F=i.tabbableGroups.findIndex(function(j){var k=j.lastTabbableNode;return d===k});if(F<0&&(R.container===d||Le(d,r.tabbableOptions)&&!oe(d,r.tabbableOptions)&&!R.nextTabbableNode(d))&&(F=S),F>=0){var z=F===i.tabbableGroups.length-1?0:F+1,P=i.tabbableGroups[z];O=re(d)>=0?P.firstTabbableNode:P.firstDomTabbableNode}else ve(v)||(O=R.nextTabbableNode(d))}}else O=f("fallbackFocus");return O},C=function(u){var d=Ee(u);if(!(c(d,u)>=0)){if(me(r.clickOutsideDeactivates,u)){o.deactivate({returnFocus:r.returnFocusOnDeactivate});return}me(r.allowOutsideClick,u)||u.preventDefault()}},A=function(u){var d=Ee(u),v=c(d,u)>=0;if(v||d instanceof Document)v&&(i.mostRecentlyFocusedNode=d);else{u.stopImmediatePropagation();var E,T=!0;if(i.mostRecentlyFocusedNode)if(re(i.mostRecentlyFocusedNode)>0){var O=c(i.mostRecentlyFocusedNode),S=i.containerGroups[O].tabbableNodes;if(S.length>0){var R=S.findIndex(function(m){return m===i.mostRecentlyFocusedNode});R>=0&&(r.isKeyForward(i.recentNavEvent)?R+1<S.length&&(E=S[R+1],T=!1):R-1>=0&&(E=S[R-1],T=!1))}}else i.containerGroups.some(function(m){return m.tabbableNodes.some(function(p){return re(p)>0})})||(T=!1);else T=!1;T&&(E=w({target:i.mostRecentlyFocusedNode,isBackward:r.isKeyBackward(i.recentNavEvent)})),y(E||i.mostRecentlyFocusedNode||g())}i.recentNavEvent=void 0},J=function(u){var d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;i.recentNavEvent=u;var v=w({event:u,isBackward:d});v&&(ve(u)&&u.preventDefault(),y(v))},Q=function(u){(r.isKeyForward(u)||r.isKeyBackward(u))&&J(u,r.isKeyBackward(u))},W=function(u){An(u)&&me(r.escapeDeactivates,u)!==!1&&(u.preventDefault(),o.deactivate())},V=function(u){var d=Ee(u);c(d,u)>=0||me(r.clickOutsideDeactivates,u)||me(r.allowOutsideClick,u)||(u.preventDefault(),u.stopImmediatePropagation())},$=function(){if(i.active)return ut.activateTrap(s,o),i.delayInitialFocusTimer=r.delayInitialFocus?dt(function(){y(g())}):y(g()),n.addEventListener("focusin",A,!0),n.addEventListener("mousedown",C,{capture:!0,passive:!1}),n.addEventListener("touchstart",C,{capture:!0,passive:!1}),n.addEventListener("click",V,{capture:!0,passive:!1}),n.addEventListener("keydown",Q,{capture:!0,passive:!1}),n.addEventListener("keydown",W),o},be=function(){if(i.active)return n.removeEventListener("focusin",A,!0),n.removeEventListener("mousedown",C,!0),n.removeEventListener("touchstart",C,!0),n.removeEventListener("click",V,!0),n.removeEventListener("keydown",Q,!0),n.removeEventListener("keydown",W),o},M=function(u){var d=u.some(function(v){var E=Array.from(v.removedNodes);return E.some(function(T){return T===i.mostRecentlyFocusedNode})});d&&y(g())},U=typeof window<"u"&&"MutationObserver"in window?new MutationObserver(M):void 0,q=function(){U&&(U.disconnect(),i.active&&!i.paused&&i.containers.map(function(u){U.observe(u,{subtree:!0,childList:!0})}))};return o={get active(){return i.active},get paused(){return i.paused},activate:function(u){if(i.active)return this;var d=l(u,"onActivate"),v=l(u,"onPostActivate"),E=l(u,"checkCanFocusTrap");E||h(),i.active=!0,i.paused=!1,i.nodeFocusedBeforeActivation=n.activeElement,d==null||d();var T=function(){E&&h(),$(),q(),v==null||v()};return E?(E(i.containers.concat()).then(T,T),this):(T(),this)},deactivate:function(u){if(!i.active)return this;var d=ct({onDeactivate:r.onDeactivate,onPostDeactivate:r.onPostDeactivate,checkCanReturnFocus:r.checkCanReturnFocus},u);clearTimeout(i.delayInitialFocusTimer),i.delayInitialFocusTimer=void 0,be(),i.active=!1,i.paused=!1,q(),ut.deactivateTrap(s,o);var v=l(d,"onDeactivate"),E=l(d,"onPostDeactivate"),T=l(d,"checkCanReturnFocus"),O=l(d,"returnFocus","returnFocusOnDeactivate");v==null||v();var S=function(){dt(function(){O&&y(x(i.nodeFocusedBeforeActivation)),E==null||E()})};return O&&T?(T(x(i.nodeFocusedBeforeActivation)).then(S,S),this):(S(),this)},pause:function(u){return i.active?(i.manuallyPaused=!0,this._setPausedState(!0,u)):this},unpause:function(u){return i.active?(i.manuallyPaused=!1,s[s.length-1]!==this?this:this._setPausedState(!1,u)):this},updateContainerElements:function(u){var d=[].concat(u).filter(Boolean);return i.containers=d.map(function(v){return typeof v=="string"?n.querySelector(v):v}),i.active&&h(),q(),this}},Object.defineProperties(o,{_isManuallyPaused:{value:function(){return i.manuallyPaused}},_setPausedState:{value:function(u,d){if(i.paused===u)return this;if(i.paused=u,u){var v=l(d,"onPause"),E=l(d,"onPostPause");v==null||v(),be(),q(),E==null||E()}else{var T=l(d,"onUnpause"),O=l(d,"onPostUnpause");T==null||T(),h(),$(),q(),O==null||O()}return this}}}),o.updateContainerElements(e),o};function Pn(a,e={}){let t;const{immediate:n,...s}=e,r=ie(!1),i=ie(!1),o=h=>t&&t.activate(h),l=h=>t&&t.deactivate(h),c=()=>{t&&(t.pause(),i.value=!0)},f=()=>{t&&(t.unpause(),i.value=!1)},g=ge(()=>{const h=tt(a);return Ct(h).map(b=>{const y=tt(b);return typeof y=="string"?y:At(y)}).filter(Mt)});return $e(g,h=>{h.length&&(t=zn(h,{...s,onActivate(){r.value=!0,e.onActivate&&e.onActivate()},onDeactivate(){r.value=!1,e.onDeactivate&&e.onDeactivate()}}),n&&o())},{flush:"post"}),Lt(()=>l()),{hasFocus:r,isPaused:i,activate:o,deactivate:l,pause:c,unpause:f}}class ce{constructor(e,t=!0,n=[],s=5e3){this.ctx=e,this.iframes=t,this.exclude=n,this.iframesTimeout=s}static matches(e,t){const n=typeof t=="string"?[t]:t,s=e.matches||e.matchesSelector||e.msMatchesSelector||e.mozMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector;if(s){let r=!1;return n.every(i=>s.call(e,i)?(r=!0,!1):!0),r}else return!1}getContexts(){let e,t=[];return typeof this.ctx>"u"||!this.ctx?e=[]:NodeList.prototype.isPrototypeOf(this.ctx)?e=Array.prototype.slice.call(this.ctx):Array.isArray(this.ctx)?e=this.ctx:typeof this.ctx=="string"?e=Array.prototype.slice.call(document.querySelectorAll(this.ctx)):e=[this.ctx],e.forEach(n=>{const s=t.filter(r=>r.contains(n)).length>0;t.indexOf(n)===-1&&!s&&t.push(n)}),t}getIframeContents(e,t,n=()=>{}){let s;try{const r=e.contentWindow;if(s=r.document,!r||!s)throw new Error("iframe inaccessible")}catch{n()}s&&t(s)}isIframeBlank(e){const t="about:blank",n=e.getAttribute("src").trim();return e.contentWindow.location.href===t&&n!==t&&n}observeIframeLoad(e,t,n){let s=!1,r=null;const i=()=>{if(!s){s=!0,clearTimeout(r);try{this.isIframeBlank(e)||(e.removeEventListener("load",i),this.getIframeContents(e,t,n))}catch{n()}}};e.addEventListener("load",i),r=setTimeout(i,this.iframesTimeout)}onIframeReady(e,t,n){try{e.contentWindow.document.readyState==="complete"?this.isIframeBlank(e)?this.observeIframeLoad(e,t,n):this.getIframeContents(e,t,n):this.observeIframeLoad(e,t,n)}catch{n()}}waitForIframes(e,t){let n=0;this.forEachIframe(e,()=>!0,s=>{n++,this.waitForIframes(s.querySelector("html"),()=>{--n||t()})},s=>{s||t()})}forEachIframe(e,t,n,s=()=>{}){let r=e.querySelectorAll("iframe"),i=r.length,o=0;r=Array.prototype.slice.call(r);const l=()=>{--i<=0&&s(o)};i||l(),r.forEach(c=>{ce.matches(c,this.exclude)?l():this.onIframeReady(c,f=>{t(c)&&(o++,n(f)),l()},l)})}createIterator(e,t,n){return document.createNodeIterator(e,t,n,!1)}createInstanceOnIframe(e){return new ce(e.querySelector("html"),this.iframes)}compareNodeIframe(e,t,n){const s=e.compareDocumentPosition(n),r=Node.DOCUMENT_POSITION_PRECEDING;if(s&r)if(t!==null){const i=t.compareDocumentPosition(n),o=Node.DOCUMENT_POSITION_FOLLOWING;if(i&o)return!0}else return!0;return!1}getIteratorNode(e){const t=e.previousNode();let n;return t===null?n=e.nextNode():n=e.nextNode()&&e.nextNode(),{prevNode:t,node:n}}checkIframeFilter(e,t,n,s){let r=!1,i=!1;return s.forEach((o,l)=>{o.val===n&&(r=l,i=o.handled)}),this.compareNodeIframe(e,t,n)?(r===!1&&!i?s.push({val:n,handled:!0}):r!==!1&&!i&&(s[r].handled=!0),!0):(r===!1&&s.push({val:n,handled:!1}),!1)}handleOpenIframes(e,t,n,s){e.forEach(r=>{r.handled||this.getIframeContents(r.val,i=>{this.createInstanceOnIframe(i).forEachNode(t,n,s)})})}iterateThroughNodes(e,t,n,s,r){const i=this.createIterator(t,e,s);let o=[],l=[],c,f,g=()=>({prevNode:f,node:c}=this.getIteratorNode(i),c);for(;g();)this.iframes&&this.forEachIframe(t,h=>this.checkIframeFilter(c,f,h,o),h=>{this.createInstanceOnIframe(h).forEachNode(e,b=>l.push(b),s)}),l.push(c);l.forEach(h=>{n(h)}),this.iframes&&this.handleOpenIframes(o,e,n,s),r()}forEachNode(e,t,n,s=()=>{}){const r=this.getContexts();let i=r.length;i||s(),r.forEach(o=>{const l=()=>{this.iterateThroughNodes(e,o,t,n,()=>{--i<=0&&s()})};this.iframes?this.waitForIframes(o,l):l()})}}let jn=class{constructor(e){this.ctx=e,this.ie=!1;const t=window.navigator.userAgent;(t.indexOf("MSIE")>-1||t.indexOf("Trident")>-1)&&(this.ie=!0)}set opt(e){this._opt=Object.assign({},{element:"",className:"",exclude:[],iframes:!1,iframesTimeout:5e3,separateWordSearch:!0,diacritics:!0,synonyms:{},accuracy:"partially",acrossElements:!1,caseSensitive:!1,ignoreJoiners:!1,ignoreGroups:0,ignorePunctuation:[],wildcards:"disabled",each:()=>{},noMatch:()=>{},filter:()=>!0,done:()=>{},debug:!1,log:window.console},e)}get opt(){return this._opt}get iterator(){return new ce(this.ctx,this.opt.iframes,this.opt.exclude,this.opt.iframesTimeout)}log(e,t="debug"){const n=this.opt.log;this.opt.debug&&typeof n=="object"&&typeof n[t]=="function"&&n[t](`mark.js: ${e}`)}escapeStr(e){return e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")}createRegExp(e){return this.opt.wildcards!=="disabled"&&(e=this.setupWildcardsRegExp(e)),e=this.escapeStr(e),Object.keys(this.opt.synonyms).length&&(e=this.createSynonymsRegExp(e)),(this.opt.ignoreJoiners||this.opt.ignorePunctuation.length)&&(e=this.setupIgnoreJoinersRegExp(e)),this.opt.diacritics&&(e=this.createDiacriticsRegExp(e)),e=this.createMergedBlanksRegExp(e),(this.opt.ignoreJoiners||this.opt.ignorePunctuation.length)&&(e=this.createJoinersRegExp(e)),this.opt.wildcards!=="disabled"&&(e=this.createWildcardsRegExp(e)),e=this.createAccuracyRegExp(e),e}createSynonymsRegExp(e){const t=this.opt.synonyms,n=this.opt.caseSensitive?"":"i",s=this.opt.ignoreJoiners||this.opt.ignorePunctuation.length?"\0":"";for(let r in t)if(t.hasOwnProperty(r)){const i=t[r],o=this.opt.wildcards!=="disabled"?this.setupWildcardsRegExp(r):this.escapeStr(r),l=this.opt.wildcards!=="disabled"?this.setupWildcardsRegExp(i):this.escapeStr(i);o!==""&&l!==""&&(e=e.replace(new RegExp(`(${this.escapeStr(o)}|${this.escapeStr(l)})`,`gm${n}`),s+`(${this.processSynomyms(o)}|${this.processSynomyms(l)})`+s))}return e}processSynomyms(e){return(this.opt.ignoreJoiners||this.opt.ignorePunctuation.length)&&(e=this.setupIgnoreJoinersRegExp(e)),e}setupWildcardsRegExp(e){return e=e.replace(/(?:\\)*\?/g,t=>t.charAt(0)==="\\"?"?":""),e.replace(/(?:\\)*\*/g,t=>t.charAt(0)==="\\"?"*":"")}createWildcardsRegExp(e){let t=this.opt.wildcards==="withSpaces";return e.replace(/\u0001/g,t?"[\\S\\s]?":"\\S?").replace(/\u0002/g,t?"[\\S\\s]*?":"\\S*")}setupIgnoreJoinersRegExp(e){return e.replace(/[^(|)\\]/g,(t,n,s)=>{let r=s.charAt(n+1);return/[(|)\\]/.test(r)||r===""?t:t+"\0"})}createJoinersRegExp(e){let t=[];const n=this.opt.ignorePunctuation;return Array.isArray(n)&&n.length&&t.push(this.escapeStr(n.join(""))),this.opt.ignoreJoiners&&t.push("\\u00ad\\u200b\\u200c\\u200d"),t.length?e.split(/\u0000+/).join(`[${t.join("")}]*`):e}createDiacriticsRegExp(e){const t=this.opt.caseSensitive?"":"i",n=this.opt.caseSensitive?["aàáảãạăằắẳẵặâầấẩẫậäåāą","AÀÁẢÃẠĂẰẮẲẴẶÂẦẤẨẪẬÄÅĀĄ","cçćč","CÇĆČ","dđď","DĐĎ","eèéẻẽẹêềếểễệëěēę","EÈÉẺẼẸÊỀẾỂỄỆËĚĒĘ","iìíỉĩịîïī","IÌÍỈĨỊÎÏĪ","lł","LŁ","nñňń","NÑŇŃ","oòóỏõọôồốổỗộơởỡớờợöøō","OÒÓỎÕỌÔỒỐỔỖỘƠỞỠỚỜỢÖØŌ","rř","RŘ","sšśșş","SŠŚȘŞ","tťțţ","TŤȚŢ","uùúủũụưừứửữựûüůū","UÙÚỦŨỤƯỪỨỬỮỰÛÜŮŪ","yýỳỷỹỵÿ","YÝỲỶỸỴŸ","zžżź","ZŽŻŹ"]:["aàáảãạăằắẳẵặâầấẩẫậäåāąAÀÁẢÃẠĂẰẮẲẴẶÂẦẤẨẪẬÄÅĀĄ","cçćčCÇĆČ","dđďDĐĎ","eèéẻẽẹêềếểễệëěēęEÈÉẺẼẸÊỀẾỂỄỆËĚĒĘ","iìíỉĩịîïīIÌÍỈĨỊÎÏĪ","lłLŁ","nñňńNÑŇŃ","oòóỏõọôồốổỗộơởỡớờợöøōOÒÓỎÕỌÔỒỐỔỖỘƠỞỠỚỜỢÖØŌ","rřRŘ","sšśșşSŠŚȘŞ","tťțţTŤȚŢ","uùúủũụưừứửữựûüůūUÙÚỦŨỤƯỪỨỬỮỰÛÜŮŪ","yýỳỷỹỵÿYÝỲỶỸỴŸ","zžżźZŽŻŹ"];let s=[];return e.split("").forEach(r=>{n.every(i=>{if(i.indexOf(r)!==-1){if(s.indexOf(i)>-1)return!1;e=e.replace(new RegExp(`[${i}]`,`gm${t}`),`[${i}]`),s.push(i)}return!0})}),e}createMergedBlanksRegExp(e){return e.replace(/[\s]+/gmi,"[\\s]+")}createAccuracyRegExp(e){const t="!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~¡¿";let n=this.opt.accuracy,s=typeof n=="string"?n:n.value,r=typeof n=="string"?[]:n.limiters,i="";switch(r.forEach(o=>{i+=`|${this.escapeStr(o)}`}),s){case"partially":default:return`()(${e})`;case"complementary":return i="\\s"+(i||this.escapeStr(t)),`()([^${i}]*${e}[^${i}]*)`;case"exactly":return`(^|\\s${i})(${e})(?=$|\\s${i})`}}getSeparatedKeywords(e){let t=[];return e.forEach(n=>{this.opt.separateWordSearch?n.split(" ").forEach(s=>{s.trim()&&t.indexOf(s)===-1&&t.push(s)}):n.trim()&&t.indexOf(n)===-1&&t.push(n)}),{keywords:t.sort((n,s)=>s.length-n.length),length:t.length}}isNumeric(e){return Number(parseFloat(e))==e}checkRanges(e){if(!Array.isArray(e)||Object.prototype.toString.call(e[0])!=="[object Object]")return this.log("markRanges() will only accept an array of objects"),this.opt.noMatch(e),[];const t=[];let n=0;return e.sort((s,r)=>s.start-r.start).forEach(s=>{let{start:r,end:i,valid:o}=this.callNoMatchOnInvalidRanges(s,n);o&&(s.start=r,s.length=i-r,t.push(s),n=i)}),t}callNoMatchOnInvalidRanges(e,t){let n,s,r=!1;return e&&typeof e.start<"u"?(n=parseInt(e.start,10),s=n+parseInt(e.length,10),this.isNumeric(e.start)&&this.isNumeric(e.length)&&s-t>0&&s-n>0?r=!0:(this.log(`Ignoring invalid or overlapping range: ${JSON.stringify(e)}`),this.opt.noMatch(e))):(this.log(`Ignoring invalid range: ${JSON.stringify(e)}`),this.opt.noMatch(e)),{start:n,end:s,valid:r}}checkWhitespaceRanges(e,t,n){let s,r=!0,i=n.length,o=t-i,l=parseInt(e.start,10)-o;return l=l>i?i:l,s=l+parseInt(e.length,10),s>i&&(s=i,this.log(`End range automatically set to the max value of ${i}`)),l<0||s-l<0||l>i||s>i?(r=!1,this.log(`Invalid range: ${JSON.stringify(e)}`),this.opt.noMatch(e)):n.substring(l,s).replace(/\s+/g,"")===""&&(r=!1,this.log("Skipping whitespace only range: "+JSON.stringify(e)),this.opt.noMatch(e)),{start:l,end:s,valid:r}}getTextNodes(e){let t="",n=[];this.iterator.forEachNode(NodeFilter.SHOW_TEXT,s=>{n.push({start:t.length,end:(t+=s.textContent).length,node:s})},s=>this.matchesExclude(s.parentNode)?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT,()=>{e({value:t,nodes:n})})}matchesExclude(e){return ce.matches(e,this.opt.exclude.concat(["script","style","title","head","html"]))}wrapRangeInTextNode(e,t,n){const s=this.opt.element?this.opt.element:"mark",r=e.splitText(t),i=r.splitText(n-t);let o=document.createElement(s);return o.setAttribute("data-markjs","true"),this.opt.className&&o.setAttribute("class",this.opt.className),o.textContent=r.textContent,r.parentNode.replaceChild(o,r),i}wrapRangeInMappedTextNode(e,t,n,s,r){e.nodes.every((i,o)=>{const l=e.nodes[o+1];if(typeof l>"u"||l.start>t){if(!s(i.node))return!1;const c=t-i.start,f=(n>i.end?i.end:n)-i.start,g=e.value.substr(0,i.start),h=e.value.substr(f+i.start);if(i.node=this.wrapRangeInTextNode(i.node,c,f),e.value=g+h,e.nodes.forEach((b,y)=>{y>=o&&(e.nodes[y].start>0&&y!==o&&(e.nodes[y].start-=f),e.nodes[y].end-=f)}),n-=f,r(i.node.previousSibling,i.start),n>i.end)t=i.end;else return!1}return!0})}wrapMatches(e,t,n,s,r){const i=t===0?0:t+1;this.getTextNodes(o=>{o.nodes.forEach(l=>{l=l.node;let c;for(;(c=e.exec(l.textContent))!==null&&c[i]!=="";){if(!n(c[i],l))continue;let f=c.index;if(i!==0)for(let g=1;g<i;g++)f+=c[g].length;l=this.wrapRangeInTextNode(l,f,f+c[i].length),s(l.previousSibling),e.lastIndex=0}}),r()})}wrapMatchesAcrossElements(e,t,n,s,r){const i=t===0?0:t+1;this.getTextNodes(o=>{let l;for(;(l=e.exec(o.value))!==null&&l[i]!=="";){let c=l.index;if(i!==0)for(let g=1;g<i;g++)c+=l[g].length;const f=c+l[i].length;this.wrapRangeInMappedTextNode(o,c,f,g=>n(l[i],g),(g,h)=>{e.lastIndex=h,s(g)})}r()})}wrapRangeFromIndex(e,t,n,s){this.getTextNodes(r=>{const i=r.value.length;e.forEach((o,l)=>{let{start:c,end:f,valid:g}=this.checkWhitespaceRanges(o,i,r.value);g&&this.wrapRangeInMappedTextNode(r,c,f,h=>t(h,o,r.value.substring(c,f),l),h=>{n(h,o)})}),s()})}unwrapMatches(e){const t=e.parentNode;let n=document.createDocumentFragment();for(;e.firstChild;)n.appendChild(e.removeChild(e.firstChild));t.replaceChild(n,e),this.ie?this.normalizeTextNode(t):t.normalize()}normalizeTextNode(e){if(e){if(e.nodeType===3)for(;e.nextSibling&&e.nextSibling.nodeType===3;)e.nodeValue+=e.nextSibling.nodeValue,e.parentNode.removeChild(e.nextSibling);else this.normalizeTextNode(e.firstChild);this.normalizeTextNode(e.nextSibling)}}markRegExp(e,t){this.opt=t,this.log(`Searching with expression "${e}"`);let n=0,s="wrapMatches";const r=i=>{n++,this.opt.each(i)};this.opt.acrossElements&&(s="wrapMatchesAcrossElements"),this[s](e,this.opt.ignoreGroups,(i,o)=>this.opt.filter(o,i,n),r,()=>{n===0&&this.opt.noMatch(e),this.opt.done(n)})}mark(e,t){this.opt=t;let n=0,s="wrapMatches";const{keywords:r,length:i}=this.getSeparatedKeywords(typeof e=="string"?[e]:e),o=this.opt.caseSensitive?"":"i",l=c=>{let f=new RegExp(this.createRegExp(c),`gm${o}`),g=0;this.log(`Searching with expression "${f}"`),this[s](f,1,(h,b)=>this.opt.filter(b,c,n,g),h=>{g++,n++,this.opt.each(h)},()=>{g===0&&this.opt.noMatch(c),r[i-1]===c?this.opt.done(n):l(r[r.indexOf(c)+1])})};this.opt.acrossElements&&(s="wrapMatchesAcrossElements"),i===0?this.opt.done(n):l(r[0])}markRanges(e,t){this.opt=t;let n=0,s=this.checkRanges(e);s&&s.length?(this.log("Starting to mark with the following ranges: "+JSON.stringify(s)),this.wrapRangeFromIndex(s,(r,i,o,l)=>this.opt.filter(r,i,o,l),(r,i)=>{n++,this.opt.each(r,i)},()=>{this.opt.done(n)})):this.opt.done(n)}unmark(e){this.opt=e;let t=this.opt.element?this.opt.element:"*";t+="[data-markjs]",this.opt.className&&(t+=`.${this.opt.className}`),this.log(`Removal selector "${t}"`),this.iterator.forEachNode(NodeFilter.SHOW_ELEMENT,n=>{this.unwrapMatches(n)},n=>{const s=ce.matches(n,t),r=this.matchesExclude(n);return!s||r?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT},this.opt.done)}};function Vn(a){const e=new jn(a);return this.mark=(t,n)=>(e.mark(t,n),this),this.markRegExp=(t,n)=>(e.markRegExp(t,n),this),this.markRanges=(t,n)=>(e.markRanges(t,n),this),this.unmark=t=>(e.unmark(t),this),this}function ke(a,e,t,n){function s(r){return r instanceof t?r:new t(function(i){i(r)})}return new(t||(t=Promise))(function(r,i){function o(f){try{c(n.next(f))}catch(g){i(g)}}function l(f){try{c(n.throw(f))}catch(g){i(g)}}function c(f){f.done?r(f.value):s(f.value).then(o,l)}c((n=n.apply(a,[])).next())})}const $n="ENTRIES",_t="KEYS",St="VALUES",D="";class De{constructor(e,t){const n=e._tree,s=Array.from(n.keys());this.set=e,this._type=t,this._path=s.length>0?[{node:n,keys:s}]:[]}next(){const e=this.dive();return this.backtrack(),e}dive(){if(this._path.length===0)return{done:!0,value:void 0};const{node:e,keys:t}=le(this._path);if(le(t)===D)return{done:!1,value:this.result()};const n=e.get(le(t));return this._path.push({node:n,keys:Array.from(n.keys())}),this.dive()}backtrack(){if(this._path.length===0)return;const e=le(this._path).keys;e.pop(),!(e.length>0)&&(this._path.pop(),this.backtrack())}key(){return this.set._prefix+this._path.map(({keys:e})=>le(e)).filter(e=>e!==D).join("")}value(){return le(this._path).node.get(D)}result(){switch(this._type){case St:return this.value();case _t:return this.key();default:return[this.key(),this.value()]}}[Symbol.iterator](){return this}}const le=a=>a[a.length-1],Bn=(a,e,t)=>{const n=new Map;if(e===void 0)return n;const s=e.length+1,r=s+t,i=new Uint8Array(r*s).fill(t+1);for(let o=0;o<s;++o)i[o]=o;for(let o=1;o<r;++o)i[o*s]=o;return Et(a,e,t,n,i,1,s,""),n},Et=(a,e,t,n,s,r,i,o)=>{const l=r*i;e:for(const c of a.keys())if(c===D){const f=s[l-1];f<=t&&n.set(o,[a.get(c),f])}else{let f=r;for(let g=0;g<c.length;++g,++f){const h=c[g],b=i*f,y=b-i;let x=s[b];const w=Math.max(0,f-t-1),C=Math.min(i-1,f+t);for(let A=w;A<C;++A){const J=h!==e[A],Q=s[y+A]+ +J,W=s[y+A+1]+1,V=s[b+A]+1,$=s[b+A+1]=Math.min(Q,W,V);$<x&&(x=$)}if(x>t)continue e}Et(a.get(c),e,t,n,s,f,i,o+c)}};class X{constructor(e=new Map,t=""){this._size=void 0,this._tree=e,this._prefix=t}atPrefix(e){if(!e.startsWith(this._prefix))throw new Error("Mismatched prefix");const[t,n]=Ce(this._tree,e.slice(this._prefix.length));if(t===void 0){const[s,r]=qe(n);for(const i of s.keys())if(i!==D&&i.startsWith(r)){const o=new Map;return o.set(i.slice(r.length),s.get(i)),new X(o,e)}}return new X(t,e)}clear(){this._size=void 0,this._tree.clear()}delete(e){return this._size=void 0,Wn(this._tree,e)}entries(){return new De(this,$n)}forEach(e){for(const[t,n]of this)e(t,n,this)}fuzzyGet(e,t){return Bn(this._tree,e,t)}get(e){const t=Ke(this._tree,e);return t!==void 0?t.get(D):void 0}has(e){const t=Ke(this._tree,e);return t!==void 0&&t.has(D)}keys(){return new De(this,_t)}set(e,t){if(typeof e!="string")throw new Error("key must be a string");return this._size=void 0,ze(this._tree,e).set(D,t),this}get size(){if(this._size)return this._size;this._size=0;const e=this.entries();for(;!e.next().done;)this._size+=1;return this._size}update(e,t){if(typeof e!="string")throw new Error("key must be a string");this._size=void 0;const n=ze(this._tree,e);return n.set(D,t(n.get(D))),this}fetch(e,t){if(typeof e!="string")throw new Error("key must be a string");this._size=void 0;const n=ze(this._tree,e);let s=n.get(D);return s===void 0&&n.set(D,s=t()),s}values(){return new De(this,St)}[Symbol.iterator](){return this.entries()}static from(e){const t=new X;for(const[n,s]of e)t.set(n,s);return t}static fromObject(e){return X.from(Object.entries(e))}}const Ce=(a,e,t=[])=>{if(e.length===0||a==null)return[a,t];for(const n of a.keys())if(n!==D&&e.startsWith(n))return t.push([a,n]),Ce(a.get(n),e.slice(n.length),t);return t.push([a,e]),Ce(void 0,"",t)},Ke=(a,e)=>{if(e.length===0||a==null)return a;for(const t of a.keys())if(t!==D&&e.startsWith(t))return Ke(a.get(t),e.slice(t.length))},ze=(a,e)=>{const t=e.length;e:for(let n=0;a&&n<t;){for(const r of a.keys())if(r!==D&&e[n]===r[0]){const i=Math.min(t-n,r.length);let o=1;for(;o<i&&e[n+o]===r[o];)++o;const l=a.get(r);if(o===r.length)a=l;else{const c=new Map;c.set(r.slice(o),l),a.set(e.slice(n,n+o),c),a.delete(r),a=c}n+=o;continue e}const s=new Map;return a.set(e.slice(n),s),s}return a},Wn=(a,e)=>{const[t,n]=Ce(a,e);if(t!==void 0){if(t.delete(D),t.size===0)Tt(n);else if(t.size===1){const[s,r]=t.entries().next().value;It(n,s,r)}}},Tt=a=>{if(a.length===0)return;const[e,t]=qe(a);if(e.delete(t),e.size===0)Tt(a.slice(0,-1));else if(e.size===1){const[n,s]=e.entries().next().value;n!==D&&It(a.slice(0,-1),n,s)}},It=(a,e,t)=>{if(a.length===0)return;const[n,s]=qe(a);n.set(s+e,t),n.delete(s)},qe=a=>a[a.length-1],Ge="or",kt="and",Kn="and_not";class ue{constructor(e){if((e==null?void 0:e.fields)==null)throw new Error('MiniSearch: option "fields" must be provided');const t=e.autoVacuum==null||e.autoVacuum===!0?Ve:e.autoVacuum;this._options=Object.assign(Object.assign(Object.assign({},je),e),{autoVacuum:t,searchOptions:Object.assign(Object.assign({},ft),e.searchOptions||{}),autoSuggestOptions:Object.assign(Object.assign({},Hn),e.autoSuggestOptions||{})}),this._index=new X,this._documentCount=0,this._documentIds=new Map,this._idToShortId=new Map,this._fieldIds={},this._fieldLength=new Map,this._avgFieldLength=[],this._nextId=0,this._storedFields=new Map,this._dirtCount=0,this._currentVacuum=null,this._enqueuedVacuum=null,this._enqueuedVacuumConditions=Ue,this.addFields(this._options.fields)}add(e){const{extractField:t,tokenize:n,processTerm:s,fields:r,idField:i}=this._options,o=t(e,i);if(o==null)throw new Error(`MiniSearch: document does not have ID field "${i}"`);if(this._idToShortId.has(o))throw new Error(`MiniSearch: duplicate ID ${o}`);const l=this.addDocumentId(o);this.saveStoredFields(l,e);for(const c of r){const f=t(e,c);if(f==null)continue;const g=n(f.toString(),c),h=this._fieldIds[c],b=new Set(g).size;this.addFieldLength(l,h,this._documentCount-1,b);for(const y of g){const x=s(y,c);if(Array.isArray(x))for(const w of x)this.addTerm(h,l,w);else x&&this.addTerm(h,l,x)}}}addAll(e){for(const t of e)this.add(t)}addAllAsync(e,t={}){const{chunkSize:n=10}=t,s={chunk:[],promise:Promise.resolve()},{chunk:r,promise:i}=e.reduce(({chunk:o,promise:l},c,f)=>(o.push(c),(f+1)%n===0?{chunk:[],promise:l.then(()=>new Promise(g=>setTimeout(g,0))).then(()=>this.addAll(o))}:{chunk:o,promise:l}),s);return i.then(()=>this.addAll(r))}remove(e){const{tokenize:t,processTerm:n,extractField:s,fields:r,idField:i}=this._options,o=s(e,i);if(o==null)throw new Error(`MiniSearch: document does not have ID field "${i}"`);const l=this._idToShortId.get(o);if(l==null)throw new Error(`MiniSearch: cannot remove document with ID ${o}: it is not in the index`);for(const c of r){const f=s(e,c);if(f==null)continue;const g=t(f.toString(),c),h=this._fieldIds[c],b=new Set(g).size;this.removeFieldLength(l,h,this._documentCount,b);for(const y of g){const x=n(y,c);if(Array.isArray(x))for(const w of x)this.removeTerm(h,l,w);else x&&this.removeTerm(h,l,x)}}this._storedFields.delete(l),this._documentIds.delete(l),this._idToShortId.delete(o),this._fieldLength.delete(l),this._documentCount-=1}removeAll(e){if(e)for(const t of e)this.remove(t);else{if(arguments.length>0)throw new Error("Expected documents to be present. Omit the argument to remove all documents.");this._index=new X,this._documentCount=0,this._documentIds=new Map,this._idToShortId=new Map,this._fieldLength=new Map,this._avgFieldLength=[],this._storedFields=new Map,this._nextId=0}}discard(e){const t=this._idToShortId.get(e);if(t==null)throw new Error(`MiniSearch: cannot discard document with ID ${e}: it is not in the index`);this._idToShortId.delete(e),this._documentIds.delete(t),this._storedFields.delete(t),(this._fieldLength.get(t)||[]).forEach((n,s)=>{this.removeFieldLength(t,s,this._documentCount,n)}),this._fieldLength.delete(t),this._documentCount-=1,this._dirtCount+=1,this.maybeAutoVacuum()}maybeAutoVacuum(){if(this._options.autoVacuum===!1)return;const{minDirtFactor:e,minDirtCount:t,batchSize:n,batchWait:s}=this._options.autoVacuum;this.conditionalVacuum({batchSize:n,batchWait:s},{minDirtCount:t,minDirtFactor:e})}discardAll(e){const t=this._options.autoVacuum;try{this._options.autoVacuum=!1;for(const n of e)this.discard(n)}finally{this._options.autoVacuum=t}this.maybeAutoVacuum()}replace(e){const{idField:t,extractField:n}=this._options,s=n(e,t);this.discard(s),this.add(e)}vacuum(e={}){return this.conditionalVacuum(e)}conditionalVacuum(e,t){return this._currentVacuum?(this._enqueuedVacuumConditions=this._enqueuedVacuumConditions&&t,this._enqueuedVacuum!=null?this._enqueuedVacuum:(this._enqueuedVacuum=this._currentVacuum.then(()=>{const n=this._enqueuedVacuumConditions;return this._enqueuedVacuumConditions=Ue,this.performVacuuming(e,n)}),this._enqueuedVacuum)):this.vacuumConditionsMet(t)===!1?Promise.resolve():(this._currentVacuum=this.performVacuuming(e),this._currentVacuum)}performVacuuming(e,t){return ke(this,void 0,void 0,function*(){const n=this._dirtCount;if(this.vacuumConditionsMet(t)){const s=e.batchSize||Je.batchSize,r=e.batchWait||Je.batchWait;let i=1;for(const[o,l]of this._index){for(const[c,f]of l)for(const[g]of f)this._documentIds.has(g)||(f.size<=1?l.delete(c):f.delete(g));this._index.get(o).size===0&&this._index.delete(o),i%s===0&&(yield new Promise(c=>setTimeout(c,r))),i+=1}this._dirtCount-=n}yield null,this._currentVacuum=this._enqueuedVacuum,this._enqueuedVacuum=null})}vacuumConditionsMet(e){if(e==null)return!0;let{minDirtCount:t,minDirtFactor:n}=e;return t=t||Ve.minDirtCount,n=n||Ve.minDirtFactor,this.dirtCount>=t&&this.dirtFactor>=n}get isVacuuming(){return this._currentVacuum!=null}get dirtCount(){return this._dirtCount}get dirtFactor(){return this._dirtCount/(1+this._documentCount+this._dirtCount)}has(e){return this._idToShortId.has(e)}getStoredFields(e){const t=this._idToShortId.get(e);if(t!=null)return this._storedFields.get(t)}search(e,t={}){const{searchOptions:n}=this._options,s=Object.assign(Object.assign({},n),t),r=this.executeQuery(e,t),i=[];for(const[o,{score:l,terms:c,match:f}]of r){const g=c.length||1,h={id:this._documentIds.get(o),score:l*g,terms:Object.keys(f),queryTerms:c,match:f};Object.assign(h,this._storedFields.get(o)),(s.filter==null||s.filter(h))&&i.push(h)}return e===ue.wildcard&&s.boostDocument==null||i.sort(pt),i}autoSuggest(e,t={}){t=Object.assign(Object.assign({},this._options.autoSuggestOptions),t);const n=new Map;for(const{score:r,terms:i}of this.search(e,t)){const o=i.join(" "),l=n.get(o);l!=null?(l.score+=r,l.count+=1):n.set(o,{score:r,terms:i,count:1})}const s=[];for(const[r,{score:i,terms:o,count:l}]of n)s.push({suggestion:r,terms:o,score:i/l});return s.sort(pt),s}get documentCount(){return this._documentCount}get termCount(){return this._index.size}static loadJSON(e,t){if(t==null)throw new Error("MiniSearch: loadJSON should be given the same options used when serializing the index");return this.loadJS(JSON.parse(e),t)}static loadJSONAsync(e,t){return ke(this,void 0,void 0,function*(){if(t==null)throw new Error("MiniSearch: loadJSON should be given the same options used when serializing the index");return this.loadJSAsync(JSON.parse(e),t)})}static getDefault(e){if(je.hasOwnProperty(e))return Pe(je,e);throw new Error(`MiniSearch: unknown option "${e}"`)}static loadJS(e,t){const{index:n,documentIds:s,fieldLength:r,storedFields:i,serializationVersion:o}=e,l=this.instantiateMiniSearch(e,t);l._documentIds=Te(s),l._fieldLength=Te(r),l._storedFields=Te(i);for(const[c,f]of l._documentIds)l._idToShortId.set(f,c);for(const[c,f]of n){const g=new Map;for(const h of Object.keys(f)){let b=f[h];o===1&&(b=b.ds),g.set(parseInt(h,10),Te(b))}l._index.set(c,g)}return l}static loadJSAsync(e,t){return ke(this,void 0,void 0,function*(){const{index:n,documentIds:s,fieldLength:r,storedFields:i,serializationVersion:o}=e,l=this.instantiateMiniSearch(e,t);l._documentIds=yield Ie(s),l._fieldLength=yield Ie(r),l._storedFields=yield Ie(i);for(const[f,g]of l._documentIds)l._idToShortId.set(g,f);let c=0;for(const[f,g]of n){const h=new Map;for(const b of Object.keys(g)){let y=g[b];o===1&&(y=y.ds),h.set(parseInt(b,10),yield Ie(y))}++c%1e3===0&&(yield Nt(0)),l._index.set(f,h)}return l})}static instantiateMiniSearch(e,t){const{documentCount:n,nextId:s,fieldIds:r,averageFieldLength:i,dirtCount:o,serializationVersion:l}=e;if(l!==1&&l!==2)throw new Error("MiniSearch: cannot deserialize an index created with an incompatible version");const c=new ue(t);return c._documentCount=n,c._nextId=s,c._idToShortId=new Map,c._fieldIds=r,c._avgFieldLength=i,c._dirtCount=o||0,c._index=new X,c}executeQuery(e,t={}){if(e===ue.wildcard)return this.executeWildcardQuery(t);if(typeof e!="string"){const h=Object.assign(Object.assign(Object.assign({},t),e),{queries:void 0}),b=e.queries.map(y=>this.executeQuery(y,h));return this.combineResults(b,h.combineWith)}const{tokenize:n,processTerm:s,searchOptions:r}=this._options,i=Object.assign(Object.assign({tokenize:n,processTerm:s},r),t),{tokenize:o,processTerm:l}=i,g=o(e).flatMap(h=>l(h)).filter(h=>!!h).map(Gn(i)).map(h=>this.executeQuerySpec(h,i));return this.combineResults(g,i.combineWith)}executeQuerySpec(e,t){const n=Object.assign(Object.assign({},this._options.searchOptions),t),s=(n.fields||this._options.fields).reduce((x,w)=>Object.assign(Object.assign({},x),{[w]:Pe(n.boost,w)||1}),{}),{boostDocument:r,weights:i,maxFuzzy:o,bm25:l}=n,{fuzzy:c,prefix:f}=Object.assign(Object.assign({},ft.weights),i),g=this._index.get(e.term),h=this.termResults(e.term,e.term,1,e.termBoost,g,s,r,l);let b,y;if(e.prefix&&(b=this._index.atPrefix(e.term)),e.fuzzy){const x=e.fuzzy===!0?.2:e.fuzzy,w=x<1?Math.min(o,Math.round(e.term.length*x)):x;w&&(y=this._index.fuzzyGet(e.term,w))}if(b)for(const[x,w]of b){const C=x.length-e.term.length;if(!C)continue;y==null||y.delete(x);const A=f*x.length/(x.length+.3*C);this.termResults(e.term,x,A,e.termBoost,w,s,r,l,h)}if(y)for(const x of y.keys()){const[w,C]=y.get(x);if(!C)continue;const A=c*x.length/(x.length+C);this.termResults(e.term,x,A,e.termBoost,w,s,r,l,h)}return h}executeWildcardQuery(e){const t=new Map,n=Object.assign(Object.assign({},this._options.searchOptions),e);for(const[s,r]of this._documentIds){const i=n.boostDocument?n.boostDocument(r,"",this._storedFields.get(s)):1;t.set(s,{score:i,terms:[],match:{}})}return t}combineResults(e,t=Ge){if(e.length===0)return new Map;const n=t.toLowerCase(),s=Jn[n];if(!s)throw new Error(`Invalid combination operator: ${t}`);return e.reduce(s)||new Map}toJSON(){const e=[];for(const[t,n]of this._index){const s={};for(const[r,i]of n)s[r]=Object.fromEntries(i);e.push([t,s])}return{documentCount:this._documentCount,nextId:this._nextId,documentIds:Object.fromEntries(this._documentIds),fieldIds:this._fieldIds,fieldLength:Object.fromEntries(this._fieldLength),averageFieldLength:this._avgFieldLength,storedFields:Object.fromEntries(this._storedFields),dirtCount:this._dirtCount,index:e,serializationVersion:2}}termResults(e,t,n,s,r,i,o,l,c=new Map){if(r==null)return c;for(const f of Object.keys(i)){const g=i[f],h=this._fieldIds[f],b=r.get(h);if(b==null)continue;let y=b.size;const x=this._avgFieldLength[h];for(const w of b.keys()){if(!this._documentIds.has(w)){this.removeTerm(h,w,t),y-=1;continue}const C=o?o(this._documentIds.get(w),t,this._storedFields.get(w)):1;if(!C)continue;const A=b.get(w),J=this._fieldLength.get(w)[h],Q=qn(A,y,this._documentCount,J,x,l),W=n*s*g*C*Q,V=c.get(w);if(V){V.score+=W,Qn(V.terms,e);const $=Pe(V.match,t);$?$.push(f):V.match[t]=[f]}else c.set(w,{score:W,terms:[e],match:{[t]:[f]}})}}return c}addTerm(e,t,n){const s=this._index.fetch(n,mt);let r=s.get(e);if(r==null)r=new Map,r.set(t,1),s.set(e,r);else{const i=r.get(t);r.set(t,(i||0)+1)}}removeTerm(e,t,n){if(!this._index.has(n)){this.warnDocumentChanged(t,e,n);return}const s=this._index.fetch(n,mt),r=s.get(e);r==null||r.get(t)==null?this.warnDocumentChanged(t,e,n):r.get(t)<=1?r.size<=1?s.delete(e):r.delete(t):r.set(t,r.get(t)-1),this._index.get(n).size===0&&this._index.delete(n)}warnDocumentChanged(e,t,n){for(const s of Object.keys(this._fieldIds))if(this._fieldIds[s]===t){this._options.logger("warn",`MiniSearch: document with ID ${this._documentIds.get(e)} has changed before removal: term "${n}" was not present in field "${s}". Removing a document after it has changed can corrupt the index!`,"version_conflict");return}}addDocumentId(e){const t=this._nextId;return this._idToShortId.set(e,t),this._documentIds.set(t,e),this._documentCount+=1,this._nextId+=1,t}addFields(e){for(let t=0;t<e.length;t++)this._fieldIds[e[t]]=t}addFieldLength(e,t,n,s){let r=this._fieldLength.get(e);r==null&&this._fieldLength.set(e,r=[]),r[t]=s;const o=(this._avgFieldLength[t]||0)*n+s;this._avgFieldLength[t]=o/(n+1)}removeFieldLength(e,t,n,s){if(n===1){this._avgFieldLength[t]=0;return}const r=this._avgFieldLength[t]*n-s;this._avgFieldLength[t]=r/(n-1)}saveStoredFields(e,t){const{storeFields:n,extractField:s}=this._options;if(n==null||n.length===0)return;let r=this._storedFields.get(e);r==null&&this._storedFields.set(e,r={});for(const i of n){const o=s(t,i);o!==void 0&&(r[i]=o)}}}ue.wildcard=Symbol("*");const Pe=(a,e)=>Object.prototype.hasOwnProperty.call(a,e)?a[e]:void 0,Jn={[Ge]:(a,e)=>{for(const t of e.keys()){const n=a.get(t);if(n==null)a.set(t,e.get(t));else{const{score:s,terms:r,match:i}=e.get(t);n.score=n.score+s,n.match=Object.assign(n.match,i),ht(n.terms,r)}}return a},[kt]:(a,e)=>{const t=new Map;for(const n of e.keys()){const s=a.get(n);if(s==null)continue;const{score:r,terms:i,match:o}=e.get(n);ht(s.terms,i),t.set(n,{score:s.score+r,terms:s.terms,match:Object.assign(s.match,o)})}return t},[Kn]:(a,e)=>{for(const t of e.keys())a.delete(t);return a}},Un={k:1.2,b:.7,d:.5},qn=(a,e,t,n,s,r)=>{const{k:i,b:o,d:l}=r;return Math.log(1+(t-e+.5)/(e+.5))*(l+a*(i+1)/(a+i*(1-o+o*n/s)))},Gn=a=>(e,t,n)=>{const s=typeof a.fuzzy=="function"?a.fuzzy(e,t,n):a.fuzzy||!1,r=typeof a.prefix=="function"?a.prefix(e,t,n):a.prefix===!0,i=typeof a.boostTerm=="function"?a.boostTerm(e,t,n):1;return{term:e,fuzzy:s,prefix:r,termBoost:i}},je={idField:"id",extractField:(a,e)=>a[e],tokenize:a=>a.split(Yn),processTerm:a=>a.toLowerCase(),fields:void 0,searchOptions:void 0,storeFields:[],logger:(a,e)=>{typeof(console==null?void 0:console[a])=="function"&&console[a](e)},autoVacuum:!0},ft={combineWith:Ge,prefix:!1,fuzzy:!1,maxFuzzy:6,boost:{},weights:{fuzzy:.45,prefix:.375},bm25:Un},Hn={combineWith:kt,prefix:(a,e,t)=>e===t.length-1},Je={batchSize:1e3,batchWait:10},Ue={minDirtFactor:.1,minDirtCount:20},Ve=Object.assign(Object.assign({},Je),Ue),Qn=(a,e)=>{a.includes(e)||a.push(e)},ht=(a,e)=>{for(const t of e)a.includes(t)||a.push(t)},pt=({score:a},{score:e})=>e-a,mt=()=>new Map,Te=a=>{const e=new Map;for(const t of Object.keys(a))e.set(parseInt(t,10),a[t]);return e},Ie=a=>ke(void 0,void 0,void 0,function*(){const e=new Map;let t=0;for(const n of Object.keys(a))e.set(parseInt(n,10),a[n]),++t%1e3===0&&(yield Nt(0));return e}),Nt=a=>new Promise(e=>setTimeout(e,a)),Yn=/[\n\r\p{Z}\p{P}]+/u;class Zn{constructor(e=10){Ae(this,"max");Ae(this,"cache");this.max=e,this.cache=new Map}get(e){let t=this.cache.get(e);return t!==void 0&&(this.cache.delete(e),this.cache.set(e,t)),t}set(e,t){this.cache.has(e)?this.cache.delete(e):this.cache.size===this.max&&this.cache.delete(this.first()),this.cache.set(e,t)}first(){return this.cache.keys().next().value}clear(){this.cache.clear()}}const Xn=["aria-owns"],es={class:"shell"},ts=["title"],ns={class:"search-actions before"},ss=["title"],is=["aria-activedescendant","aria-controls","placeholder"],rs={class:"search-actions"},as=["title"],os=["disabled","title"],ls=["id","role","aria-labelledby"],cs=["id","aria-selected"],us=["href","aria-label","onMouseenter","onFocusin","data-index"],ds={class:"titles"},fs=["innerHTML"],hs={class:"title main"},ps=["innerHTML"],ms={key:0,class:"excerpt-wrapper"},gs={key:0,class:"excerpt",inert:""},vs=["innerHTML"],bs={key:0,class:"no-results"},ys={class:"search-keyboard-shortcuts"},ws=["aria-label"],xs=["aria-label"],_s=["aria-label"],Ss=["aria-label"],Es=Dt({__name:"VPLocalSearchBox",emits:["close"],setup(a,{emit:e}){var S,R;const t=e,n=xe(),s=xe(),r=xe(an),i=sn(),{activate:o}=Pn(n,{immediate:!0,allowOutsideClick:!0,clickOutsideDeactivates:!0,escapeDeactivates:!0}),{localeIndex:l,theme:c}=i,f=nt(async()=>{var m,p,I,F,z,P,j,k,K;return at(ue.loadJSON((I=await((p=(m=r.value)[l.value])==null?void 0:p.call(m)))==null?void 0:I.default,{fields:["title","titles","text"],storeFields:["title","titles"],searchOptions:{fuzzy:.2,prefix:!0,boost:{title:4,text:2,titles:1},...((F=c.value.search)==null?void 0:F.provider)==="local"&&((P=(z=c.value.search.options)==null?void 0:z.miniSearch)==null?void 0:P.searchOptions)},...((j=c.value.search)==null?void 0:j.provider)==="local"&&((K=(k=c.value.search.options)==null?void 0:k.miniSearch)==null?void 0:K.options)}))}),h=ge(()=>{var m,p;return((m=c.value.search)==null?void 0:m.provider)==="local"&&((p=c.value.search.options)==null?void 0:p.disableQueryPersistence)===!0}).value?ie(""):zt("vitepress:local-search-filter",""),b=Pt("vitepress:local-search-detailed-list",((S=c.value.search)==null?void 0:S.provider)==="local"&&((R=c.value.search.options)==null?void 0:R.detailedView)===!0),y=ge(()=>{var m,p,I;return((m=c.value.search)==null?void 0:m.provider)==="local"&&(((p=c.value.search.options)==null?void 0:p.disableDetailedView)===!0||((I=c.value.search.options)==null?void 0:I.detailedView)===!1)}),x=ge(()=>{var p,I,F,z,P,j,k;const m=((p=c.value.search)==null?void 0:p.options)??c.value.algolia;return((P=(z=(F=(I=m==null?void 0:m.locales)==null?void 0:I[l.value])==null?void 0:F.translations)==null?void 0:z.button)==null?void 0:P.buttonText)||((k=(j=m==null?void 0:m.translations)==null?void 0:j.button)==null?void 0:k.buttonText)||"Search"});jt(()=>{y.value&&(b.value=!1)});const w=xe([]),C=ie(!1);$e(h,()=>{C.value=!1});const A=nt(async()=>{if(s.value)return at(new Vn(s.value))},null),J=new Zn(16);Vt(()=>[f.value,h.value,b.value],async([m,p,I],F,z)=>{var ee,ye,He,Qe;(F==null?void 0:F[0])!==m&&J.clear();let P=!1;if(z(()=>{P=!0}),!m)return;w.value=m.search(p).slice(0,16),C.value=!0;const j=I?await Promise.all(w.value.map(B=>Q(B.id))):[];if(P)return;for(const{id:B,mod:te}of j){const ne=B.slice(0,B.indexOf("#"));let Y=J.get(ne);if(Y)continue;Y=new Map,J.set(ne,Y);const G=te.default??te;if(G!=null&&G.render||G!=null&&G.setup){const se=Zt(G);se.config.warnHandler=()=>{},se.provide(Xt,i),Object.defineProperties(se.config.globalProperties,{$frontmatter:{get(){return i.frontmatter.value}},$params:{get(){return i.page.value.params}}});const Ye=document.createElement("div");se.mount(Ye),Ye.querySelectorAll("h1, h2, h3, h4, h5, h6").forEach(de=>{var et;const we=(et=de.querySelector("a"))==null?void 0:et.getAttribute("href"),Ze=(we==null?void 0:we.startsWith("#"))&&we.slice(1);if(!Ze)return;let Xe="";for(;(de=de.nextElementSibling)&&!/^h[1-6]$/i.test(de.tagName);)Xe+=de.outerHTML;Y.set(Ze,Xe)}),se.unmount()}if(P)return}const k=new Set;if(w.value=w.value.map(B=>{const[te,ne]=B.id.split("#"),Y=J.get(te),G=(Y==null?void 0:Y.get(ne))??"";for(const se in B.match)k.add(se);return{...B,text:G}}),await fe(),P)return;await new Promise(B=>{var te;(te=A.value)==null||te.unmark({done:()=>{var ne;(ne=A.value)==null||ne.markRegExp(T(k),{done:B})}})});const K=((ee=n.value)==null?void 0:ee.querySelectorAll(".result .excerpt"))??[];for(const B of K)(ye=B.querySelector('mark[data-markjs="true"]'))==null||ye.scrollIntoView({block:"center"});(Qe=(He=s.value)==null?void 0:He.firstElementChild)==null||Qe.scrollIntoView({block:"start"})},{debounce:200,immediate:!0});async function Q(m){const p=en(m.slice(0,m.indexOf("#")));try{if(!p)throw new Error(`Cannot find file for id: ${m}`);return{id:m,mod:await import(p)}}catch(I){return console.error(I),{id:m,mod:{}}}}const W=ie(),V=ge(()=>{var m;return((m=h.value)==null?void 0:m.length)<=0});function $(m=!0){var p,I;(p=W.value)==null||p.focus(),m&&((I=W.value)==null||I.select())}Me(()=>{$()});function be(m){m.pointerType==="mouse"&&$()}const M=ie(-1),U=ie(!0);$e(w,m=>{M.value=m.length?0:-1,q()});function q(){fe(()=>{const m=document.querySelector(".result.selected");m==null||m.scrollIntoView({block:"nearest"})})}_e("ArrowUp",m=>{m.preventDefault(),M.value--,M.value<0&&(M.value=w.value.length-1),U.value=!0,q()}),_e("ArrowDown",m=>{m.preventDefault(),M.value++,M.value>=w.value.length&&(M.value=0),U.value=!0,q()});const N=$t();_e("Enter",m=>{if(m.isComposing||m.target instanceof HTMLButtonElement&&m.target.type!=="submit")return;const p=w.value[M.value];if(m.target instanceof HTMLInputElement&&!p){m.preventDefault();return}p&&(N.go(p.id),t("close"))}),_e("Escape",()=>{t("close")});const d=rn({modal:{displayDetails:"Display detailed list",resetButtonTitle:"Reset search",backButtonTitle:"Close search",noResultsText:"No results for",footer:{selectText:"to select",selectKeyAriaLabel:"enter",navigateText:"to navigate",navigateUpKeyAriaLabel:"up arrow",navigateDownKeyAriaLabel:"down arrow",closeText:"to close",closeKeyAriaLabel:"escape"}}});Me(()=>{window.history.pushState(null,"",null)}),Bt("popstate",m=>{m.preventDefault(),t("close")});const v=Wt(Yt?document.body:null);Me(()=>{fe(()=>{v.value=!0,fe().then(()=>o())})}),Kt(()=>{v.value=!1});function E(){h.value="",fe().then(()=>$(!1))}function T(m){return new RegExp([...m].sort((p,I)=>I.length-p.length).map(p=>`(${tn(p)})`).join("|"),"gi")}function O(m){var F;if(!U.value)return;const p=(F=m.target)==null?void 0:F.closest(".result"),I=Number.parseInt(p==null?void 0:p.dataset.index);I>=0&&I!==M.value&&(M.value=I),U.value=!1}return(m,p)=>{var I,F,z,P,j;return H(),Jt(Qt,{to:"body"},[_("div",{ref_key:"el",ref:n,role:"button","aria-owns":(I=w.value)!=null&&I.length?"localsearch-list":void 0,"aria-expanded":"true","aria-haspopup":"listbox","aria-labelledby":"localsearch-label",class:"VPLocalSearchBox"},[_("div",{class:"backdrop",onClick:p[0]||(p[0]=k=>m.$emit("close"))}),_("div",es,[_("form",{class:"search-bar",onPointerup:p[4]||(p[4]=k=>be(k)),onSubmit:p[5]||(p[5]=Ut(()=>{},["prevent"]))},[_("label",{title:x.value,id:"localsearch-label",for:"localsearch-input"},p[7]||(p[7]=[_("span",{"aria-hidden":"true",class:"vpi-search search-icon local-search-icon"},null,-1)]),8,ts),_("div",ns,[_("button",{class:"back-button",title:L(d)("modal.backButtonTitle"),onClick:p[1]||(p[1]=k=>m.$emit("close"))},p[8]||(p[8]=[_("span",{class:"vpi-arrow-left local-search-icon"},null,-1)]),8,ss)]),qt(_("input",{ref_key:"searchInput",ref:W,"onUpdate:modelValue":p[2]||(p[2]=k=>Ht(h)?h.value=k:null),"aria-activedescendant":M.value>-1?"localsearch-item-"+M.value:void 0,"aria-autocomplete":"both","aria-controls":(F=w.value)!=null&&F.length?"localsearch-list":void 0,"aria-labelledby":"localsearch-label",autocapitalize:"off",autocomplete:"off",autocorrect:"off",class:"search-input",id:"localsearch-input",enterkeyhint:"go",maxlength:"64",placeholder:x.value,spellcheck:"false",type:"search"},null,8,is),[[Gt,L(h)]]),_("div",rs,[y.value?Se("",!0):(H(),Z("button",{key:0,class:st(["toggle-layout-button",{"detailed-list":L(b)}]),type:"button",title:L(d)("modal.displayDetails"),onClick:p[3]||(p[3]=k=>M.value>-1&&(b.value=!L(b)))},p[9]||(p[9]=[_("span",{class:"vpi-layout-list local-search-icon"},null,-1)]),10,as)),_("button",{class:"clear-button",type:"reset",disabled:V.value,title:L(d)("modal.resetButtonTitle"),onClick:E},p[10]||(p[10]=[_("span",{class:"vpi-delete local-search-icon"},null,-1)]),8,os)])],32),_("ul",{ref_key:"resultsEl",ref:s,id:(z=w.value)!=null&&z.length?"localsearch-list":void 0,role:(P=w.value)!=null&&P.length?"listbox":void 0,"aria-labelledby":(j=w.value)!=null&&j.length?"localsearch-label":void 0,class:"results",onMousemove:O},[(H(!0),Z(rt,null,it(w.value,(k,K)=>(H(),Z("li",{key:k.id,id:"localsearch-item-"+K,"aria-selected":M.value===K?"true":"false",role:"option"},[_("a",{href:k.id,class:st(["result",{selected:M.value===K}]),"aria-label":[...k.titles,k.title].join(" > "),onMouseenter:ee=>!U.value&&(M.value=K),onFocusin:ee=>M.value=K,onClick:p[6]||(p[6]=ee=>m.$emit("close")),"data-index":K},[_("div",null,[_("div",ds,[p[12]||(p[12]=_("span",{class:"title-icon"},"#",-1)),(H(!0),Z(rt,null,it(k.titles,(ee,ye)=>(H(),Z("span",{key:ye,class:"title"},[_("span",{class:"text",innerHTML:ee},null,8,fs),p[11]||(p[11]=_("span",{class:"vpi-chevron-right local-search-icon"},null,-1))]))),128)),_("span",hs,[_("span",{class:"text",innerHTML:k.title},null,8,ps)])]),L(b)?(H(),Z("div",ms,[k.text?(H(),Z("div",gs,[_("div",{class:"vp-doc",innerHTML:k.text},null,8,vs)])):Se("",!0),p[13]||(p[13]=_("div",{class:"excerpt-gradient-bottom"},null,-1)),p[14]||(p[14]=_("div",{class:"excerpt-gradient-top"},null,-1))])):Se("",!0)])],42,us)],8,cs))),128)),L(h)&&!w.value.length&&C.value?(H(),Z("li",bs,[he(pe(L(d)("modal.noResultsText"))+' "',1),_("strong",null,pe(L(h)),1),p[15]||(p[15]=he('" '))])):Se("",!0)],40,ls),_("div",ys,[_("span",null,[_("kbd",{"aria-label":L(d)("modal.footer.navigateUpKeyAriaLabel")},p[16]||(p[16]=[_("span",{class:"vpi-arrow-up navigate-icon"},null,-1)]),8,ws),_("kbd",{"aria-label":L(d)("modal.footer.navigateDownKeyAriaLabel")},p[17]||(p[17]=[_("span",{class:"vpi-arrow-down navigate-icon"},null,-1)]),8,xs),he(" "+pe(L(d)("modal.footer.navigateText")),1)]),_("span",null,[_("kbd",{"aria-label":L(d)("modal.footer.selectKeyAriaLabel")},p[18]||(p[18]=[_("span",{class:"vpi-corner-down-left navigate-icon"},null,-1)]),8,_s),he(" "+pe(L(d)("modal.footer.selectText")),1)]),_("span",null,[_("kbd",{"aria-label":L(d)("modal.footer.closeKeyAriaLabel")},"esc",8,Ss),he(" "+pe(L(d)("modal.footer.closeText")),1)])])])],8,Xn)])}}}),Fs=nn(Es,[["__scopeId","data-v-c4c8a6b3"]]);export{Fs as default};
