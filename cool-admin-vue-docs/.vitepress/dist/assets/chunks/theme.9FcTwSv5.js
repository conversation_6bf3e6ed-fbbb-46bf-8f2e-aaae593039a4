const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/chunks/VPLocalSearchBox.BMPQPFwl.js","assets/chunks/framework.BMq9nYrq.js","assets/chunks/index.BjsZv4Lw.js","assets/chunks/index.CSHP5FlZ.js","assets/chunks/index.umd.min.DvzFVQw3.js"])))=>i.map(i=>d[i]);
import{d as _,c as g,o as s,T as De,w as f,a as c,b as h,_ as y,u as kt,i as yt,t as $t,e as Ie,f as E,g as m,h as A,j as i,k as ie,l as we,r as N,m as Q,n as be,p as x,q as Me,s as He,v as Et,x as Lt,y as ee,z as D,F as M,A as G,B as Ze,C as ke,D as b,E as u,G as Qe,H as X,I as se,J as de,K as ae,L as ye,M as Pt,N as xe,O as Ce,P as ze,Q as et,R as $e,S as St,U as Vt,V as Fe,W as tt,X as nt,Y as Tt,Z as wt,$ as Ct,a0 as Ft,a1 as Bt,a2 as Nt}from"./framework.BMq9nYrq.js";const At={key:0,class:"VPBackdrop"},Dt=_({__name:"VPBackdrop",props:{show:{type:Boolean}},setup(a){return(e,t)=>(s(),g(De,{name:"fade"},{default:f(()=>[e.show?(s(),c("div",At)):h("",!0)]),_:1}))}}),It=y(Dt,[["__scopeId","data-v-3e47472d"]]),S=kt;function Mt(a,e){let t,n=!1;return()=>{t&&clearTimeout(t),n?t=setTimeout(a,e):(a(),(n=!0)&&setTimeout(()=>n=!1,e))}}function Be(a){return a.startsWith("/")?a:`/${a}`}function Oe(a){const{pathname:e,search:t,hash:n,protocol:o}=new URL(a,"http://a.com");if(yt(a)||a.startsWith("#")||!o.startsWith("http")||!$t(e))return a;const{site:r}=S(),l=e.endsWith("/")||e.endsWith(".html")?a:a.replace(/(?:(^\.+)\/)?.*$/,`$1${e.replace(/(\.md)?$/,r.value.cleanUrls?"":".html")}${t}${n}`);return Ie(l)}function me({correspondingLink:a=!1}={}){const{site:e,localeIndex:t,page:n,theme:o,hash:r}=S(),l=E(()=>{var p,$;return{label:(p=e.value.locales[t.value])==null?void 0:p.label,link:(($=e.value.locales[t.value])==null?void 0:$.link)||(t.value==="root"?"/":`/${t.value}/`)}});return{localeLinks:E(()=>Object.entries(e.value.locales).flatMap(([p,$])=>l.value.label===$.label?[]:{text:$.label,link:Ht($.link||(p==="root"?"/":`/${p}/`),o.value.i18nRouting!==!1&&a,n.value.relativePath.slice(l.value.link.length-1),!e.value.cleanUrls)+r.value})),currentLang:l}}function Ht(a,e,t,n){return e?a.replace(/\/$/,"")+Be(t.replace(/(^|\/)index\.md$/,"$1").replace(/\.md$/,n?".html":"")):a}const zt={class:"NotFound"},Ot={class:"code"},jt={class:"title"},Ut={class:"quote"},Gt={class:"action"},Rt=["href","aria-label"],Wt=_({__name:"NotFound",setup(a){const{theme:e}=S(),{currentLang:t}=me();return(n,o)=>{var r,l,d,p,$;return s(),c("div",zt,[m("p",Ot,A(((r=i(e).notFound)==null?void 0:r.code)??"404"),1),m("h1",jt,A(((l=i(e).notFound)==null?void 0:l.title)??"PAGE NOT FOUND"),1),o[0]||(o[0]=m("div",{class:"divider"},null,-1)),m("blockquote",Ut,A(((d=i(e).notFound)==null?void 0:d.quote)??"But if you don't change your direction, and if you keep looking, you may end up where you are heading."),1),m("div",Gt,[m("a",{class:"link",href:i(Ie)(i(t).link),"aria-label":((p=i(e).notFound)==null?void 0:p.linkLabel)??"go to home"},A((($=i(e).notFound)==null?void 0:$.linkText)??"Take me home"),9,Rt)])])}}}),qt=y(Wt,[["__scopeId","data-v-06d252a7"]]);function ot(a,e){if(Array.isArray(a))return he(a);if(a==null)return[];e=Be(e);const t=Object.keys(a).sort((o,r)=>r.split("/").length-o.split("/").length).find(o=>e.startsWith(Be(o))),n=t?a[t]:[];return Array.isArray(n)?he(n):he(n.items,n.base)}function Kt(a){const e=[];let t=0;for(const n in a){const o=a[n];if(o.items){t=e.push(o);continue}e[t]||e.push({items:[]}),e[t].items.push(o)}return e}function Yt(a){const e=[];function t(n){for(const o of n)o.text&&o.link&&e.push({text:o.text,link:o.link,docFooterText:o.docFooterText}),o.items&&t(o.items)}return t(a),e}function Ne(a,e){return Array.isArray(e)?e.some(t=>Ne(a,t)):ie(a,e.link)?!0:e.items?Ne(a,e.items):!1}function he(a,e){return[...a].map(t=>{const n={...t},o=n.base||e;return o&&n.link&&(n.link=o+n.link),n.items&&(n.items=he(n.items,o)),n})}function te(){const{frontmatter:a,page:e,theme:t}=S(),n=we("(min-width: 960px)"),o=N(!1),r=E(()=>{const z=t.value.sidebar,B=e.value.relativePath;return z?ot(z,B):[]}),l=N(r.value);Q(r,(z,B)=>{JSON.stringify(z)!==JSON.stringify(B)&&(l.value=r.value)});const d=E(()=>a.value.sidebar!==!1&&l.value.length>0&&a.value.layout!=="home"),p=E(()=>$?a.value.aside==null?t.value.aside==="left":a.value.aside==="left":!1),$=E(()=>a.value.layout==="home"?!1:a.value.aside!=null?!!a.value.aside:t.value.aside!==!1),V=E(()=>d.value&&n.value),k=E(()=>d.value?Kt(l.value):[]);function L(){o.value=!0}function F(){o.value=!1}function C(){o.value?F():L()}return{isOpen:o,sidebar:l,sidebarGroups:k,hasSidebar:d,hasAside:$,leftAside:p,isSidebarEnabled:V,open:L,close:F,toggle:C}}function Jt(a,e){let t;be(()=>{t=a.value?document.activeElement:void 0}),x(()=>{window.addEventListener("keyup",n)}),Me(()=>{window.removeEventListener("keyup",n)});function n(o){o.key==="Escape"&&a.value&&(e(),t==null||t.focus())}}function Xt(a){const{page:e,hash:t}=S(),n=N(!1),o=E(()=>a.value.collapsed!=null),r=E(()=>!!a.value.link),l=N(!1),d=()=>{l.value=ie(e.value.relativePath,a.value.link)};Q([e,a,t],d),x(d);const p=E(()=>l.value?!0:a.value.items?Ne(e.value.relativePath,a.value.items):!1),$=E(()=>!!(a.value.items&&a.value.items.length));be(()=>{n.value=!!(o.value&&a.value.collapsed)}),He(()=>{(l.value||p.value)&&(n.value=!1)});function V(){o.value&&(n.value=!n.value)}return{collapsed:n,collapsible:o,isLink:r,isActiveLink:l,hasActiveLink:p,hasChildren:$,toggle:V}}function Zt(){const{hasSidebar:a}=te(),e=we("(min-width: 960px)"),t=we("(min-width: 1280px)");return{isAsideEnabled:E(()=>!t.value&&!e.value?!1:a.value?t.value:e.value)}}const Qt=/\b(?:VPBadge|header-anchor|footnote-ref|ignore-header)\b/,Ae=[];function at(a){return typeof a.outline=="object"&&!Array.isArray(a.outline)&&a.outline.label||a.outlineTitle||"On this page"}function je(a){const e=[...document.querySelectorAll(".VPDoc :where(h1,h2,h3,h4,h5,h6)")].filter(t=>t.id&&t.hasChildNodes()).map(t=>{const n=Number(t.tagName[1]);return{element:t,title:xt(t),link:"#"+t.id,level:n}});return en(e,a)}function xt(a){let e="";for(const t of a.childNodes)if(t.nodeType===1){if(Qt.test(t.className))continue;e+=t.textContent}else t.nodeType===3&&(e+=t.textContent);return e.trim()}function en(a,e){if(e===!1)return[];const t=(typeof e=="object"&&!Array.isArray(e)?e.level:e)||2,[n,o]=typeof t=="number"?[t,t]:t==="deep"?[2,6]:t;return on(a,n,o)}function tn(a,e){const{isAsideEnabled:t}=Zt(),n=Mt(r,100);let o=null;x(()=>{requestAnimationFrame(r),window.addEventListener("scroll",n)}),Et(()=>{l(location.hash)}),Me(()=>{window.removeEventListener("scroll",n)});function r(){if(!t.value)return;const d=window.scrollY,p=window.innerHeight,$=document.body.offsetHeight,V=Math.abs(d+p-$)<1,k=Ae.map(({element:F,link:C})=>({link:C,top:nn(F)})).filter(({top:F})=>!Number.isNaN(F)).sort((F,C)=>F.top-C.top);if(!k.length){l(null);return}if(d<1){l(null);return}if(V){l(k[k.length-1].link);return}let L=null;for(const{link:F,top:C}of k){if(C>d+Lt()+4)break;L=F}l(L)}function l(d){o&&o.classList.remove("active"),d==null?o=null:o=a.value.querySelector(`a[href="${decodeURIComponent(d)}"]`);const p=o;p?(p.classList.add("active"),e.value.style.top=p.offsetTop+39+"px",e.value.style.opacity="1"):(e.value.style.top="33px",e.value.style.opacity="0")}}function nn(a){let e=0;for(;a!==document.body;){if(a===null)return NaN;e+=a.offsetTop,a=a.offsetParent}return e}function on(a,e,t){Ae.length=0;const n=[],o=[];return a.forEach(r=>{const l={...r,children:[]};let d=o[o.length-1];for(;d&&d.level>=l.level;)o.pop(),d=o[o.length-1];if(l.element.classList.contains("ignore-header")||d&&"shouldIgnore"in d){o.push({level:l.level,shouldIgnore:!0});return}l.level>t||l.level<e||(Ae.push({element:l.element,link:l.link}),d?d.children.push(l):n.push(l),o.push(l))}),n}const an=["href","title"],sn=_({__name:"VPDocOutlineItem",props:{headers:{},root:{type:Boolean}},setup(a){function e({target:t}){const n=t.href.split("#")[1],o=document.getElementById(decodeURIComponent(n));o==null||o.focus({preventScroll:!0})}return(t,n)=>{const o=ee("VPDocOutlineItem",!0);return s(),c("ul",{class:D(["VPDocOutlineItem",t.root?"root":"nested"])},[(s(!0),c(M,null,G(t.headers,({children:r,link:l,title:d})=>(s(),c("li",null,[m("a",{class:"outline-link",href:l,onClick:e,title:d},A(d),9,an),r!=null&&r.length?(s(),g(o,{key:0,headers:r},null,8,["headers"])):h("",!0)]))),256))],2)}}}),st=y(sn,[["__scopeId","data-v-d7778d7d"]]),rn={class:"content"},ln={"aria-level":"2",class:"outline-title",id:"doc-outline-aria-label",role:"heading"},un=_({__name:"VPDocAsideOutline",setup(a){const{frontmatter:e,theme:t}=S(),n=Ze([]);ke(()=>{n.value=je(e.value.outline??t.value.outline)});const o=N(),r=N();return tn(o,r),(l,d)=>(s(),c("nav",{"aria-labelledby":"doc-outline-aria-label",class:D(["VPDocAsideOutline",{"has-outline":n.value.length>0}]),ref_key:"container",ref:o},[m("div",rn,[m("div",{class:"outline-marker",ref_key:"marker",ref:r},null,512),m("div",ln,A(i(at)(i(t))),1),b(st,{headers:n.value,root:!0},null,8,["headers"])])],2))}}),cn=y(un,[["__scopeId","data-v-c0faa9ae"]]),dn={class:"VPDocAsideCarbonAds"},mn=_({__name:"VPDocAsideCarbonAds",props:{carbonAds:{}},setup(a){const e=()=>null;return(t,n)=>(s(),c("div",dn,[b(i(e),{"carbon-ads":t.carbonAds},null,8,["carbon-ads"])]))}}),pn={class:"VPDocAside"},fn=_({__name:"VPDocAside",setup(a){const{theme:e}=S();return(t,n)=>(s(),c("div",pn,[u(t.$slots,"aside-top",{},void 0,!0),u(t.$slots,"aside-outline-before",{},void 0,!0),b(cn),u(t.$slots,"aside-outline-after",{},void 0,!0),n[0]||(n[0]=m("div",{class:"spacer"},null,-1)),u(t.$slots,"aside-ads-before",{},void 0,!0),i(e).carbonAds?(s(),g(mn,{key:0,"carbon-ads":i(e).carbonAds},null,8,["carbon-ads"])):h("",!0),u(t.$slots,"aside-ads-after",{},void 0,!0),u(t.$slots,"aside-bottom",{},void 0,!0)]))}}),vn=y(fn,[["__scopeId","data-v-55fda58b"]]);function hn(){const{theme:a,page:e}=S();return E(()=>{const{text:t="Edit this page",pattern:n=""}=a.value.editLink||{};let o;return typeof n=="function"?o=n(e.value):o=n.replace(/:path/g,e.value.filePath),{url:o,text:t}})}function _n(){const{page:a,theme:e,frontmatter:t}=S();return E(()=>{var $,V,k,L,F,C,z,B;const n=ot(e.value.sidebar,a.value.relativePath),o=Yt(n),r=gn(o,q=>q.link.replace(/[?#].*$/,"")),l=r.findIndex(q=>ie(a.value.relativePath,q.link)),d=(($=e.value.docFooter)==null?void 0:$.prev)===!1&&!t.value.prev||t.value.prev===!1,p=((V=e.value.docFooter)==null?void 0:V.next)===!1&&!t.value.next||t.value.next===!1;return{prev:d?void 0:{text:(typeof t.value.prev=="string"?t.value.prev:typeof t.value.prev=="object"?t.value.prev.text:void 0)??((k=r[l-1])==null?void 0:k.docFooterText)??((L=r[l-1])==null?void 0:L.text),link:(typeof t.value.prev=="object"?t.value.prev.link:void 0)??((F=r[l-1])==null?void 0:F.link)},next:p?void 0:{text:(typeof t.value.next=="string"?t.value.next:typeof t.value.next=="object"?t.value.next.text:void 0)??((C=r[l+1])==null?void 0:C.docFooterText)??((z=r[l+1])==null?void 0:z.text),link:(typeof t.value.next=="object"?t.value.next.link:void 0)??((B=r[l+1])==null?void 0:B.link)}}})}function gn(a,e){const t=new Set;return a.filter(n=>{const o=e(n);return t.has(o)?!1:t.add(o)})}const Z=_({__name:"VPLink",props:{tag:{},href:{},noIcon:{type:Boolean},target:{},rel:{}},setup(a){const e=a,t=E(()=>e.tag??(e.href?"a":"span")),n=E(()=>e.href&&Qe.test(e.href)||e.target==="_blank");return(o,r)=>(s(),g(X(t.value),{class:D(["VPLink",{link:o.href,"vp-external-link-icon":n.value,"no-icon":o.noIcon}]),href:o.href?i(Oe)(o.href):void 0,target:o.target??(n.value?"_blank":void 0),rel:o.rel??(n.value?"noreferrer":void 0)},{default:f(()=>[u(o.$slots,"default")]),_:3},8,["class","href","target","rel"]))}}),bn={class:"VPLastUpdated"},kn=["datetime"],yn=_({__name:"VPDocFooterLastUpdated",setup(a){const{theme:e,page:t,lang:n}=S(),o=E(()=>new Date(t.value.lastUpdated)),r=E(()=>o.value.toISOString()),l=N("");return x(()=>{be(()=>{var d,p,$;l.value=new Intl.DateTimeFormat((p=(d=e.value.lastUpdated)==null?void 0:d.formatOptions)!=null&&p.forceLocale?n.value:void 0,(($=e.value.lastUpdated)==null?void 0:$.formatOptions)??{dateStyle:"short",timeStyle:"short"}).format(o.value)})}),(d,p)=>{var $;return s(),c("p",bn,[se(A((($=i(e).lastUpdated)==null?void 0:$.text)||i(e).lastUpdatedText||"Last updated")+": ",1),m("time",{datetime:r.value},A(l.value),9,kn)])}}}),$n=y(yn,[["__scopeId","data-v-8f99b711"]]),En={key:0,class:"VPDocFooter"},Ln={key:0,class:"edit-info"},Pn={key:0,class:"edit-link"},Sn={key:1,class:"last-updated"},Vn={key:1,class:"prev-next","aria-labelledby":"doc-footer-aria-label"},Tn={class:"pager"},wn=["innerHTML"],Cn=["innerHTML"],Fn={class:"pager"},Bn=["innerHTML"],Nn=["innerHTML"],An=_({__name:"VPDocFooter",setup(a){const{theme:e,page:t,frontmatter:n}=S(),o=hn(),r=_n(),l=E(()=>e.value.editLink&&n.value.editLink!==!1),d=E(()=>t.value.lastUpdated),p=E(()=>l.value||d.value||r.value.prev||r.value.next);return($,V)=>{var k,L,F,C;return p.value?(s(),c("footer",En,[u($.$slots,"doc-footer-before",{},void 0,!0),l.value||d.value?(s(),c("div",Ln,[l.value?(s(),c("div",Pn,[b(Z,{class:"edit-link-button",href:i(o).url,"no-icon":!0},{default:f(()=>[V[0]||(V[0]=m("span",{class:"vpi-square-pen edit-link-icon"},null,-1)),se(" "+A(i(o).text),1)]),_:1},8,["href"])])):h("",!0),d.value?(s(),c("div",Sn,[b($n)])):h("",!0)])):h("",!0),(k=i(r).prev)!=null&&k.link||(L=i(r).next)!=null&&L.link?(s(),c("nav",Vn,[V[1]||(V[1]=m("span",{class:"visually-hidden",id:"doc-footer-aria-label"},"Pager",-1)),m("div",Tn,[(F=i(r).prev)!=null&&F.link?(s(),g(Z,{key:0,class:"pager-link prev",href:i(r).prev.link},{default:f(()=>{var z;return[m("span",{class:"desc",innerHTML:((z=i(e).docFooter)==null?void 0:z.prev)||"Previous page"},null,8,wn),m("span",{class:"title",innerHTML:i(r).prev.text},null,8,Cn)]}),_:1},8,["href"])):h("",!0)]),m("div",Fn,[(C=i(r).next)!=null&&C.link?(s(),g(Z,{key:0,class:"pager-link next",href:i(r).next.link},{default:f(()=>{var z;return[m("span",{class:"desc",innerHTML:((z=i(e).docFooter)==null?void 0:z.next)||"Next page"},null,8,Bn),m("span",{class:"title",innerHTML:i(r).next.text},null,8,Nn)]}),_:1},8,["href"])):h("",!0)])])):h("",!0)])):h("",!0)}}}),Dn=y(An,[["__scopeId","data-v-b4f8e1c3"]]),In={class:"container"},Mn={class:"aside-container"},Hn={class:"aside-content"},zn={class:"content"},On={class:"content-container"},jn={class:"main"},Un=_({__name:"VPDoc",setup(a){const{theme:e}=S(),t=de(),{hasSidebar:n,hasAside:o,leftAside:r}=te(),l=E(()=>t.path.replace(/[./]+/g,"_").replace(/_html$/,""));return(d,p)=>{const $=ee("Content");return s(),c("div",{class:D(["VPDoc",{"has-sidebar":i(n),"has-aside":i(o)}])},[u(d.$slots,"doc-top",{},void 0,!0),m("div",In,[i(o)?(s(),c("div",{key:0,class:D(["aside",{"left-aside":i(r)}])},[p[0]||(p[0]=m("div",{class:"aside-curtain"},null,-1)),m("div",Mn,[m("div",Hn,[b(vn,null,{"aside-top":f(()=>[u(d.$slots,"aside-top",{},void 0,!0)]),"aside-bottom":f(()=>[u(d.$slots,"aside-bottom",{},void 0,!0)]),"aside-outline-before":f(()=>[u(d.$slots,"aside-outline-before",{},void 0,!0)]),"aside-outline-after":f(()=>[u(d.$slots,"aside-outline-after",{},void 0,!0)]),"aside-ads-before":f(()=>[u(d.$slots,"aside-ads-before",{},void 0,!0)]),"aside-ads-after":f(()=>[u(d.$slots,"aside-ads-after",{},void 0,!0)]),_:3})])])],2)):h("",!0),m("div",zn,[m("div",On,[u(d.$slots,"doc-before",{},void 0,!0),m("main",jn,[b($,{class:D(["vp-doc",[l.value,i(e).externalLinkIcon&&"external-link-icon-enabled"]])},null,8,["class"])]),b(Dn,null,{"doc-footer-before":f(()=>[u(d.$slots,"doc-footer-before",{},void 0,!0)]),_:3}),u(d.$slots,"doc-after",{},void 0,!0)])])]),u(d.$slots,"doc-bottom",{},void 0,!0)],2)}}}),Gn=y(Un,[["__scopeId","data-v-2eca86da"]]),Rn=_({__name:"VPButton",props:{tag:{},size:{default:"medium"},theme:{default:"brand"},text:{},href:{},target:{},rel:{}},setup(a){const e=a,t=E(()=>e.href&&Qe.test(e.href)),n=E(()=>e.tag||(e.href?"a":"button"));return(o,r)=>(s(),g(X(n.value),{class:D(["VPButton",[o.size,o.theme]]),href:o.href?i(Oe)(o.href):void 0,target:e.target??(t.value?"_blank":void 0),rel:e.rel??(t.value?"noreferrer":void 0)},{default:f(()=>[se(A(o.text),1)]),_:1},8,["class","href","target","rel"]))}}),Wn=y(Rn,[["__scopeId","data-v-3b31c7c7"]]),qn=["src","alt"],Kn=_({inheritAttrs:!1,__name:"VPImage",props:{image:{},alt:{}},setup(a){return(e,t)=>{const n=ee("VPImage",!0);return e.image?(s(),c(M,{key:0},[typeof e.image=="string"||"src"in e.image?(s(),c("img",ae({key:0,class:"VPImage"},typeof e.image=="string"?e.$attrs:{...e.image,...e.$attrs},{src:i(Ie)(typeof e.image=="string"?e.image:e.image.src),alt:e.alt??(typeof e.image=="string"?"":e.image.alt||"")}),null,16,qn)):(s(),c(M,{key:1},[b(n,ae({class:"dark",image:e.image.dark,alt:e.image.alt},e.$attrs),null,16,["image","alt"]),b(n,ae({class:"light",image:e.image.light,alt:e.image.alt},e.$attrs),null,16,["image","alt"])],64))],64)):h("",!0)}}}),ge=y(Kn,[["__scopeId","data-v-6969ec2f"]]),Yn={class:"container"},Jn={class:"main"},Xn={class:"heading"},Zn=["innerHTML"],Qn=["innerHTML"],xn=["innerHTML"],eo={key:0,class:"actions"},to={key:0,class:"image"},no={class:"image-container"},oo=_({__name:"VPHero",props:{name:{},text:{},tagline:{},image:{},actions:{}},setup(a){const e=ye("hero-image-slot-exists");return(t,n)=>(s(),c("div",{class:D(["VPHero",{"has-image":t.image||i(e)}])},[m("div",Yn,[m("div",Jn,[u(t.$slots,"home-hero-info-before",{},void 0,!0),u(t.$slots,"home-hero-info",{},()=>[m("h1",Xn,[t.name?(s(),c("span",{key:0,innerHTML:t.name,class:"name clip"},null,8,Zn)):h("",!0),t.text?(s(),c("span",{key:1,innerHTML:t.text,class:"text"},null,8,Qn)):h("",!0)]),t.tagline?(s(),c("p",{key:0,innerHTML:t.tagline,class:"tagline"},null,8,xn)):h("",!0)],!0),u(t.$slots,"home-hero-info-after",{},void 0,!0),t.actions?(s(),c("div",eo,[(s(!0),c(M,null,G(t.actions,o=>(s(),c("div",{key:o.link,class:"action"},[b(Wn,{tag:"a",size:"medium",theme:o.theme,text:o.text,href:o.link,target:o.target,rel:o.rel},null,8,["theme","text","href","target","rel"])]))),128))])):h("",!0),u(t.$slots,"home-hero-actions-after",{},void 0,!0)]),t.image||i(e)?(s(),c("div",to,[m("div",no,[n[0]||(n[0]=m("div",{class:"image-bg"},null,-1)),u(t.$slots,"home-hero-image",{},()=>[t.image?(s(),g(ge,{key:0,class:"image-src",image:t.image},null,8,["image"])):h("",!0)],!0)])])):h("",!0)])],2))}}),ao=y(oo,[["__scopeId","data-v-cd7faeda"]]),so=_({__name:"VPHomeHero",setup(a){const{frontmatter:e}=S();return(t,n)=>i(e).hero?(s(),g(ao,{key:0,class:"VPHomeHero",name:i(e).hero.name,text:i(e).hero.text,tagline:i(e).hero.tagline,image:i(e).hero.image,actions:i(e).hero.actions},{"home-hero-info-before":f(()=>[u(t.$slots,"home-hero-info-before")]),"home-hero-info":f(()=>[u(t.$slots,"home-hero-info")]),"home-hero-info-after":f(()=>[u(t.$slots,"home-hero-info-after")]),"home-hero-actions-after":f(()=>[u(t.$slots,"home-hero-actions-after")]),"home-hero-image":f(()=>[u(t.$slots,"home-hero-image")]),_:3},8,["name","text","tagline","image","actions"])):h("",!0)}}),ro={class:"box"},io={key:0,class:"icon"},lo=["innerHTML"],uo=["innerHTML"],co=["innerHTML"],mo={key:4,class:"link-text"},po={class:"link-text-value"},fo=_({__name:"VPFeature",props:{icon:{},title:{},details:{},link:{},linkText:{},rel:{},target:{}},setup(a){return(e,t)=>(s(),g(Z,{class:"VPFeature",href:e.link,rel:e.rel,target:e.target,"no-icon":!0,tag:e.link?"a":"div"},{default:f(()=>[m("article",ro,[typeof e.icon=="object"&&e.icon.wrap?(s(),c("div",io,[b(ge,{image:e.icon,alt:e.icon.alt,height:e.icon.height||48,width:e.icon.width||48},null,8,["image","alt","height","width"])])):typeof e.icon=="object"?(s(),g(ge,{key:1,image:e.icon,alt:e.icon.alt,height:e.icon.height||48,width:e.icon.width||48},null,8,["image","alt","height","width"])):e.icon?(s(),c("div",{key:2,class:"icon",innerHTML:e.icon},null,8,lo)):h("",!0),m("h2",{class:"title",innerHTML:e.title},null,8,uo),e.details?(s(),c("p",{key:3,class:"details",innerHTML:e.details},null,8,co)):h("",!0),e.linkText?(s(),c("div",mo,[m("p",po,[se(A(e.linkText)+" ",1),t[0]||(t[0]=m("span",{class:"vpi-arrow-right link-text-icon"},null,-1))])])):h("",!0)])]),_:1},8,["href","rel","target","tag"]))}}),vo=y(fo,[["__scopeId","data-v-443339a6"]]),ho={key:0,class:"VPFeatures"},_o={class:"container"},go={class:"items"},bo=_({__name:"VPFeatures",props:{features:{}},setup(a){const e=a,t=E(()=>{const n=e.features.length;if(n){if(n===2)return"grid-2";if(n===3)return"grid-3";if(n%3===0)return"grid-6";if(n>3)return"grid-4"}else return});return(n,o)=>n.features?(s(),c("div",ho,[m("div",_o,[m("div",go,[(s(!0),c(M,null,G(n.features,r=>(s(),c("div",{key:r.title,class:D(["item",[t.value]])},[b(vo,{icon:r.icon,title:r.title,details:r.details,link:r.link,"link-text":r.linkText,rel:r.rel,target:r.target},null,8,["icon","title","details","link","link-text","rel","target"])],2))),128))])])])):h("",!0)}}),ko=y(bo,[["__scopeId","data-v-1bc6ef7d"]]),yo=_({__name:"VPHomeFeatures",setup(a){const{frontmatter:e}=S();return(t,n)=>i(e).features?(s(),g(ko,{key:0,class:"VPHomeFeatures",features:i(e).features},null,8,["features"])):h("",!0)}}),$o=_({__name:"VPHomeContent",setup(a){const{width:e}=Pt({initialWidth:0,includeScrollbar:!1});return(t,n)=>(s(),c("div",{class:"vp-doc container",style:xe(i(e)?{"--vp-offset":`calc(50% - ${i(e)/2}px)`}:{})},[u(t.$slots,"default",{},void 0,!0)],4))}}),Eo=y($o,[["__scopeId","data-v-a1774aad"]]),Lo=_({__name:"VPHome",setup(a){const{frontmatter:e,theme:t}=S();return(n,o)=>{const r=ee("Content");return s(),c("div",{class:D(["VPHome",{"external-link-icon-enabled":i(t).externalLinkIcon}])},[u(n.$slots,"home-hero-before",{},void 0,!0),b(so,null,{"home-hero-info-before":f(()=>[u(n.$slots,"home-hero-info-before",{},void 0,!0)]),"home-hero-info":f(()=>[u(n.$slots,"home-hero-info",{},void 0,!0)]),"home-hero-info-after":f(()=>[u(n.$slots,"home-hero-info-after",{},void 0,!0)]),"home-hero-actions-after":f(()=>[u(n.$slots,"home-hero-actions-after",{},void 0,!0)]),"home-hero-image":f(()=>[u(n.$slots,"home-hero-image",{},void 0,!0)]),_:3}),u(n.$slots,"home-hero-after",{},void 0,!0),u(n.$slots,"home-features-before",{},void 0,!0),b(yo),u(n.$slots,"home-features-after",{},void 0,!0),i(e).markdownStyles!==!1?(s(),g(Eo,{key:0},{default:f(()=>[b(r)]),_:1})):(s(),g(r,{key:1}))],2)}}}),Po=y(Lo,[["__scopeId","data-v-47e27c74"]]),So={},Vo={class:"VPPage"};function To(a,e){const t=ee("Content");return s(),c("div",Vo,[u(a.$slots,"page-top"),b(t),u(a.$slots,"page-bottom")])}const wo=y(So,[["render",To]]),Co=_({__name:"VPContent",setup(a){const{page:e,frontmatter:t}=S(),{hasSidebar:n}=te();return(o,r)=>(s(),c("div",{class:D(["VPContent",{"has-sidebar":i(n),"is-home":i(t).layout==="home"}]),id:"VPContent"},[i(e).isNotFound?u(o.$slots,"not-found",{key:0},()=>[b(qt)],!0):i(t).layout==="page"?(s(),g(wo,{key:1},{"page-top":f(()=>[u(o.$slots,"page-top",{},void 0,!0)]),"page-bottom":f(()=>[u(o.$slots,"page-bottom",{},void 0,!0)]),_:3})):i(t).layout==="home"?(s(),g(Po,{key:2},{"home-hero-before":f(()=>[u(o.$slots,"home-hero-before",{},void 0,!0)]),"home-hero-info-before":f(()=>[u(o.$slots,"home-hero-info-before",{},void 0,!0)]),"home-hero-info":f(()=>[u(o.$slots,"home-hero-info",{},void 0,!0)]),"home-hero-info-after":f(()=>[u(o.$slots,"home-hero-info-after",{},void 0,!0)]),"home-hero-actions-after":f(()=>[u(o.$slots,"home-hero-actions-after",{},void 0,!0)]),"home-hero-image":f(()=>[u(o.$slots,"home-hero-image",{},void 0,!0)]),"home-hero-after":f(()=>[u(o.$slots,"home-hero-after",{},void 0,!0)]),"home-features-before":f(()=>[u(o.$slots,"home-features-before",{},void 0,!0)]),"home-features-after":f(()=>[u(o.$slots,"home-features-after",{},void 0,!0)]),_:3})):i(t).layout&&i(t).layout!=="doc"?(s(),g(X(i(t).layout),{key:3})):(s(),g(Gn,{key:4},{"doc-top":f(()=>[u(o.$slots,"doc-top",{},void 0,!0)]),"doc-bottom":f(()=>[u(o.$slots,"doc-bottom",{},void 0,!0)]),"doc-footer-before":f(()=>[u(o.$slots,"doc-footer-before",{},void 0,!0)]),"doc-before":f(()=>[u(o.$slots,"doc-before",{},void 0,!0)]),"doc-after":f(()=>[u(o.$slots,"doc-after",{},void 0,!0)]),"aside-top":f(()=>[u(o.$slots,"aside-top",{},void 0,!0)]),"aside-outline-before":f(()=>[u(o.$slots,"aside-outline-before",{},void 0,!0)]),"aside-outline-after":f(()=>[u(o.$slots,"aside-outline-after",{},void 0,!0)]),"aside-ads-before":f(()=>[u(o.$slots,"aside-ads-before",{},void 0,!0)]),"aside-ads-after":f(()=>[u(o.$slots,"aside-ads-after",{},void 0,!0)]),"aside-bottom":f(()=>[u(o.$slots,"aside-bottom",{},void 0,!0)]),_:3}))],2))}}),Fo=y(Co,[["__scopeId","data-v-bf3e0352"]]),Bo={class:"container"},No=["innerHTML"],Ao=["innerHTML"],Do=_({__name:"VPFooter",setup(a){const{theme:e,frontmatter:t}=S(),{hasSidebar:n}=te();return(o,r)=>i(e).footer&&i(t).footer!==!1?(s(),c("footer",{key:0,class:D(["VPFooter",{"has-sidebar":i(n)}])},[m("div",Bo,[i(e).footer.message?(s(),c("p",{key:0,class:"message",innerHTML:i(e).footer.message},null,8,No)):h("",!0),i(e).footer.copyright?(s(),c("p",{key:1,class:"copyright",innerHTML:i(e).footer.copyright},null,8,Ao)):h("",!0)])],2)):h("",!0)}}),Io=y(Do,[["__scopeId","data-v-dbcfea6c"]]);function Mo(){const{theme:a,frontmatter:e}=S(),t=Ze([]),n=E(()=>t.value.length>0);return ke(()=>{t.value=je(e.value.outline??a.value.outline)}),{headers:t,hasLocalNav:n}}const Ho={class:"menu-text"},zo={class:"header"},Oo={class:"outline"},jo=_({__name:"VPLocalNavOutlineDropdown",props:{headers:{},navHeight:{}},setup(a){const e=a,{theme:t}=S(),n=N(!1),o=N(0),r=N(),l=N();function d(k){var L;(L=r.value)!=null&&L.contains(k.target)||(n.value=!1)}Q(n,k=>{if(k){document.addEventListener("click",d);return}document.removeEventListener("click",d)}),Ce("Escape",()=>{n.value=!1}),ke(()=>{n.value=!1});function p(){n.value=!n.value,o.value=window.innerHeight+Math.min(window.scrollY-e.navHeight,0)}function $(k){k.target.classList.contains("outline-link")&&(l.value&&(l.value.style.transition="none"),ze(()=>{n.value=!1}))}function V(){n.value=!1,window.scrollTo({top:0,left:0,behavior:"smooth"})}return(k,L)=>(s(),c("div",{class:"VPLocalNavOutlineDropdown",style:xe({"--vp-vh":o.value+"px"}),ref_key:"main",ref:r},[k.headers.length>0?(s(),c("button",{key:0,onClick:p,class:D({open:n.value})},[m("span",Ho,A(i(at)(i(t))),1),L[0]||(L[0]=m("span",{class:"vpi-chevron-right icon"},null,-1))],2)):(s(),c("button",{key:1,onClick:V},A(i(t).returnToTopLabel||"Return to top"),1)),b(De,{name:"flyout"},{default:f(()=>[n.value?(s(),c("div",{key:0,ref_key:"items",ref:l,class:"items",onClick:$},[m("div",zo,[m("a",{class:"top-link",href:"#",onClick:V},A(i(t).returnToTopLabel||"Return to top"),1)]),m("div",Oo,[b(st,{headers:k.headers},null,8,["headers"])])],512)):h("",!0)]),_:1})],4))}}),Uo=y(jo,[["__scopeId","data-v-d33580be"]]),Go={class:"container"},Ro=["aria-expanded"],Wo={class:"menu-text"},qo=_({__name:"VPLocalNav",props:{open:{type:Boolean}},emits:["open-menu"],setup(a){const{theme:e,frontmatter:t}=S(),{hasSidebar:n}=te(),{headers:o}=Mo(),{y:r}=et(),l=N(0);x(()=>{l.value=parseInt(getComputedStyle(document.documentElement).getPropertyValue("--vp-nav-height"))}),ke(()=>{o.value=je(t.value.outline??e.value.outline)});const d=E(()=>o.value.length===0),p=E(()=>d.value&&!n.value),$=E(()=>({VPLocalNav:!0,"has-sidebar":n.value,empty:d.value,fixed:p.value}));return(V,k)=>i(t).layout!=="home"&&(!p.value||i(r)>=l.value)?(s(),c("div",{key:0,class:D($.value)},[m("div",Go,[i(n)?(s(),c("button",{key:0,class:"menu","aria-expanded":V.open,"aria-controls":"VPSidebarNav",onClick:k[0]||(k[0]=L=>V.$emit("open-menu"))},[k[1]||(k[1]=m("span",{class:"vpi-align-left menu-icon"},null,-1)),m("span",Wo,A(i(e).sidebarMenuLabel||"Menu"),1)],8,Ro)):h("",!0),b(Uo,{headers:i(o),navHeight:l.value},null,8,["headers","navHeight"])])],2)):h("",!0)}}),Ko=y(qo,[["__scopeId","data-v-3746dd0c"]]);function Yo(){const a=N(!1);function e(){a.value=!0,window.addEventListener("resize",o)}function t(){a.value=!1,window.removeEventListener("resize",o)}function n(){a.value?t():e()}function o(){window.outerWidth>=768&&t()}const r=de();return Q(()=>r.path,t),{isScreenOpen:a,openScreen:e,closeScreen:t,toggleScreen:n}}const Jo={},Xo={class:"VPSwitch",type:"button",role:"switch"},Zo={class:"check"},Qo={key:0,class:"icon"};function xo(a,e){return s(),c("button",Xo,[m("span",Zo,[a.$slots.default?(s(),c("span",Qo,[u(a.$slots,"default",{},void 0,!0)])):h("",!0)])])}const ea=y(Jo,[["render",xo],["__scopeId","data-v-5c495361"]]),ta=_({__name:"VPSwitchAppearance",setup(a){const{isDark:e,theme:t}=S(),n=ye("toggle-appearance",()=>{e.value=!e.value}),o=N("");return He(()=>{o.value=e.value?t.value.lightModeSwitchTitle||"Switch to light theme":t.value.darkModeSwitchTitle||"Switch to dark theme"}),(r,l)=>(s(),g(ea,{title:o.value,class:"VPSwitchAppearance","aria-checked":i(e),onClick:i(n)},{default:f(()=>l[0]||(l[0]=[m("span",{class:"vpi-sun sun"},null,-1),m("span",{class:"vpi-moon moon"},null,-1)])),_:1},8,["title","aria-checked","onClick"]))}}),Ue=y(ta,[["__scopeId","data-v-fc47fc5d"]]),na={key:0,class:"VPNavBarAppearance"},oa=_({__name:"VPNavBarAppearance",setup(a){const{site:e}=S();return(t,n)=>i(e).appearance&&i(e).appearance!=="force-dark"&&i(e).appearance!=="force-auto"?(s(),c("div",na,[b(Ue)])):h("",!0)}}),aa=y(oa,[["__scopeId","data-v-4361c235"]]),Ge=N();let rt=!1,Te=0;function sa(a){const e=N(!1);if($e){!rt&&ra(),Te++;const t=Q(Ge,n=>{var o,r,l;n===a.el.value||(o=a.el.value)!=null&&o.contains(n)?(e.value=!0,(r=a.onFocus)==null||r.call(a)):(e.value=!1,(l=a.onBlur)==null||l.call(a))});Me(()=>{t(),Te--,Te||ia()})}return St(e)}function ra(){document.addEventListener("focusin",it),rt=!0,Ge.value=document.activeElement}function ia(){document.removeEventListener("focusin",it)}function it(){Ge.value=document.activeElement}const la={class:"VPMenuLink"},ua=["innerHTML"],ca=_({__name:"VPMenuLink",props:{item:{}},setup(a){const{page:e}=S();return(t,n)=>(s(),c("div",la,[b(Z,{class:D({active:i(ie)(i(e).relativePath,t.item.activeMatch||t.item.link,!!t.item.activeMatch)}),href:t.item.link,target:t.item.target,rel:t.item.rel,"no-icon":t.item.noIcon},{default:f(()=>[m("span",{innerHTML:t.item.text},null,8,ua)]),_:1},8,["class","href","target","rel","no-icon"])]))}}),Ee=y(ca,[["__scopeId","data-v-c7e3eb75"]]),da={class:"VPMenuGroup"},ma={key:0,class:"title"},pa=_({__name:"VPMenuGroup",props:{text:{},items:{}},setup(a){return(e,t)=>(s(),c("div",da,[e.text?(s(),c("p",ma,A(e.text),1)):h("",!0),(s(!0),c(M,null,G(e.items,n=>(s(),c(M,null,["link"in n?(s(),g(Ee,{key:0,item:n},null,8,["item"])):h("",!0)],64))),256))]))}}),fa=y(pa,[["__scopeId","data-v-67474ab2"]]),va={class:"VPMenu"},ha={key:0,class:"items"},_a=_({__name:"VPMenu",props:{items:{}},setup(a){return(e,t)=>(s(),c("div",va,[e.items?(s(),c("div",ha,[(s(!0),c(M,null,G(e.items,n=>(s(),c(M,{key:JSON.stringify(n)},["link"in n?(s(),g(Ee,{key:0,item:n},null,8,["item"])):"component"in n?(s(),g(X(n.component),ae({key:1,ref_for:!0},n.props),null,16)):(s(),g(fa,{key:2,text:n.text,items:n.items},null,8,["text","items"]))],64))),128))])):h("",!0),u(e.$slots,"default",{},void 0,!0)]))}}),ga=y(_a,[["__scopeId","data-v-d56355cd"]]),ba=["aria-expanded","aria-label"],ka={key:0,class:"text"},ya=["innerHTML"],$a={key:1,class:"vpi-more-horizontal icon"},Ea={class:"menu"},La=_({__name:"VPFlyout",props:{icon:{},button:{},label:{},items:{}},setup(a){const e=N(!1),t=N();sa({el:t,onBlur:n});function n(){e.value=!1}return(o,r)=>(s(),c("div",{class:"VPFlyout",ref_key:"el",ref:t,onMouseenter:r[1]||(r[1]=l=>e.value=!0),onMouseleave:r[2]||(r[2]=l=>e.value=!1)},[m("button",{type:"button",class:"button","aria-haspopup":"true","aria-expanded":e.value,"aria-label":o.label,onClick:r[0]||(r[0]=l=>e.value=!e.value)},[o.button||o.icon?(s(),c("span",ka,[o.icon?(s(),c("span",{key:0,class:D([o.icon,"option-icon"])},null,2)):h("",!0),o.button?(s(),c("span",{key:1,innerHTML:o.button},null,8,ya)):h("",!0),r[3]||(r[3]=m("span",{class:"vpi-chevron-down text-icon"},null,-1))])):(s(),c("span",$a))],8,ba),m("div",Ea,[b(ga,{items:o.items},{default:f(()=>[u(o.$slots,"default",{},void 0,!0)]),_:3},8,["items"])])],544))}}),Re=y(La,[["__scopeId","data-v-e81ac63d"]]),Pa=["href","aria-label","innerHTML"],Sa=_({__name:"VPSocialLink",props:{icon:{},link:{},ariaLabel:{}},setup(a){const e=a,t=N();x(async()=>{var r;await ze();const o=(r=t.value)==null?void 0:r.children[0];o instanceof HTMLElement&&o.className.startsWith("vpi-social-")&&(getComputedStyle(o).maskImage||getComputedStyle(o).webkitMaskImage)==="none"&&o.style.setProperty("--icon",`url('https://api.iconify.design/simple-icons/${e.icon}.svg')`)});const n=E(()=>typeof e.icon=="object"?e.icon.svg:`<span class="vpi-social-${e.icon}"></span>`);return(o,r)=>(s(),c("a",{ref_key:"el",ref:t,class:"VPSocialLink no-icon",href:o.link,"aria-label":o.ariaLabel??(typeof o.icon=="string"?o.icon:""),target:"_blank",rel:"noopener",innerHTML:n.value},null,8,Pa))}}),Va=y(Sa,[["__scopeId","data-v-54ae48b6"]]),Ta={class:"VPSocialLinks"},wa=_({__name:"VPSocialLinks",props:{links:{}},setup(a){return(e,t)=>(s(),c("div",Ta,[(s(!0),c(M,null,G(e.links,({link:n,icon:o,ariaLabel:r})=>(s(),g(Va,{key:n,icon:o,link:n,ariaLabel:r},null,8,["icon","link","ariaLabel"]))),128))]))}}),We=y(wa,[["__scopeId","data-v-3f7b2744"]]),Ca={key:0,class:"group translations"},Fa={class:"trans-title"},Ba={key:1,class:"group"},Na={class:"item appearance"},Aa={class:"label"},Da={class:"appearance-action"},Ia={key:2,class:"group"},Ma={class:"item social-links"},Ha=_({__name:"VPNavBarExtra",setup(a){const{site:e,theme:t}=S(),{localeLinks:n,currentLang:o}=me({correspondingLink:!0}),r=E(()=>n.value.length&&o.value.label||e.value.appearance||t.value.socialLinks);return(l,d)=>r.value?(s(),g(Re,{key:0,class:"VPNavBarExtra",label:"extra navigation"},{default:f(()=>[i(n).length&&i(o).label?(s(),c("div",Ca,[m("p",Fa,A(i(o).label),1),(s(!0),c(M,null,G(i(n),p=>(s(),g(Ee,{key:p.link,item:p},null,8,["item"]))),128))])):h("",!0),i(e).appearance&&i(e).appearance!=="force-dark"&&i(e).appearance!=="force-auto"?(s(),c("div",Ba,[m("div",Na,[m("p",Aa,A(i(t).darkModeSwitchLabel||"Appearance"),1),m("div",Da,[b(Ue)])])])):h("",!0),i(t).socialLinks?(s(),c("div",Ia,[m("div",Ma,[b(We,{class:"social-links-list",links:i(t).socialLinks},null,8,["links"])])])):h("",!0)]),_:1})):h("",!0)}}),za=y(Ha,[["__scopeId","data-v-77380643"]]),Oa=["aria-expanded"],ja=_({__name:"VPNavBarHamburger",props:{active:{type:Boolean}},emits:["click"],setup(a){return(e,t)=>(s(),c("button",{type:"button",class:D(["VPNavBarHamburger",{active:e.active}]),"aria-label":"mobile navigation","aria-expanded":e.active,"aria-controls":"VPNavScreen",onClick:t[0]||(t[0]=n=>e.$emit("click"))},t[1]||(t[1]=[m("span",{class:"container"},[m("span",{class:"top"}),m("span",{class:"middle"}),m("span",{class:"bottom"})],-1)]),10,Oa))}}),Ua=y(ja,[["__scopeId","data-v-330e573a"]]),Ga=["innerHTML"],Ra=_({__name:"VPNavBarMenuLink",props:{item:{}},setup(a){const{page:e}=S();return(t,n)=>(s(),g(Z,{class:D({VPNavBarMenuLink:!0,active:i(ie)(i(e).relativePath,t.item.activeMatch||t.item.link,!!t.item.activeMatch)}),href:t.item.link,target:t.item.target,rel:t.item.rel,"no-icon":t.item.noIcon,tabindex:"0"},{default:f(()=>[m("span",{innerHTML:t.item.text},null,8,Ga)]),_:1},8,["class","href","target","rel","no-icon"]))}}),Wa=y(Ra,[["__scopeId","data-v-428e00cd"]]),qa=_({__name:"VPNavBarMenuGroup",props:{item:{}},setup(a){const e=a,{page:t}=S(),n=r=>"component"in r?!1:"link"in r?ie(t.value.relativePath,r.link,!!e.item.activeMatch):r.items.some(n),o=E(()=>n(e.item));return(r,l)=>(s(),g(Re,{class:D({VPNavBarMenuGroup:!0,active:i(ie)(i(t).relativePath,r.item.activeMatch,!!r.item.activeMatch)||o.value}),button:r.item.text,items:r.item.items},null,8,["class","button","items"]))}}),Ka={key:0,"aria-labelledby":"main-nav-aria-label",class:"VPNavBarMenu"},Ya=_({__name:"VPNavBarMenu",setup(a){const{theme:e}=S();return(t,n)=>i(e).nav?(s(),c("nav",Ka,[n[0]||(n[0]=m("span",{id:"main-nav-aria-label",class:"visually-hidden"}," Main Navigation ",-1)),(s(!0),c(M,null,G(i(e).nav,o=>(s(),c(M,{key:JSON.stringify(o)},["link"in o?(s(),g(Wa,{key:0,item:o},null,8,["item"])):"component"in o?(s(),g(X(o.component),ae({key:1,ref_for:!0},o.props),null,16)):(s(),g(qa,{key:2,item:o},null,8,["item"]))],64))),128))])):h("",!0)}}),Ja=y(Ya,[["__scopeId","data-v-97baaf5c"]]);function Xa(a){const{localeIndex:e,theme:t}=S();function n(o){var C,z,B;const r=o.split("."),l=(C=t.value.search)==null?void 0:C.options,d=l&&typeof l=="object",p=d&&((B=(z=l.locales)==null?void 0:z[e.value])==null?void 0:B.translations)||null,$=d&&l.translations||null;let V=p,k=$,L=a;const F=r.pop();for(const q of r){let Y=null;const O=L==null?void 0:L[q];O&&(Y=L=O);const ne=k==null?void 0:k[q];ne&&(Y=k=ne);const J=V==null?void 0:V[q];J&&(Y=V=J),O||(L=Y),ne||(k=Y),J||(V=Y)}return(V==null?void 0:V[F])??(k==null?void 0:k[F])??(L==null?void 0:L[F])??""}return n}const Za=["aria-label"],Qa={class:"DocSearch-Button-Container"},xa={class:"DocSearch-Button-Placeholder"},Ye=_({__name:"VPNavBarSearchButton",setup(a){const t=Xa({button:{buttonText:"Search",buttonAriaLabel:"Search"}});return(n,o)=>(s(),c("button",{type:"button",class:"DocSearch DocSearch-Button","aria-label":i(t)("button.buttonAriaLabel")},[m("span",Qa,[o[0]||(o[0]=m("span",{class:"vp-icon DocSearch-Search-Icon"},null,-1)),m("span",xa,A(i(t)("button.buttonText")),1)]),o[1]||(o[1]=m("span",{class:"DocSearch-Button-Keys"},[m("kbd",{class:"DocSearch-Button-Key"}),m("kbd",{class:"DocSearch-Button-Key"},"K")],-1))],8,Za))}}),es={class:"VPNavBarSearch"},ts={id:"local-search"},ns={key:1,id:"docsearch"},os=_({__name:"VPNavBarSearch",setup(a){const e=Vt(()=>Fe(()=>import("./VPLocalSearchBox.BMPQPFwl.js"),__vite__mapDeps([0,1]))),t=()=>null,{theme:n}=S(),o=N(!1),r=N(!1);x(()=>{});function l(){o.value||(o.value=!0,setTimeout(d,16))}function d(){const k=new Event("keydown");k.key="k",k.metaKey=!0,window.dispatchEvent(k),setTimeout(()=>{document.querySelector(".DocSearch-Modal")||d()},16)}function p(k){const L=k.target,F=L.tagName;return L.isContentEditable||F==="INPUT"||F==="SELECT"||F==="TEXTAREA"}const $=N(!1);Ce("k",k=>{(k.ctrlKey||k.metaKey)&&(k.preventDefault(),$.value=!0)}),Ce("/",k=>{p(k)||(k.preventDefault(),$.value=!0)});const V="local";return(k,L)=>{var F;return s(),c("div",es,[i(V)==="local"?(s(),c(M,{key:0},[$.value?(s(),g(i(e),{key:0,onClose:L[0]||(L[0]=C=>$.value=!1)})):h("",!0),m("div",ts,[b(Ye,{onClick:L[1]||(L[1]=C=>$.value=!0)})])],64)):i(V)==="algolia"?(s(),c(M,{key:1},[o.value?(s(),g(i(t),{key:0,algolia:((F=i(n).search)==null?void 0:F.options)??i(n).algolia,onVnodeBeforeMount:L[2]||(L[2]=C=>r.value=!0)},null,8,["algolia"])):h("",!0),r.value?h("",!0):(s(),c("div",ns,[b(Ye,{onClick:l})]))],64)):h("",!0)])}}}),as=_({__name:"VPNavBarSocialLinks",setup(a){const{theme:e}=S();return(t,n)=>i(e).socialLinks?(s(),g(We,{key:0,class:"VPNavBarSocialLinks",links:i(e).socialLinks},null,8,["links"])):h("",!0)}}),ss=y(as,[["__scopeId","data-v-d970829e"]]),rs=["href","rel","target"],is=["innerHTML"],ls={key:2},us=_({__name:"VPNavBarTitle",setup(a){const{site:e,theme:t}=S(),{hasSidebar:n}=te(),{currentLang:o}=me(),r=E(()=>{var p;return typeof t.value.logoLink=="string"?t.value.logoLink:(p=t.value.logoLink)==null?void 0:p.link}),l=E(()=>{var p;return typeof t.value.logoLink=="string"||(p=t.value.logoLink)==null?void 0:p.rel}),d=E(()=>{var p;return typeof t.value.logoLink=="string"||(p=t.value.logoLink)==null?void 0:p.target});return(p,$)=>(s(),c("div",{class:D(["VPNavBarTitle",{"has-sidebar":i(n)}])},[m("a",{class:"title",href:r.value??i(Oe)(i(o).link),rel:l.value,target:d.value},[u(p.$slots,"nav-bar-title-before",{},void 0,!0),i(t).logo?(s(),g(ge,{key:0,class:"logo",image:i(t).logo},null,8,["image"])):h("",!0),i(t).siteTitle?(s(),c("span",{key:1,innerHTML:i(t).siteTitle},null,8,is)):i(t).siteTitle===void 0?(s(),c("span",ls,A(i(e).title),1)):h("",!0),u(p.$slots,"nav-bar-title-after",{},void 0,!0)],8,rs)],2))}}),cs=y(us,[["__scopeId","data-v-94e7aa9a"]]),ds={class:"items"},ms={class:"title"},ps=_({__name:"VPNavBarTranslations",setup(a){const{theme:e}=S(),{localeLinks:t,currentLang:n}=me({correspondingLink:!0});return(o,r)=>i(t).length&&i(n).label?(s(),g(Re,{key:0,class:"VPNavBarTranslations",icon:"vpi-languages",label:i(e).langMenuLabel||"Change language"},{default:f(()=>[m("div",ds,[m("p",ms,A(i(n).label),1),(s(!0),c(M,null,G(i(t),l=>(s(),g(Ee,{key:l.link,item:l},null,8,["item"]))),128))])]),_:1},8,["label"])):h("",!0)}}),fs=y(ps,[["__scopeId","data-v-604d585e"]]),vs={class:"wrapper"},hs={class:"container"},_s={class:"title"},gs={class:"content"},bs={class:"content-body"},ks=_({__name:"VPNavBar",props:{isScreenOpen:{type:Boolean}},emits:["toggle-screen"],setup(a){const e=a,{y:t}=et(),{hasSidebar:n}=te(),{frontmatter:o}=S(),r=N({});return He(()=>{r.value={"has-sidebar":n.value,home:o.value.layout==="home",top:t.value===0,"screen-open":e.isScreenOpen}}),(l,d)=>(s(),c("div",{class:D(["VPNavBar",r.value])},[m("div",vs,[m("div",hs,[m("div",_s,[b(cs,null,{"nav-bar-title-before":f(()=>[u(l.$slots,"nav-bar-title-before",{},void 0,!0)]),"nav-bar-title-after":f(()=>[u(l.$slots,"nav-bar-title-after",{},void 0,!0)]),_:3})]),m("div",gs,[m("div",bs,[u(l.$slots,"nav-bar-content-before",{},void 0,!0),b(os,{class:"search"}),b(Ja,{class:"menu"}),b(fs,{class:"translations"}),b(aa,{class:"appearance"}),b(ss,{class:"social-links"}),b(za,{class:"extra"}),u(l.$slots,"nav-bar-content-after",{},void 0,!0),b(Ua,{class:"hamburger",active:l.isScreenOpen,onClick:d[0]||(d[0]=p=>l.$emit("toggle-screen"))},null,8,["active"])])])])]),d[1]||(d[1]=m("div",{class:"divider"},[m("div",{class:"divider-line"})],-1))],2))}}),ys=y(ks,[["__scopeId","data-v-78672aa7"]]),$s={key:0,class:"VPNavScreenAppearance"},Es={class:"text"},Ls=_({__name:"VPNavScreenAppearance",setup(a){const{site:e,theme:t}=S();return(n,o)=>i(e).appearance&&i(e).appearance!=="force-dark"&&i(e).appearance!=="force-auto"?(s(),c("div",$s,[m("p",Es,A(i(t).darkModeSwitchLabel||"Appearance"),1),b(Ue)])):h("",!0)}}),Ps=y(Ls,[["__scopeId","data-v-8dd0e5a3"]]),Ss=["innerHTML"],Vs=_({__name:"VPNavScreenMenuLink",props:{item:{}},setup(a){const e=ye("close-screen");return(t,n)=>(s(),g(Z,{class:"VPNavScreenMenuLink",href:t.item.link,target:t.item.target,rel:t.item.rel,"no-icon":t.item.noIcon,onClick:i(e)},{default:f(()=>[m("span",{innerHTML:t.item.text},null,8,Ss)]),_:1},8,["href","target","rel","no-icon","onClick"]))}}),Ts=y(Vs,[["__scopeId","data-v-11a78402"]]),ws=["innerHTML"],Cs=_({__name:"VPNavScreenMenuGroupLink",props:{item:{}},setup(a){const e=ye("close-screen");return(t,n)=>(s(),g(Z,{class:"VPNavScreenMenuGroupLink",href:t.item.link,target:t.item.target,rel:t.item.rel,"no-icon":t.item.noIcon,onClick:i(e)},{default:f(()=>[m("span",{innerHTML:t.item.text},null,8,ws)]),_:1},8,["href","target","rel","no-icon","onClick"]))}}),lt=y(Cs,[["__scopeId","data-v-581cfdc0"]]),Fs={class:"VPNavScreenMenuGroupSection"},Bs={key:0,class:"title"},Ns=_({__name:"VPNavScreenMenuGroupSection",props:{text:{},items:{}},setup(a){return(e,t)=>(s(),c("div",Fs,[e.text?(s(),c("p",Bs,A(e.text),1)):h("",!0),(s(!0),c(M,null,G(e.items,n=>(s(),g(lt,{key:n.text,item:n},null,8,["item"]))),128))]))}}),As=y(Ns,[["__scopeId","data-v-fbe68093"]]),Ds=["aria-controls","aria-expanded"],Is=["innerHTML"],Ms=["id"],Hs={key:0,class:"item"},zs={key:1,class:"item"},Os={key:2,class:"group"},js=_({__name:"VPNavScreenMenuGroup",props:{text:{},items:{}},setup(a){const e=a,t=N(!1),n=E(()=>`NavScreenGroup-${e.text.replace(" ","-").toLowerCase()}`);function o(){t.value=!t.value}return(r,l)=>(s(),c("div",{class:D(["VPNavScreenMenuGroup",{open:t.value}])},[m("button",{class:"button","aria-controls":n.value,"aria-expanded":t.value,onClick:o},[m("span",{class:"button-text",innerHTML:r.text},null,8,Is),l[0]||(l[0]=m("span",{class:"vpi-plus button-icon"},null,-1))],8,Ds),m("div",{id:n.value,class:"items"},[(s(!0),c(M,null,G(r.items,d=>(s(),c(M,{key:JSON.stringify(d)},["link"in d?(s(),c("div",Hs,[b(lt,{item:d},null,8,["item"])])):"component"in d?(s(),c("div",zs,[(s(),g(X(d.component),ae({ref_for:!0},d.props,{"screen-menu":""}),null,16))])):(s(),c("div",Os,[b(As,{text:d.text,items:d.items},null,8,["text","items"])]))],64))),128))],8,Ms)],2))}}),Us=y(js,[["__scopeId","data-v-bb7e0d79"]]),Gs={key:0,class:"VPNavScreenMenu"},Rs=_({__name:"VPNavScreenMenu",setup(a){const{theme:e}=S();return(t,n)=>i(e).nav?(s(),c("nav",Gs,[(s(!0),c(M,null,G(i(e).nav,o=>(s(),c(M,{key:JSON.stringify(o)},["link"in o?(s(),g(Ts,{key:0,item:o},null,8,["item"])):"component"in o?(s(),g(X(o.component),ae({key:1,ref_for:!0},o.props,{"screen-menu":""}),null,16)):(s(),g(Us,{key:2,text:o.text||"",items:o.items},null,8,["text","items"]))],64))),128))])):h("",!0)}}),Ws=_({__name:"VPNavScreenSocialLinks",setup(a){const{theme:e}=S();return(t,n)=>i(e).socialLinks?(s(),g(We,{key:0,class:"VPNavScreenSocialLinks",links:i(e).socialLinks},null,8,["links"])):h("",!0)}}),qs={class:"list"},Ks=_({__name:"VPNavScreenTranslations",setup(a){const{localeLinks:e,currentLang:t}=me({correspondingLink:!0}),n=N(!1);function o(){n.value=!n.value}return(r,l)=>i(e).length&&i(t).label?(s(),c("div",{key:0,class:D(["VPNavScreenTranslations",{open:n.value}])},[m("button",{class:"title",onClick:o},[l[0]||(l[0]=m("span",{class:"vpi-languages icon lang"},null,-1)),se(" "+A(i(t).label)+" ",1),l[1]||(l[1]=m("span",{class:"vpi-chevron-down icon chevron"},null,-1))]),m("ul",qs,[(s(!0),c(M,null,G(i(e),d=>(s(),c("li",{key:d.link,class:"item"},[b(Z,{class:"link",href:d.link},{default:f(()=>[se(A(d.text),1)]),_:2},1032,["href"])]))),128))])],2)):h("",!0)}}),Ys=y(Ks,[["__scopeId","data-v-cc4f7a1b"]]),Js={class:"container"},Xs=_({__name:"VPNavScreen",props:{open:{type:Boolean}},setup(a){const e=N(null),t=tt($e?document.body:null);return(n,o)=>(s(),g(De,{name:"fade",onEnter:o[0]||(o[0]=r=>t.value=!0),onAfterLeave:o[1]||(o[1]=r=>t.value=!1)},{default:f(()=>[n.open?(s(),c("div",{key:0,class:"VPNavScreen",ref_key:"screen",ref:e,id:"VPNavScreen"},[m("div",Js,[u(n.$slots,"nav-screen-content-before",{},void 0,!0),b(Rs,{class:"menu"}),b(Ys,{class:"translations"}),b(Ps,{class:"appearance"}),b(Ws,{class:"social-links"}),u(n.$slots,"nav-screen-content-after",{},void 0,!0)])],512)):h("",!0)]),_:3}))}}),Zs=y(Xs,[["__scopeId","data-v-005bb9a2"]]),Qs={key:0,class:"VPNav"},xs=_({__name:"VPNav",setup(a){const{isScreenOpen:e,closeScreen:t,toggleScreen:n}=Yo(),{frontmatter:o}=S(),r=E(()=>o.value.navbar!==!1);return nt("close-screen",t),be(()=>{$e&&document.documentElement.classList.toggle("hide-nav",!r.value)}),(l,d)=>r.value?(s(),c("header",Qs,[b(ys,{"is-screen-open":i(e),onToggleScreen:i(n)},{"nav-bar-title-before":f(()=>[u(l.$slots,"nav-bar-title-before",{},void 0,!0)]),"nav-bar-title-after":f(()=>[u(l.$slots,"nav-bar-title-after",{},void 0,!0)]),"nav-bar-content-before":f(()=>[u(l.$slots,"nav-bar-content-before",{},void 0,!0)]),"nav-bar-content-after":f(()=>[u(l.$slots,"nav-bar-content-after",{},void 0,!0)]),_:3},8,["is-screen-open","onToggleScreen"]),b(Zs,{open:i(e)},{"nav-screen-content-before":f(()=>[u(l.$slots,"nav-screen-content-before",{},void 0,!0)]),"nav-screen-content-after":f(()=>[u(l.$slots,"nav-screen-content-after",{},void 0,!0)]),_:3},8,["open"])])):h("",!0)}}),er=y(xs,[["__scopeId","data-v-2877b773"]]),tr=["role","tabindex"],nr={key:1,class:"items"},or=_({__name:"VPSidebarItem",props:{item:{},depth:{}},setup(a){const e=a,{collapsed:t,collapsible:n,isLink:o,isActiveLink:r,hasActiveLink:l,hasChildren:d,toggle:p}=Xt(E(()=>e.item)),$=E(()=>d.value?"section":"div"),V=E(()=>o.value?"a":"div"),k=E(()=>d.value?e.depth+2===7?"p":`h${e.depth+2}`:"p"),L=E(()=>o.value?void 0:"button"),F=E(()=>[[`level-${e.depth}`],{collapsible:n.value},{collapsed:t.value},{"is-link":o.value},{"is-active":r.value},{"has-active":l.value}]);function C(B){"key"in B&&B.key!=="Enter"||!e.item.link&&p()}function z(){e.item.link&&p()}return(B,q)=>{const Y=ee("VPSidebarItem",!0);return s(),g(X($.value),{class:D(["VPSidebarItem",F.value])},{default:f(()=>[B.item.text?(s(),c("div",ae({key:0,class:"item",role:L.value},Tt(B.item.items?{click:C,keydown:C}:{},!0),{tabindex:B.item.items&&0}),[q[1]||(q[1]=m("div",{class:"indicator"},null,-1)),B.item.link?(s(),g(Z,{key:0,tag:V.value,class:"link",href:B.item.link,rel:B.item.rel,target:B.item.target},{default:f(()=>[(s(),g(X(k.value),{class:"text",innerHTML:B.item.text},null,8,["innerHTML"]))]),_:1},8,["tag","href","rel","target"])):(s(),g(X(k.value),{key:1,class:"text",innerHTML:B.item.text},null,8,["innerHTML"])),B.item.collapsed!=null&&B.item.items&&B.item.items.length?(s(),c("div",{key:2,class:"caret",role:"button","aria-label":"toggle section",onClick:z,onKeydown:wt(z,["enter"]),tabindex:"0"},q[0]||(q[0]=[m("span",{class:"vpi-chevron-right caret-icon"},null,-1)]),32)):h("",!0)],16,tr)):h("",!0),B.item.items&&B.item.items.length?(s(),c("div",nr,[B.depth<5?(s(!0),c(M,{key:0},G(B.item.items,O=>(s(),g(Y,{key:O.text,item:O,depth:B.depth+1},null,8,["item","depth"]))),128)):h("",!0)])):h("",!0)]),_:1},8,["class"])}}}),ar=y(or,[["__scopeId","data-v-c5627c3b"]]),sr=_({__name:"VPSidebarGroup",props:{items:{}},setup(a){const e=N(!0);let t=null;return x(()=>{t=setTimeout(()=>{t=null,e.value=!1},300)}),Ct(()=>{t!=null&&(clearTimeout(t),t=null)}),(n,o)=>(s(!0),c(M,null,G(n.items,r=>(s(),c("div",{key:r.text,class:D(["group",{"no-transition":e.value}])},[b(ar,{item:r,depth:0},null,8,["item"])],2))),128))}}),rr=y(sr,[["__scopeId","data-v-6081356c"]]),ir={class:"nav",id:"VPSidebarNav","aria-labelledby":"sidebar-aria-label",tabindex:"-1"},lr=_({__name:"VPSidebar",props:{open:{type:Boolean}},setup(a){const{sidebarGroups:e,hasSidebar:t}=te(),n=a,o=N(null),r=tt($e?document.body:null);Q([n,o],()=>{var d;n.open?(r.value=!0,(d=o.value)==null||d.focus()):r.value=!1},{immediate:!0,flush:"post"});const l=N(0);return Q(e,()=>{l.value+=1},{deep:!0}),(d,p)=>i(t)?(s(),c("aside",{key:0,class:D(["VPSidebar",{open:d.open}]),ref_key:"navEl",ref:o,onClick:p[0]||(p[0]=Ft(()=>{},["stop"]))},[p[2]||(p[2]=m("div",{class:"curtain"},null,-1)),m("nav",ir,[p[1]||(p[1]=m("span",{class:"visually-hidden",id:"sidebar-aria-label"}," Sidebar Navigation ",-1)),u(d.$slots,"sidebar-nav-before",{},void 0,!0),(s(),g(rr,{items:i(e),key:l.value},null,8,["items"])),u(d.$slots,"sidebar-nav-after",{},void 0,!0)])],2)):h("",!0)}}),ur=y(lr,[["__scopeId","data-v-21179b8d"]]),cr=_({__name:"VPSkipLink",setup(a){const{theme:e}=S(),t=de(),n=N();Q(()=>t.path,()=>n.value.focus());function o({target:r}){const l=document.getElementById(decodeURIComponent(r.hash).slice(1));if(l){const d=()=>{l.removeAttribute("tabindex"),l.removeEventListener("blur",d)};l.setAttribute("tabindex","-1"),l.addEventListener("blur",d),l.focus(),window.scrollTo(0,0)}}return(r,l)=>(s(),c(M,null,[m("span",{ref_key:"backToTop",ref:n,tabindex:"-1"},null,512),m("a",{href:"#VPContent",class:"VPSkipLink visually-hidden",onClick:o},A(i(e).skipToContentLabel||"Skip to content"),1)],64))}}),dr=y(cr,[["__scopeId","data-v-bec96377"]]),mr=_({__name:"Layout",setup(a){const{isOpen:e,open:t,close:n}=te(),o=de();Q(()=>o.path,n),Jt(e,n);const{frontmatter:r}=S(),l=Bt(),d=E(()=>!!l["home-hero-image"]);return nt("hero-image-slot-exists",d),(p,$)=>{const V=ee("Content");return i(r).layout!==!1?(s(),c("div",{key:0,class:D(["Layout",i(r).pageClass])},[u(p.$slots,"layout-top",{},void 0,!0),b(dr),b(It,{class:"backdrop",show:i(e),onClick:i(n)},null,8,["show","onClick"]),b(er,null,{"nav-bar-title-before":f(()=>[u(p.$slots,"nav-bar-title-before",{},void 0,!0)]),"nav-bar-title-after":f(()=>[u(p.$slots,"nav-bar-title-after",{},void 0,!0)]),"nav-bar-content-before":f(()=>[u(p.$slots,"nav-bar-content-before",{},void 0,!0)]),"nav-bar-content-after":f(()=>[u(p.$slots,"nav-bar-content-after",{},void 0,!0)]),"nav-screen-content-before":f(()=>[u(p.$slots,"nav-screen-content-before",{},void 0,!0)]),"nav-screen-content-after":f(()=>[u(p.$slots,"nav-screen-content-after",{},void 0,!0)]),_:3}),b(Ko,{open:i(e),onOpenMenu:i(t)},null,8,["open","onOpenMenu"]),b(ur,{open:i(e)},{"sidebar-nav-before":f(()=>[u(p.$slots,"sidebar-nav-before",{},void 0,!0)]),"sidebar-nav-after":f(()=>[u(p.$slots,"sidebar-nav-after",{},void 0,!0)]),_:3},8,["open"]),b(Fo,null,{"page-top":f(()=>[u(p.$slots,"page-top",{},void 0,!0)]),"page-bottom":f(()=>[u(p.$slots,"page-bottom",{},void 0,!0)]),"not-found":f(()=>[u(p.$slots,"not-found",{},void 0,!0)]),"home-hero-before":f(()=>[u(p.$slots,"home-hero-before",{},void 0,!0)]),"home-hero-info-before":f(()=>[u(p.$slots,"home-hero-info-before",{},void 0,!0)]),"home-hero-info":f(()=>[u(p.$slots,"home-hero-info",{},void 0,!0)]),"home-hero-info-after":f(()=>[u(p.$slots,"home-hero-info-after",{},void 0,!0)]),"home-hero-actions-after":f(()=>[u(p.$slots,"home-hero-actions-after",{},void 0,!0)]),"home-hero-image":f(()=>[u(p.$slots,"home-hero-image",{},void 0,!0)]),"home-hero-after":f(()=>[u(p.$slots,"home-hero-after",{},void 0,!0)]),"home-features-before":f(()=>[u(p.$slots,"home-features-before",{},void 0,!0)]),"home-features-after":f(()=>[u(p.$slots,"home-features-after",{},void 0,!0)]),"doc-footer-before":f(()=>[u(p.$slots,"doc-footer-before",{},void 0,!0)]),"doc-before":f(()=>[u(p.$slots,"doc-before",{},void 0,!0)]),"doc-after":f(()=>[u(p.$slots,"doc-after",{},void 0,!0)]),"doc-top":f(()=>[u(p.$slots,"doc-top",{},void 0,!0)]),"doc-bottom":f(()=>[u(p.$slots,"doc-bottom",{},void 0,!0)]),"aside-top":f(()=>[u(p.$slots,"aside-top",{},void 0,!0)]),"aside-bottom":f(()=>[u(p.$slots,"aside-bottom",{},void 0,!0)]),"aside-outline-before":f(()=>[u(p.$slots,"aside-outline-before",{},void 0,!0)]),"aside-outline-after":f(()=>[u(p.$slots,"aside-outline-after",{},void 0,!0)]),"aside-ads-before":f(()=>[u(p.$slots,"aside-ads-before",{},void 0,!0)]),"aside-ads-after":f(()=>[u(p.$slots,"aside-ads-after",{},void 0,!0)]),_:3}),b(Io),u(p.$slots,"layout-bottom",{},void 0,!0)],2)):(s(),g(V,{key:1}))}}}),pr=y(mr,[["__scopeId","data-v-eb4c655e"]]),fr={Layout:pr},vr={class:"ad"},hr=["src","onClick"],_r={__name:"layout",setup(a){const{Layout:e}=fr,t=Nt({list:[],toLink(n){n.link?window.open(n.link):window.open(`https://cool-js.com/ad/${n.id}`)},get(){fetch("https://service.cool-js.com/api/app/info/ad/list",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({channel:1})}).then(n=>n.json()).then(n=>{this.list=n.data})}});return x(()=>{t.get()}),(n,o)=>(s(),g(i(e),null,{"sidebar-nav-before":f(()=>[m("div",vr,[(s(!0),c(M,null,G(t.list,r=>(s(),c("img",{key:r,src:r.pic,onClick:l=>t.toLink(r)},null,8,hr))),128))])]),_:1}))}},gr=y(_r,[["__scopeId","data-v-6a8df82f"]]);/*! medium-zoom 1.1.0 | MIT License | https://github.com/francoischalifour/medium-zoom */var re=Object.assign||function(a){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(a[n]=t[n])}return a},ve=function(e){return e.tagName==="IMG"},br=function(e){return NodeList.prototype.isPrototypeOf(e)},_e=function(e){return e&&e.nodeType===1},Je=function(e){var t=e.currentSrc||e.src;return t.substr(-4).toLowerCase()===".svg"},Xe=function(e){try{return Array.isArray(e)?e.filter(ve):br(e)?[].slice.call(e).filter(ve):_e(e)?[e].filter(ve):typeof e=="string"?[].slice.call(document.querySelectorAll(e)).filter(ve):[]}catch{throw new TypeError(`The provided selector is invalid.
Expects a CSS selector, a Node element, a NodeList or an array.
See: https://github.com/francoischalifour/medium-zoom`)}},kr=function(e){var t=document.createElement("div");return t.classList.add("medium-zoom-overlay"),t.style.background=e,t},yr=function(e){var t=e.getBoundingClientRect(),n=t.top,o=t.left,r=t.width,l=t.height,d=e.cloneNode(),p=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0,$=window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0;return d.removeAttribute("id"),d.style.position="absolute",d.style.top=n+p+"px",d.style.left=o+$+"px",d.style.width=r+"px",d.style.height=l+"px",d.style.transform="",d},ue=function(e,t){var n=re({bubbles:!1,cancelable:!1,detail:void 0},t);if(typeof window.CustomEvent=="function")return new CustomEvent(e,n);var o=document.createEvent("CustomEvent");return o.initCustomEvent(e,n.bubbles,n.cancelable,n.detail),o},$r=function a(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=window.Promise||function(P){function T(){}P(T,T)},o=function(P){var T=P.target;if(T===pe){C();return}O.indexOf(T)!==-1&&z({target:T})},r=function(){if(!(J||!v.original)){var P=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;Math.abs(qe-P)>I.scrollOffset&&setTimeout(C,150)}},l=function(P){var T=P.key||P.keyCode;(T==="Escape"||T==="Esc"||T===27)&&C()},d=function(){var P=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},T=P;if(P.background&&(pe.style.background=P.background),P.container&&P.container instanceof Object&&(T.container=re({},I.container,P.container)),P.template){var H=_e(P.template)?P.template:document.querySelector(P.template);T.template=H}return I=re({},I,T),O.forEach(function(j){j.dispatchEvent(ue("medium-zoom:update",{detail:{zoom:U}}))}),U},p=function(){var P=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return a(re({},I,P))},$=function(){for(var P=arguments.length,T=Array(P),H=0;H<P;H++)T[H]=arguments[H];var j=T.reduce(function(w,W){return[].concat(w,Xe(W))},[]);return j.filter(function(w){return O.indexOf(w)===-1}).forEach(function(w){O.push(w),w.classList.add("medium-zoom-image")}),ne.forEach(function(w){var W=w.type,K=w.listener,le=w.options;j.forEach(function(oe){oe.addEventListener(W,K,le)})}),U},V=function(){for(var P=arguments.length,T=Array(P),H=0;H<P;H++)T[H]=arguments[H];v.zoomed&&C();var j=T.length>0?T.reduce(function(w,W){return[].concat(w,Xe(W))},[]):O;return j.forEach(function(w){w.classList.remove("medium-zoom-image"),w.dispatchEvent(ue("medium-zoom:detach",{detail:{zoom:U}}))}),O=O.filter(function(w){return j.indexOf(w)===-1}),U},k=function(P,T){var H=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return O.forEach(function(j){j.addEventListener("medium-zoom:"+P,T,H)}),ne.push({type:"medium-zoom:"+P,listener:T,options:H}),U},L=function(P,T){var H=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return O.forEach(function(j){j.removeEventListener("medium-zoom:"+P,T,H)}),ne=ne.filter(function(j){return!(j.type==="medium-zoom:"+P&&j.listener.toString()===T.toString())}),U},F=function(){var P=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},T=P.target,H=function(){var w={width:document.documentElement.clientWidth,height:document.documentElement.clientHeight,left:0,top:0,right:0,bottom:0},W=void 0,K=void 0;if(I.container)if(I.container instanceof Object)w=re({},w,I.container),W=w.width-w.left-w.right-I.margin*2,K=w.height-w.top-w.bottom-I.margin*2;else{var le=_e(I.container)?I.container:document.querySelector(I.container),oe=le.getBoundingClientRect(),Le=oe.width,ut=oe.height,ct=oe.left,dt=oe.top;w=re({},w,{width:Le,height:ut,left:ct,top:dt})}W=W||w.width-I.margin*2,K=K||w.height-I.margin*2;var ce=v.zoomedHd||v.original,mt=Je(ce)?W:ce.naturalWidth||W,pt=Je(ce)?K:ce.naturalHeight||K,fe=ce.getBoundingClientRect(),ft=fe.top,vt=fe.left,Pe=fe.width,Se=fe.height,ht=Math.min(Math.max(Pe,mt),W)/Pe,_t=Math.min(Math.max(Se,pt),K)/Se,Ve=Math.min(ht,_t),gt=(-vt+(W-Pe)/2+I.margin+w.left)/Ve,bt=(-ft+(K-Se)/2+I.margin+w.top)/Ve,Ke="scale("+Ve+") translate3d("+gt+"px, "+bt+"px, 0)";v.zoomed.style.transform=Ke,v.zoomedHd&&(v.zoomedHd.style.transform=Ke)};return new n(function(j){if(T&&O.indexOf(T)===-1){j(U);return}var w=function Le(){J=!1,v.zoomed.removeEventListener("transitionend",Le),v.original.dispatchEvent(ue("medium-zoom:opened",{detail:{zoom:U}})),j(U)};if(v.zoomed){j(U);return}if(T)v.original=T;else if(O.length>0){var W=O;v.original=W[0]}else{j(U);return}if(v.original.dispatchEvent(ue("medium-zoom:open",{detail:{zoom:U}})),qe=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0,J=!0,v.zoomed=yr(v.original),document.body.appendChild(pe),I.template){var K=_e(I.template)?I.template:document.querySelector(I.template);v.template=document.createElement("div"),v.template.appendChild(K.content.cloneNode(!0)),document.body.appendChild(v.template)}if(v.original.parentElement&&v.original.parentElement.tagName==="PICTURE"&&v.original.currentSrc&&(v.zoomed.src=v.original.currentSrc),document.body.appendChild(v.zoomed),window.requestAnimationFrame(function(){document.body.classList.add("medium-zoom--opened")}),v.original.classList.add("medium-zoom-image--hidden"),v.zoomed.classList.add("medium-zoom-image--opened"),v.zoomed.addEventListener("click",C),v.zoomed.addEventListener("transitionend",w),v.original.getAttribute("data-zoom-src")){v.zoomedHd=v.zoomed.cloneNode(),v.zoomedHd.removeAttribute("srcset"),v.zoomedHd.removeAttribute("sizes"),v.zoomedHd.removeAttribute("loading"),v.zoomedHd.src=v.zoomed.getAttribute("data-zoom-src"),v.zoomedHd.onerror=function(){clearInterval(le),console.warn("Unable to reach the zoom image target "+v.zoomedHd.src),v.zoomedHd=null,H()};var le=setInterval(function(){v.zoomedHd.complete&&(clearInterval(le),v.zoomedHd.classList.add("medium-zoom-image--opened"),v.zoomedHd.addEventListener("click",C),document.body.appendChild(v.zoomedHd),H())},10)}else if(v.original.hasAttribute("srcset")){v.zoomedHd=v.zoomed.cloneNode(),v.zoomedHd.removeAttribute("sizes"),v.zoomedHd.removeAttribute("loading");var oe=v.zoomedHd.addEventListener("load",function(){v.zoomedHd.removeEventListener("load",oe),v.zoomedHd.classList.add("medium-zoom-image--opened"),v.zoomedHd.addEventListener("click",C),document.body.appendChild(v.zoomedHd),H()})}else H()})},C=function(){return new n(function(P){if(J||!v.original){P(U);return}var T=function H(){v.original.classList.remove("medium-zoom-image--hidden"),document.body.removeChild(v.zoomed),v.zoomedHd&&document.body.removeChild(v.zoomedHd),document.body.removeChild(pe),v.zoomed.classList.remove("medium-zoom-image--opened"),v.template&&document.body.removeChild(v.template),J=!1,v.zoomed.removeEventListener("transitionend",H),v.original.dispatchEvent(ue("medium-zoom:closed",{detail:{zoom:U}})),v.original=null,v.zoomed=null,v.zoomedHd=null,v.template=null,P(U)};J=!0,document.body.classList.remove("medium-zoom--opened"),v.zoomed.style.transform="",v.zoomedHd&&(v.zoomedHd.style.transform=""),v.template&&(v.template.style.transition="opacity 150ms",v.template.style.opacity=0),v.original.dispatchEvent(ue("medium-zoom:close",{detail:{zoom:U}})),v.zoomed.addEventListener("transitionend",T)})},z=function(){var P=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},T=P.target;return v.original?C():F({target:T})},B=function(){return I},q=function(){return O},Y=function(){return v.original},O=[],ne=[],J=!1,qe=0,I=t,v={original:null,zoomed:null,zoomedHd:null,template:null};Object.prototype.toString.call(e)==="[object Object]"?I=e:(e||typeof e=="string")&&$(e),I=re({margin:0,background:"#fff",scrollOffset:40,container:null,template:null},I);var pe=kr(I.background);document.addEventListener("click",o),document.addEventListener("keyup",l),document.addEventListener("scroll",r),window.addEventListener("resize",C);var U={open:F,close:C,toggle:z,update:d,clone:p,attach:$,detach:V,on:k,off:L,getOptions:B,getImages:q,getZoomedImage:Y};return U};function Er(a,e){e===void 0&&(e={});var t=e.insertAt;if(!(typeof document>"u")){var n=document.head||document.getElementsByTagName("head")[0],o=document.createElement("style");o.type="text/css",t==="top"&&n.firstChild?n.insertBefore(o,n.firstChild):n.appendChild(o),o.styleSheet?o.styleSheet.cssText=a:o.appendChild(document.createTextNode(a))}}var Lr=".medium-zoom-overlay{position:fixed;top:0;right:0;bottom:0;left:0;opacity:0;transition:opacity .3s;will-change:opacity}.medium-zoom--opened .medium-zoom-overlay{cursor:pointer;cursor:zoom-out;opacity:1}.medium-zoom-image{cursor:pointer;cursor:zoom-in;transition:transform .3s cubic-bezier(.2,0,.2,1)!important}.medium-zoom-image--hidden{visibility:hidden}.medium-zoom-image--opened{position:relative;cursor:pointer;cursor:zoom-out;will-change:transform}";Er(Lr);/*! Element Plus v2.9.3 */var Pr={name:"zh-cn",el:{breadcrumb:{label:"面包屑"},colorpicker:{confirm:"确定",clear:"清空",defaultLabel:"颜色选择器",description:"当前颜色 {color}，按 Enter 键选择新颜色",alphaLabel:"选择透明度的值"},datepicker:{now:"此刻",today:"今天",cancel:"取消",clear:"清空",confirm:"确定",dateTablePrompt:"使用方向键与 Enter 键可选择日期",monthTablePrompt:"使用方向键与 Enter 键可选择月份",yearTablePrompt:"使用方向键与 Enter 键可选择年份",selectedDate:"已选日期",selectDate:"选择日期",selectTime:"选择时间",startDate:"开始日期",startTime:"开始时间",endDate:"结束日期",endTime:"结束时间",prevYear:"前一年",nextYear:"后一年",prevMonth:"上个月",nextMonth:"下个月",year:"年",month1:"1 月",month2:"2 月",month3:"3 月",month4:"4 月",month5:"5 月",month6:"6 月",month7:"7 月",month8:"8 月",month9:"9 月",month10:"10 月",month11:"11 月",month12:"12 月",weeks:{sun:"日",mon:"一",tue:"二",wed:"三",thu:"四",fri:"五",sat:"六"},weeksFull:{sun:"星期日",mon:"星期一",tue:"星期二",wed:"星期三",thu:"星期四",fri:"星期五",sat:"星期六"},months:{jan:"一月",feb:"二月",mar:"三月",apr:"四月",may:"五月",jun:"六月",jul:"七月",aug:"八月",sep:"九月",oct:"十月",nov:"十一月",dec:"十二月"}},inputNumber:{decrease:"减少数值",increase:"增加数值"},select:{loading:"加载中",noMatch:"无匹配数据",noData:"无数据",placeholder:"请选择"},dropdown:{toggleDropdown:"切换下拉选项"},mention:{loading:"加载中"},cascader:{noMatch:"无匹配数据",loading:"加载中",placeholder:"请选择",noData:"暂无数据"},pagination:{goto:"前往",pagesize:"条/页",total:"共 {total} 条",pageClassifier:"页",page:"页",prev:"上一页",next:"下一页",currentPage:"第 {pager} 页",prevPages:"向前 {pager} 页",nextPages:"向后 {pager} 页",deprecationWarning:"你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档"},dialog:{close:"关闭此对话框"},drawer:{close:"关闭此对话框"},messagebox:{title:"提示",confirm:"确定",cancel:"取消",error:"输入的数据不合法!",close:"关闭此对话框"},upload:{deleteTip:"按 delete 键可删除",delete:"删除",preview:"查看图片",continue:"继续上传"},slider:{defaultLabel:"滑块介于 {min} 至 {max}",defaultRangeStartLabel:"选择起始值",defaultRangeEndLabel:"选择结束值"},table:{emptyText:"暂无数据",confirmFilter:"筛选",resetFilter:"重置",clearFilter:"全部",sumText:"合计"},tour:{next:"下一步",previous:"上一步",finish:"结束导览"},tree:{emptyText:"暂无数据"},transfer:{noMatch:"无匹配数据",noData:"无数据",titles:["列表 1","列表 2"],filterPlaceholder:"请输入搜索内容",noCheckedFormat:"共 {total} 项",hasCheckedFormat:"已选 {checked}/{total} 项"},image:{error:"加载失败"},pageHeader:{title:"返回"},popconfirm:{confirmButtonText:"确定",cancelButtonText:"取消"},carousel:{leftArrow:"上一张幻灯片",rightArrow:"下一张幻灯片",indicator:"幻灯片切换至索引 {index}"}}};const Sr=_({name:"code-demo",props:{title:String,opened:Boolean,showCode:{type:Boolean,default:!0}},setup(a){const e=N(a.opened);function t(n){e.value=n===void 0?!e.value:n}return{collapse:e,toCollapse:t,zhCn:Pr}}}),Vr={class:"code-demo"},Tr={key:0,class:"code-demo__header"},wr={class:"code-demo__title"},Cr={key:1,class:"code-demo__desc"},Fr={class:"code-demo__preview"},Br={key:2,class:"code-demo__container"},Nr={class:"code-demo__src"},Ar={key:3,class:"code-demo__footer"};function Dr(a,e,t,n,o,r){const l=ee("el-button"),d=ee("el-config-provider");return s(),g(d,{locale:a.zhCn},{default:f(()=>[m("div",Vr,[a.title?(s(),c("div",Tr,[m("h4",wr,A(a.title),1),a.showCode&&a.collapse?(s(),g(l,{key:0,round:"",type:"primary",onClick:e[0]||(e[0]=p=>a.toCollapse(!1))},{default:f(()=>e[2]||(e[2]=[se(" 收起 ")])),_:1})):h("",!0)])):h("",!0),a.$slots.desc?(s(),c("div",Cr,[u(a.$slots,"desc")])):h("",!0),m("div",Fr,[u(a.$slots,"preview")]),a.collapse?(s(),c("div",Br,[m("div",Nr,[u(a.$slots,"default")])])):h("",!0),a.showCode?(s(),c("div",Ar,[b(l,{round:"",onClick:e[1]||(e[1]=p=>a.toCollapse())},{default:f(()=>[se(A(a.collapse?"隐藏代码":"显示代码"),1)]),_:1})])):h("",!0)])]),_:3},8,["locale"])}const Ir=y(Sr,[["render",Dr]]),Hr={Layout:gr,enhanceApp({app:a}){a.component("code-demo",Ir),Fe(()=>import("./index.BjsZv4Lw.js").then(e=>e.e),__vite__mapDeps([2,3,1])).then(e=>{a.use(e.default)}),Fe(()=>import("./index.umd.min.DvzFVQw3.js").then(e=>e.i),__vite__mapDeps([4,2,3,1])).then(e=>{a.use(e.default,{style:{table:{autoHeight:!1}}})})},setup(){const a=de(),e=()=>{new $r("[data-zoomable]",{background:"var(--vp-c-bg)"})};x(()=>{e()}),Q(()=>a.path,()=>ze(()=>e()))}};export{Hr as R,Xa as c,S as u};
