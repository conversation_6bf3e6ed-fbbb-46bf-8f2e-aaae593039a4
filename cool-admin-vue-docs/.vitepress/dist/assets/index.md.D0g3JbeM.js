import{_ as l,a as t,o as i}from"./chunks/framework.BMq9nYrq.js";const n=JSON.parse('{"title":"","description":"","frontmatter":{"layout":"home","hero":{"name":"Cool Admin(Vue3)","text":"一个很酷的后台管理系统开发框架","tagline":"开源免费、Ai编码、流程编排、扩展插件、模块化","actions":[{"theme":"brand","text":"快速开始","link":"/src/guide/quick.md"},{"theme":"alt","text":"源码下载","link":"/src/introduce/src"}],"image":{"src":"/show/admin.jpg","alt":"cool-admin"}},"features":[{"title":"Ai编码+流程编排","icon":"<svg t=\\"1718630416527\\" class=\\"icon\\" viewBox=\\"0 0 1024 1024\\" version=\\"1.1\\" xmlns=\\"http://www.w3.org/2000/svg\\" p-id=\\"4109\\" width=\\"200\\" height=\\"200\\"><path d=\\"M219.424 18.304h530.272l201.152 219.424v768H219.424V18.304z\\" fill=\\"#FFFFFF\\" p-id=\\"4110\\"></path><path d=\\"M733.696 253.728V50.304H251.424v923.424h667.424v-720h-185.152z m217.152-16v768H219.424V18.304h530.272l201.152 219.424z m-58.08-16l-127.04-138.624v138.624h127.04z\\" fill=\\"#465F78\\" p-id=\\"4111\\"></path><path d=\\"M64 288a32 32 0 0 1 32-32h448a32 32 0 0 1 32 32v448a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V288zM640 416h224v37.344h-224V416zM640 509.344h224v37.312h-224v-37.312zM640 602.656h224V640h-224v-37.344z\\" fill=\\"#FA7553\\" p-id=\\"4112\\"></path><path d=\\"M168.416 672H105.152l96.384-279.264h76.096L373.92 672H310.656L240.64 456.544h-2.176L168.416 672z m35.712-109.76h70.88l14.944 46.08h-100.8l14.976-46.08zM417.824 671.616v-174.528h48.416v174.528h-48.416z m24.32-197.024a26.304 26.304 0 0 1-18.56-7.168 23.232 23.232 0 0 1-7.584-17.376 22.72 22.72 0 0 1 7.616-17.184 26.08 26.08 0 0 1 18.528-7.264c7.2 0 13.312 2.432 18.4 7.264 5.152 4.8 7.744 10.496 7.744 17.184a23.04 23.04 0 0 1-7.744 17.376 25.92 25.92 0 0 1-18.4 7.168z\\" fill=\\"#FFFFFF\\" p-id=\\"4113\\"></path></svg>","details":"框架结合Ai大模型微调训练，让Ai更懂框架，只需少量文字描述即可零代码实现部分功能。流程编排Ai开发必备神器，只需拖动配置，即可实现Ai开发的核心功能。"},{"title":"快速开发","icon":"<svg t=\\"1718630447066\\" class=\\"icon\\" viewBox=\\"0 0 1024 1024\\" version=\\"1.1\\" xmlns=\\"http://www.w3.org/2000/svg\\" p-id=\\"6941\\" width=\\"200\\" height=\\"200\\"><path d=\\"M400.495534 161.33256l303.660364 16.005568-82.106484 209.230484 160.530797 51.639485L339.739705 960.096509l22.300894-497.657349 38.454935-301.1066z\\" fill=\\"#56E5BE\\" p-id=\\"6942\\"></path><path d=\\"M439.247419 247.833894l277.202181 11.848277-78.840042 215.674284 160.530797 51.639485L381.965897 968.886208l18.826586-419.945714 38.454936-301.1066z\\" fill=\\"#50DDB8\\" p-id=\\"6943\\"></path><path d=\\"M727.763369 515.177358l64.734949 33.881916L441.623014 916.415265l13.451803-299.651548 38.425241-301.106601 153.225844 8.047327-46.799211 149.632757 127.836678 41.840158z\\" fill=\\"#42D3AD\\" p-id=\\"6944\\"></path><path d=\\"M338.106484 1024a54.638673 54.638673 0 0 1-25.65642-6.473495 53.985385 53.985385 0 0 1-27.735066-59.033523l73.316785-355.121679-126.975525-18.885976A54.401114 54.401114 0 0 1 185.474539 521.264818L269.00638 44.98782A54.549588 54.549588 0 0 1 324.83285 0.17817l462.201601 17.816958a54.430808 54.430808 0 0 1 45.581719 80.473263l-197.382206 360.823107 140.961837 12.056141a54.401114 54.401114 0 0 1 34.26795 92.232456l-433.545992 443.939218a54.133859 54.133859 0 0 1-38.811275 16.480687z m5.137223-53.450876z m-97.993272-444.384642l127.094305 18.915671a54.430808 54.430808 0 0 1 45.255074 64.824034L346.955574 952.613386l412.75954-422.677648-137.873564-11.877972a54.401114 54.401114 0 0 1-43.087345-80.176314l197.441596-360.496462-448.987357-17.460619z\\" fill=\\"#2E3138\\" p-id=\\"6945\\"></path></svg>","details":"通过Ai自动化编码，简单功能实现零代码开发，复杂的功能也只需少量代码配置即可。大大提高软件研发速度，快速迭代，快速试错降低成本，提高市场竞争力。"},{"title":"扩展插件","icon":"<svg t=\\"1718630540055\\" class=\\"icon\\" viewBox=\\"0 0 1024 1024\\" version=\\"1.1\\" xmlns=\\"http://www.w3.org/2000/svg\\" p-id=\\"17011\\" width=\\"200\\" height=\\"200\\"><path d=\\"M904.528 701.44V385.712c0-23.904-13.648-46.08-34.128-59.744L597.328 168.96c-20.48-11.952-47.776-11.952-68.256 0L256 325.968a68.832 68.832 0 0 0-34.128 59.744V701.44c0 23.888 13.648 46.08 34.128 59.728l273.072 157.024c20.48 11.936 47.776 11.936 68.256 0L870.4 761.168c20.48-13.648 34.128-35.84 34.128-59.728z\\" fill=\\"#07C160\\" opacity=\\".3\\" p-id=\\"17012\\"></path><path d=\\"M512 979.632c-17.072 0-32.432-3.408-47.792-11.936L141.648 779.936c-29.008-17.056-47.776-47.776-47.776-81.92V325.984c0-34.128 18.768-64.848 47.776-81.92l322.56-186.032a92.448 92.448 0 0 1 93.872 0l324.272 186.032c29.008 17.056 47.776 47.792 47.776 81.92v373.76c0 34.128-18.768 64.848-47.776 81.92L558.08 967.68c-13.648 6.816-29.008 11.936-46.08 11.936z m0-884.048c-6.832 0-15.36 1.696-20.48 5.12L167.248 288.432c-13.648 6.832-20.48 22.192-20.48 37.552v373.76c0 15.36 8.544 29.008 20.48 37.536l322.56 184.32a47.008 47.008 0 0 0 42.672 0l324.272-186.016c13.648-6.832 20.48-22.192 20.48-37.552V325.984c0-15.36-8.544-29.008-20.48-37.552L532.48 102.4c-5.12-5.12-13.648-6.816-20.48-6.816z\\" fill=\\"#07C160\\" p-id=\\"17013\\"></path><path d=\\"M512 785.056c-5.12 0-8.528-1.712-13.648-3.408l-85.344-49.488c-8.528-5.12-13.648-13.664-13.648-22.192v-97.28c0-8.528 5.12-17.072 13.648-22.192l85.344-49.488c8.528-5.12 17.056-5.12 25.6 0l85.328 49.488c8.528 5.12 13.648 13.664 13.648 22.192v97.28c0 8.528-5.12 17.072-13.648 22.192l-85.328 49.488c-3.424 1.696-6.832 3.408-11.952 3.408z m-59.728-90.448L512 728.736l59.728-34.128v-68.272L512 592.208l-59.728 34.128v68.272z m-97.28-139.952c-5.12 0-8.544-1.696-13.664-3.408L256 501.744c-8.528-5.12-13.648-13.648-13.648-22.176v-97.28c0-8.544 5.12-17.072 13.648-22.192l85.328-49.488c8.544-5.12 17.072-5.12 25.6 0l85.344 49.488c8.528 5.12 13.648 13.664 13.648 22.192v97.28c0 8.528-5.12 17.072-13.648 22.192l-85.344 49.488c-3.408 1.696-6.816 3.408-11.936 3.408zM296.96 465.92l59.728 34.128 59.744-34.144v-68.256l-59.744-34.144-59.728 34.144v68.256z m372.048 88.736c-5.12 0-8.528-1.696-13.648-3.408l-85.328-49.504c-8.544-5.12-13.664-13.648-13.664-22.176v-97.28c0-8.544 5.12-17.072 13.664-22.192l85.328-49.488c8.528-5.12 17.072-5.12 25.6 0l85.328 49.488c8.544 5.12 13.664 13.664 13.664 22.192v97.28c0 8.528-5.12 17.072-13.664 22.192l-85.328 49.488c-3.408 1.696-8.528 3.408-11.952 3.408zM609.28 465.92l59.728 34.128 59.744-34.144v-68.256l-59.744-34.144-59.728 34.144v68.256z\\" fill=\\"#07C160\\" p-id=\\"17014\\"></path><path d=\\"M583.68 457.392h-145.072c-13.648 0-25.6-11.952-25.6-25.6 0-13.664 11.952-25.6 25.6-25.6h145.072c13.648 0 25.6 11.936 25.6 25.6 0 13.648-10.24 25.6-25.6 25.6zM469.328 614.4a24.32 24.32 0 0 1-20.48-10.24l-59.728-81.92c-8.528-11.952-5.12-27.312 5.12-35.84 11.952-8.528 27.312-5.12 35.84 5.12l59.728 81.92c8.544 11.952 5.12 27.312-5.12 35.84-5.12 3.408-10.24 5.12-15.36 5.12z m93.872 0c-5.12 0-10.24-1.712-15.36-5.12-11.952-8.528-13.648-23.888-5.12-35.84l59.728-81.92c8.544-11.952 23.904-13.648 35.84-5.12 11.952 8.528 13.664 23.888 5.12 35.84l-59.728 81.92c-5.12 5.12-13.648 10.24-20.48 10.24z\\" fill=\\"#07C160\\" p-id=\\"17015\\"></path></svg>","details":"支持插件扩展，通过插件扩展功能，实现功能快速迭代。如支付、文件上传、短信等功能可以通过安装插件的方式动态继承，不需要或者需要更换，卸载重装即可，配置不用写在代码里，代码简洁清晰。"}]},"headers":[],"relativePath":"index.md","filePath":"index.md","lastUpdated":1738995647000}'),a={name:"index.md"};function c(h,e,d,p,s,o){return i(),t("div")}const m=l(a,[["render",c]]);export{n as __pageData,m as default};
