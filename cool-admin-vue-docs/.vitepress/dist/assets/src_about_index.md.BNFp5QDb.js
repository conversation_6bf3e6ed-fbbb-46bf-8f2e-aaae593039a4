import{_ as e,a as t,o as r,ah as o}from"./chunks/framework.BMq9nYrq.js";const i="/show/wechat.png",s="/show/douyin.png",n="/show/mp.png",b=JSON.parse('{"title":"交流合作","description":"","frontmatter":{},"headers":[],"relativePath":"src/about/index.md","filePath":"src/about/index.md","lastUpdated":1720922603000}'),l={name:"src/about/index.md"};function p(c,a,h,d,_,m){return r(),t("div",null,a[0]||(a[0]=[o('<h1 id="交流合作" tabindex="-1">交流合作 <a class="header-anchor" href="#交流合作" aria-label="Permalink to &quot;交流合作&quot;">​</a></h1><p>如果我们的项目对你有用，欢迎加入我们大家庭一起讨论，共创一些美好的事情。</p><h2 id="微信" tabindex="-1">微信 <a class="header-anchor" href="#微信" aria-label="Permalink to &quot;微信&quot;">​</a></h2><p>扫一扫加入微信开发交流群</p><p><img src="'+i+'" alt=""></p><h2 id="抖音" tabindex="-1">抖音 <a class="header-anchor" href="#抖音" aria-label="Permalink to &quot;抖音&quot;">​</a></h2><p>主要是做一些技术教程、技术分享、框架更新、插件分享等内容。你可以在这边了解到一些关于我们框架的最新动态，还可以了解到一些前沿的技术，特别是跟 Ai 相关的，有兴趣的可以关注我们。</p><p><img src="'+s+'" alt=""></p><h2 id="b-站" tabindex="-1">B 站 <a class="header-anchor" href="#b-站" aria-label="Permalink to &quot;B 站&quot;">​</a></h2><p><a href="https://space.bilibili.com/19293745" target="_blank" rel="noreferrer">COOL 团队哔哩哔哩官方账户</a></p><h2 id="公众号" tabindex="-1">公众号 <a class="header-anchor" href="#公众号" aria-label="Permalink to &quot;公众号&quot;">​</a></h2><p>COOL 的一些最新资讯、技术分享、框架更新、插件分享等</p><p><img src="'+n+'" alt=""></p>',13)]))}const f=e(l,[["render",p]]);export{b as __pageData,f as default};
