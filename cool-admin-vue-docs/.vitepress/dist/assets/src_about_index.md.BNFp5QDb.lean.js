import{_ as e,a as t,o as r,ah as o}from"./chunks/framework.BMq9nYrq.js";const i="/show/wechat.png",s="/show/douyin.png",n="/show/mp.png",b=JSON.parse('{"title":"交流合作","description":"","frontmatter":{},"headers":[],"relativePath":"src/about/index.md","filePath":"src/about/index.md","lastUpdated":1720922603000}'),l={name:"src/about/index.md"};function p(c,a,h,d,_,m){return r(),t("div",null,a[0]||(a[0]=[o("",13)]))}const f=e(l,[["render",p]]);export{b as __pageData,f as default};
