import{_ as i,a,o as t,ah as p}from"./chunks/framework.BMq9nYrq.js";const g=JSON.parse('{"title":"App.vue","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/App.vue.md","filePath":"src/guide/App.vue.md","lastUpdated":1738995647000}'),n={name:"src/guide/App.vue.md"};function l(e,s,h,k,E,r){return t(),a("div",null,s[0]||(s[0]=[p("",3)]))}const o=i(n,[["render",l]]);export{g as __pageData,o as default};
