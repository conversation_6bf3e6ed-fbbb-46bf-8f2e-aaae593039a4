import{_ as t,a as r,o,g as e,I as i}from"./chunks/framework.BMq9nYrq.js";const s="/show/code.png",h=JSON.parse('{"title":"Ai 编码","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/ai.md","filePath":"src/guide/ai.md","lastUpdated":1721064544000}'),n={name:"src/guide/ai.md"};function c(l,a,d,p,m,_){return o(),r("div",null,a[0]||(a[0]=[e("h1",{id:"ai-编码",tabindex:"-1"},[i("Ai 编码 "),e("a",{class:"header-anchor",href:"#ai-编码","aria-label":'Permalink to "Ai 编码"'},"​")],-1),e("p",null,[e("a",{href:"https://show.cool-admin.com/helper/ai-code",target:"_blank",rel:"noreferrer"},"预览地址 click!!")],-1),e("p",null,[e("img",{src:s,alt:"","data-zoomable":""})],-1)]))}const u=t(n,[["render",c]]);export{h as __pageData,u as default};
