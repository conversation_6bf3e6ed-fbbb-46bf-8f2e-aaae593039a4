import{_ as i,a,o as n,ah as t}from"./chunks/framework.BMq9nYrq.js";const l="/assets/dev-tools-proxy.DVj6_w1F.jpg",y=JSON.parse('{"title":"config","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/config.md","filePath":"src/guide/config.md","lastUpdated":1738995647000}'),h={name:"src/guide/config.md"};function p(k,s,e,E,r,d){return n(),a("div",null,s[0]||(s[0]=[t("",20)]))}const c=i(h,[["render",p]]);export{y as __pageData,c as default};
