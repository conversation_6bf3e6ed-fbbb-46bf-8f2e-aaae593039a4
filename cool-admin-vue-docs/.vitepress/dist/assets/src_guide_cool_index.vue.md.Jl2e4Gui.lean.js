import{_ as i,a,o as n,ah as t}from"./chunks/framework.BMq9nYrq.js";const E=JSON.parse('{"title":"全局组件","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/cool/index.vue.md","filePath":"src/guide/cool/index.vue.md","lastUpdated":1738995647000}'),e={name:"src/guide/cool/index.vue.md"};function p(l,s,h,k,d,r){return n(),a("div",null,s[0]||(s[0]=[t("",4)]))}const c=i(e,[["render",p]]);export{E as __pageData,c as default};
