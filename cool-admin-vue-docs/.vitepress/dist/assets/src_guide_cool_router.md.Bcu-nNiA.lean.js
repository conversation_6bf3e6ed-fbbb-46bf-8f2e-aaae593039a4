import{_ as i,a,o as t,ah as n}from"./chunks/framework.BMq9nYrq.js";const E=JSON.parse('{"title":"router","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/cool/router.md","filePath":"src/guide/cool/router.md","lastUpdated":1721096908000}'),e={name:"src/guide/cool/router.md"};function l(p,s,h,k,r,o){return t(),a("div",null,s[0]||(s[0]=[n("",3)]))}const c=i(e,[["render",l]]);export{E as __pageData,c as default};
