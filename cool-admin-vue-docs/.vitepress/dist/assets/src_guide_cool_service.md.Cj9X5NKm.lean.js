import{_ as i,a,o as n,ah as t}from"./chunks/framework.BMq9nYrq.js";const l="/images/service-console.gif",h="/images/service-tip.gif",c=JSON.parse('{"title":"service","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/cool/service.md","filePath":"src/guide/cool/service.md","lastUpdated":1738995647000}'),p={name:"src/guide/cool/service.md"};function e(k,s,E,d,r,o){return n(),a("div",null,s[0]||(s[0]=[t("",37)]))}const y=i(p,[["render",e]]);export{c as __pageData,y as default};
