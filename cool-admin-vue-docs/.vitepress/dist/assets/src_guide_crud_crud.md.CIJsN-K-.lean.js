import{a as h}from"./chunks/index.umd.min.DvzFVQw3.js";import{d as v,y as i,a as P,o as D,F as I,D as s,w as a,I as F,a2 as N,c as R,g as V,h as A,ah as w}from"./chunks/framework.BMq9nYrq.js";import{E as U}from"./chunks/index.CSHP5FlZ.js";import{E as f}from"./chunks/index.BjsZv4Lw.js";const W=v({__name:"form-btn",setup(x){const o=h.useUpsert({items:[{label:"姓名",prop:"name",component:{name:"el-input"}},{label:"创建时间",prop:"createTime",component:{name:"el-date-picker"}}]}),p=h.useTable({autoHeight:!1,columns:[{type:"selection"},{label:"姓名",prop:"name"},{label:"创建时间",prop:"createTime"},{type:"op"}]}),k=h.useCrud({service:"test"},e=>{e.refresh()}),c=h.useForm();function u(){var e;(e=c.value)==null||e.open({title:"自定义表单",props:{labelPosition:"top"},items:[{label:"获取 ref，打开后聚焦",prop:"name",component:{name:"el-input",props:{placeholder:"请填写昵称"}}},{label:"内嵌 cl-crud",component:{name:"slot-crud"}}]},[h.setFocus()])}return(e,n)=>{const t=i("el-button"),l=i("cl-refresh-btn"),y=i("cl-add-btn"),C=i("cl-multi-delete-btn"),E=i("cl-flex1"),m=i("cl-search-key"),d=i("cl-row"),r=i("cl-table"),g=i("cl-pagination"),B=i("cl-upsert"),b=i("cl-crud"),_=i("cl-form");return D(),P(I,null,[s(t,{onClick:u},{default:a(()=>n[0]||(n[0]=[F("自定义表单")])),_:1}),s(_,{ref_key:"Form",ref:c},{"slot-crud":a(()=>[s(b,{ref_key:"Crud",ref:k,padding:"0"},{default:a(()=>[s(d,null,{default:a(()=>[s(l),s(y),s(C),s(E),s(m)]),_:1}),s(d,null,{default:a(()=>[s(r,{ref_key:"Table",ref:p},null,512)]),_:1}),s(d,null,{default:a(()=>[s(E),s(g)]),_:1}),s(B,{ref_key:"Upsert",ref:o},null,512)]),_:1},512)]),_:1},512)],64)}}}),M={style:{padding:"0 10px"}},O=v({__name:"crud-demo",setup(x){const o=N({status:[{label:"启用",value:1},{label:"禁用",type:"danger",value:0}]}),p=h.useCrud({service:"test"},n=>{n.refresh()}),k=h.useUpsert({items:[{type:"tabs",props:{type:"card",labels:[{label:"基础",value:"base"},{label:"其他",value:"other"}]}},()=>({label:"姓名",prop:"name",required:!0,group:"base",component:{name:"el-input"}}),{label:"年龄",group:"base",prop:"age",component:{name:"el-input-number"}},{label:"身份证号",prop:"idCard",group:"other",component:{name:"el-input"}}],onInfo(n,{next:t,done:l}){l({...n,age:18})},onSubmit(n,{next:t,close:l,done:y}){t({...n,code:"615206459"})},onOpened(n){var t;((t=k.value)==null?void 0:t.mode)!="info"&&f.success("编辑中")},onClose(n,t){var l;if(((l=k.value)==null?void 0:l.mode)=="update"&&n=="close")return U.confirm("还没填完，确定关闭不？","提示",{type:"warning"}).then(()=>{t(),f.info("好吧")}).catch(()=>{f.success("请继续编辑")});t()}}),c=h.useTable({autoHeight:!1,columns:[{type:"selection",width:60},{label:"基础信息",prop:"baseInfo",children:[{label:"姓名",prop:"name",minWidth:120},{label:"存款(元)",prop:"wages",sortable:!0,minWidth:120}]},{label:"状态",prop:"status",dict:o.status,minWidth:100},{type:"op",width:240,buttons:["info","edit","delete"]}]});function u({data:n}){return["合计","",n.reduce((t,l)=>parseFloat(t+Number(l.wages)),0).toFixed(2)]}const e=h.useAdvSearch({items:[{label:"昵称",prop:"name",component:{name:"el-input"}}]});return(n,t)=>{const l=i("cl-refresh-btn"),y=i("cl-add-btn"),C=i("cl-multi-delete-btn"),E=i("cl-flex1"),m=i("cl-search-key"),d=i("cl-adv-btn"),r=i("cl-row"),g=i("el-descriptions-item"),B=i("el-descriptions"),b=i("cl-table"),_=i("cl-pagination"),T=i("cl-upsert"),j=i("cl-adv-search"),S=i("cl-crud");return D(),R(S,{ref_key:"Crud",ref:p,border:"",style:{"margin-top":"10px"}},{default:a(()=>[s(r,null,{default:a(()=>[s(l),s(y),s(C),s(W),s(E),s(m),s(d)]),_:1}),s(r,null,{default:a(()=>[s(b,{ref_key:"Table",ref:c,"show-summary":"","summary-method":u},{"column-detail":a(({scope:q})=>[V("div",M,[s(B,{column:3},{default:a(()=>[s(g,{label:"ID："},{default:a(()=>[F(A(q.row.id),1)]),_:2},1024),s(g,{label:"姓名："},{default:a(()=>[F(A(q.row.name),1)]),_:2},1024),s(g,{label:"存款："},{default:a(()=>[F(A(q.row.wages),1)]),_:2},1024)]),_:2},1024)])]),_:1},512)]),_:1}),s(r,null,{default:a(()=>[s(E),s(_)]),_:1}),s(T,{ref_key:"Upsert",ref:k},null,512),s(j,{ref_key:"AdvSearch",ref:e},null,512)]),_:1},512)}}}),K=JSON.parse('{"title":"cl-crud","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/crud/crud.md","filePath":"src/guide/crud/crud.md","lastUpdated":1738995647000}'),$={name:"src/guide/crud/crud.md"},L=v({...$,setup(x){return(o,p)=>(D(),P("div",null,[p[0]||(p[0]=w("",4)),s(O),p[1]||(p[1]=w("",39))]))}});export{K as __pageData,L as default};
