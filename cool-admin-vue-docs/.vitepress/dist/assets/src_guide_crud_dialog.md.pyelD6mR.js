import{E as u}from"./chunks/index.CSHP5FlZ.js";import{d as F,r as p,y as k,a as c,o as b,ah as g,D as a,g as i,w as l,I as s}from"./chunks/framework.BMq9nYrq.js";const v=JSON.parse('{"title":"cl-dialog","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/crud/dialog.md","filePath":"src/guide/crud/dialog.md","lastUpdated":1721064544000}'),m={name:"src/guide/crud/dialog.md"},A=F({...m,setup(f){const d=p(!1),h=p(!1);function y(n){u.confirm("是否关闭？","提示",{type:"warning"}).then(()=>{n()}).catch(()=>null)}return(n,t)=>{const r=k("el-button"),E=k("cl-dialog"),o=k("code-demo");return b(),c("div",null,[t[10]||(t[10]=g('<h1 id="cl-dialog" tabindex="-1">cl-dialog <a class="header-anchor" href="#cl-dialog" aria-label="Permalink to &quot;cl-dialog&quot;">​</a></h1><p>自定义对话框组件，该组件继承 <a href="https://element-plus.gitee.io/zh-CN/component/dialog.html" target="_blank" rel="noreferrer">el-dialog</a> 并享有它的参数和方法</p><h2 id="示例" tabindex="-1">示例 <a class="header-anchor" href="#示例" aria-label="Permalink to &quot;示例&quot;">​</a></h2><h3 id="基础用法" tabindex="-1">基础用法 <a class="header-anchor" href="#基础用法" aria-label="Permalink to &quot;基础用法&quot;">​</a></h3><p><code>v-model</code> 控制对话框的显示隐藏</p>',5)),a(o,null,{preview:l(()=>[a(r,{onClick:t[0]||(t[0]=e=>d.value=!0)},{default:l(()=>t[4]||(t[4]=[s("打开")])),_:1}),a(E,{title:"对话",modelValue:d.value,"onUpdate:modelValue":t[1]||(t[1]=e=>d.value=e)},{default:l(()=>t[5]||(t[5]=[i("p",null,"少年，我看你骨骼精奇，是万中无一的武学奇才，维护世界和平就靠你了",-1)])),_:1},8,["modelValue"])]),default:l(()=>[t[6]||(t[6]=i("div",{class:"language-html vp-adaptive-theme"},[i("button",{title:"Copy Code",class:"copy"}),i("span",{class:"lang"},"html"),i("pre",{class:"shiki shiki-themes github-light github-dark vp-code",tabindex:"0"},[i("code",null,[i("span",{class:"line"},[i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),i("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"cl-dialog"),i("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," title"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),i("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"对话"'),i("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," v-model"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),i("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"visible"'),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),s(`
`),i("span",{class:"line"},[i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"	<"),i("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"p"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">少年，我看你骨骼精奇，是万中无一的武学奇才，维护世界和平就靠你了</"),i("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"p"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),s(`
`),i("span",{class:"line"},[i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),i("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"cl-dialog"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")])])])],-1))]),_:1}),t[11]||(t[11]=i("h3",{id:"关闭前操作",tabindex:"-1"},[s("关闭前操作 "),i("a",{class:"header-anchor",href:"#关闭前操作","aria-label":'Permalink to "关闭前操作"'},"​")],-1)),t[12]||(t[12]=i("p",null,[s("添加 "),i("code",null,"before-close"),s(" 方法：")],-1)),t[13]||(t[13]=i("ul",null,[i("li",null,"done() 关闭")],-1)),a(o,null,{preview:l(()=>[a(r,{onClick:t[2]||(t[2]=e=>h.value=!0)},{default:l(()=>t[7]||(t[7]=[s("打开")])),_:1}),a(E,{title:"对话",modelValue:h.value,"onUpdate:modelValue":t[3]||(t[3]=e=>h.value=e),"before-close":y},{default:l(()=>t[8]||(t[8]=[i("p",null,"少年，我看你骨骼精奇，是万中无一的武学奇才，维护世界和平就靠你了",-1)])),_:1},8,["modelValue"])]),default:l(()=>[t[9]||(t[9]=i("div",{class:"language-html vp-adaptive-theme"},[i("button",{title:"Copy Code",class:"copy"}),i("span",{class:"lang"},"html"),i("pre",{class:"shiki shiki-themes github-light github-dark vp-code",tabindex:"0"},[i("code",null,[i("span",{class:"line"},[i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),i("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"cl-dialog"),i("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," title"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),i("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"对话"'),i("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," v-model"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),i("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"visible"'),i("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," :before-close"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),i("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"beforeClose"'),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),s(`
`),i("span",{class:"line"},[i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"	<"),i("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"p"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">少年，我看你骨骼精奇，是万中无一的武学奇才，维护世界和平就靠你了</"),i("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"p"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),s(`
`),i("span",{class:"line"},[i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),i("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"cl-dialog"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),s(`
`),i("span",{class:"line"}),s(`
`),i("span",{class:"line"},[i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),i("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),i("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," lang"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),i("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"ts"'),i("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," setup"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),s(`
`),i("span",{class:"line"},[i("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"	import"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { ref } "),i("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),i("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},' "vue"'),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),s(`
`),i("span",{class:"line"},[i("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"	import"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { ElMessageBox } "),i("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),i("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},' "element-plus"'),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),s(`
`),i("span",{class:"line"},[i("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"	const"),i("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}}," visible"),i("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}}," ="),i("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," ref"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),i("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"false"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},");")]),s(`
`),i("span",{class:"line"}),s(`
`),i("span",{class:"line"},[i("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"	function"),i("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," beforeClose"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),i("span",{style:{"--shiki-light":"#E36209","--shiki-dark":"#FFAB70"}},"done"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},") {")]),s(`
`),i("span",{class:"line"},[i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"		ElMessageBox."),i("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"confirm"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),i("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"是否关闭？"'),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", "),i("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"提示"'),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", {")]),s(`
`),i("span",{class:"line"},[i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"			type: "),i("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"warning"')]),s(`
`),i("span",{class:"line"},[i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"		})")]),s(`
`),i("span",{class:"line"},[i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"			."),i("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"then"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"(() "),i("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"=>"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," {")]),s(`
`),i("span",{class:"line"},[i("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"				done"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"();")]),s(`
`),i("span",{class:"line"},[i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"			})")]),s(`
`),i("span",{class:"line"},[i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"			."),i("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"catch"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"(() "),i("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"=>"),i("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}}," null"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},");")]),s(`
`),i("span",{class:"line"},[i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"	}")]),s(`
`),i("span",{class:"line"},[i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),i("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),i("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")])])])],-1))]),_:1}),t[14]||(t[14]=g('<h2 id="参数" tabindex="-1">参数 <a class="header-anchor" href="#参数" aria-label="Permalink to &quot;参数&quot;">​</a></h2><table tabindex="0"><thead><tr><th>参数</th><th>说明</th><th>类型</th><th>可选值</th><th>默认值</th></tr></thead><tbody><tr><td>v-model/modelValue</td><td>是否显示</td><td>boolean</td><td></td><td>false</td></tr><tr><td>title</td><td>标题</td><td>string</td><td></td><td>对话框</td></tr><tr><td>height</td><td>高度</td><td>string</td><td></td><td>auto</td></tr><tr><td>width</td><td>宽度</td><td>string</td><td></td><td>50%</td></tr><tr><td>fullscreen</td><td>是否全屏显示</td><td>boolean</td><td></td><td>false</td></tr><tr><td>controls</td><td>头部操作按钮</td><td>array</td><td></td><td>[&quot;fullscreen&quot;, &quot;close&quot;]</td></tr><tr><td>hideHeader</td><td>隐藏头部元素</td><td>boolean</td><td></td><td>false</td></tr><tr><td>keepAlive</td><td>是否缓存</td><td>boolean</td><td></td><td>false</td></tr><tr><td>draggable</td><td>能否拖拽</td><td>boolean</td><td></td><td>false</td></tr><tr><td>before-close</td><td>关闭前钩子</td><td>function(done)</td><td></td><td></td></tr><tr><td>custom-class</td><td>自定义样式名</td><td>string</td><td></td><td></td></tr></tbody></table><h2 id="事件" tabindex="-1">事件 <a class="header-anchor" href="#事件" aria-label="Permalink to &quot;事件&quot;">​</a></h2><table tabindex="0"><thead><tr><th>参数</th><th>说明</th></tr></thead><tbody><tr><td>open</td><td>打开的回调</td></tr><tr><td>opened</td><td>打开动画结束时的回调</td></tr><tr><td>close</td><td>关闭的回调</td></tr><tr><td>closed</td><td>关闭动画结束时的回调</td></tr></tbody></table><h2 id="插槽" tabindex="-1">插槽 <a class="header-anchor" href="#插槽" aria-label="Permalink to &quot;插槽&quot;">​</a></h2><table tabindex="0"><thead><tr><th>参数</th><th>说明</th></tr></thead><tbody><tr><td>default</td><td>内容区域</td></tr><tr><td>footer</td><td>底部区域</td></tr></tbody></table>',6))])}}});export{v as __pageData,A as default};
