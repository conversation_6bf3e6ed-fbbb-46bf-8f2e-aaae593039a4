import{_ as i,a,o as n,ah as l}from"./chunks/framework.BMq9nYrq.js";const g=JSON.parse('{"title":"模块/插件开发","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/dev.md","filePath":"src/guide/dev.md","lastUpdated":1738995647000}'),p={name:"src/guide/dev.md"};function h(t,s,k,e,E,d){return n(),a("div",null,s[0]||(s[0]=[l("",57)]))}const y=i(p,[["render",h]]);export{g as __pageData,y as default};
