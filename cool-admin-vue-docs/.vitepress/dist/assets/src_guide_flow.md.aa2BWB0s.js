import{_ as e}from"./chunks/flow.Dzy7_fvw.js";import{_ as t,a as i,o as r,ah as o}from"./chunks/framework.BMq9nYrq.js";const A=JSON.parse('{"title":"Ai 流程编排","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/flow.md","filePath":"src/guide/flow.md","lastUpdated":1721096908000}'),s={name:"src/guide/flow.md"};function p(l,a,c,_,d,n){return r(),i("div",null,a[0]||(a[0]=[o('<h1 id="ai-流程编排" tabindex="-1">Ai 流程编排 <a class="header-anchor" href="#ai-流程编排" aria-label="Permalink to &quot;Ai 流程编排&quot;">​</a></h1><p><a href="https://www.bilibili.com/video/BV1i1421r7ri/" target="_blank" rel="noreferrer">视频演示</a></p><p>开发 Ai 应用必备！！！</p><p>随着 Ai 的火热，很多应用希望接入 Ai 的能力，但是每次都是用代码手搓，容易把我们累死，而是也非常不灵活。</p><p>于是我们面向开发者和软件开发公司，做了一个 Ai 流程编排功能，结合知识库，通过拖动配置，很容易就能完成一个 Ai 的核心流程。</p><p>因此如果你需要开发 Ai 相关应用，这个可能会给你带来巨大的帮助。</p><p><img src="'+e+'" alt="流程编排" data-zoomable=""></p><div class="warning custom-block"><p class="custom-block-title">注意</p><p>Ai 流程编排+知识库需要单独购买，预售 999 元</p></div>',8)]))}const u=t(s,[["render",p]]);export{A as __pageData,u as default};
