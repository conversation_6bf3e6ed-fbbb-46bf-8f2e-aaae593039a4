import{_ as e}from"./chunks/flow.Dzy7_fvw.js";import{_ as t,a as i,o as r,ah as o}from"./chunks/framework.BMq9nYrq.js";const A=JSON.parse('{"title":"Ai 流程编排","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/flow.md","filePath":"src/guide/flow.md","lastUpdated":1721096908000}'),s={name:"src/guide/flow.md"};function p(l,a,c,_,d,n){return r(),i("div",null,a[0]||(a[0]=[o("",8)]))}const u=t(s,[["render",p]]);export{A as __pageData,u as default};
