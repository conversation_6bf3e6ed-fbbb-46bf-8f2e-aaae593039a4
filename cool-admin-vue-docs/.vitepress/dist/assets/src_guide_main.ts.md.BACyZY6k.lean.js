import{_ as i,a,o as n,ah as t}from"./chunks/framework.BMq9nYrq.js";const o=JSON.parse('{"title":"main.ts","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/main.ts.md","filePath":"src/guide/main.ts.md","lastUpdated":1721048877000}'),p={name:"src/guide/main.ts.md"};function h(k,s,l,e,E,r){return n(),a("div",null,s[0]||(s[0]=[t("",3)]))}const g=i(p,[["render",h]]);export{o as __pageData,g as default};
