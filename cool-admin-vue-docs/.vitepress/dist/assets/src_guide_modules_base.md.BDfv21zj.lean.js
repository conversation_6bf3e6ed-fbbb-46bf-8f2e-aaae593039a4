import{_ as t,a as i,o as a,ah as n}from"./chunks/framework.BMq9nYrq.js";const e="/images/code-json.png",h="/images/code-json-popover.png",g=JSON.parse('{"title":"base","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/modules/base.md","filePath":"src/guide/modules/base.md","lastUpdated":1721064544000}'),d={name:"src/guide/modules/base.md"};function l(p,s,r,k,E,o){return a(),i("div",null,s[0]||(s[0]=[n("",30)]))}const y=t(d,[["render",l]]);export{g as __pageData,y as default};
