import{_ as i,a,o as n,ah as t}from"./chunks/framework.BMq9nYrq.js";const h="/images/dict.png",g=JSON.parse('{"title":"dict","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/modules/dict.md","filePath":"src/guide/modules/dict.md","lastUpdated":1721048877000}'),l={name:"src/guide/modules/dict.md"};function p(k,s,e,E,d,r){return n(),a("div",null,s[0]||(s[0]=[t("",10)]))}const o=i(l,[["render",p]]);export{g as __pageData,o as default};
