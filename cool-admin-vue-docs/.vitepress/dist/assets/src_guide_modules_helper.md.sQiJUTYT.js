import{_ as a,a as r,o as i,ah as t}from"./chunks/framework.BMq9nYrq.js";const s="/images/quick-create-menu.jpg",o="/images/quick-create-permission.jpg",c="/images/plugin-vue.jpg",l="/images/ai-code-1.jpg",n="/images/ai-code-2.jpg",d="/images/ai-code-3.jpg",q=JSON.parse('{"title":"helper","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/modules/helper.md","filePath":"src/guide/modules/helper.md","lastUpdated":1721064544000}'),h={name:"src/guide/modules/helper.md"};function m(p,e,_,u,g,f){return i(),r("div",null,e[0]||(e[0]=[t('<h1 id="helper" tabindex="-1">helper <a class="header-anchor" href="#helper" aria-label="Permalink to &quot;helper&quot;">​</a></h1><p>开发辅助模块。</p><h2 id="快速创建菜单" tabindex="-1">快速创建菜单 <a class="header-anchor" href="#快速创建菜单" aria-label="Permalink to &quot;快速创建菜单&quot;">​</a></h2><img src="'+s+'"><h2 id="快速创建权限" tabindex="-1">快速创建权限 <a class="header-anchor" href="#快速创建权限" aria-label="Permalink to &quot;快速创建权限&quot;">​</a></h2><img src="'+o+'"><h2 id="前后端插件" tabindex="-1">前后端插件 <a class="header-anchor" href="#前后端插件" aria-label="Permalink to &quot;前后端插件&quot;">​</a></h2><img src="'+c+'"><h2 id="ai-编码" tabindex="-1">Ai 编码 <a class="header-anchor" href="#ai-编码" aria-label="Permalink to &quot;Ai 编码&quot;">​</a></h2><img src="'+l+'"><br><img src="'+n+'"><br><img src="'+d+'">',14)]))}const k=a(h,[["render",m]]);export{q as __pageData,k as default};
