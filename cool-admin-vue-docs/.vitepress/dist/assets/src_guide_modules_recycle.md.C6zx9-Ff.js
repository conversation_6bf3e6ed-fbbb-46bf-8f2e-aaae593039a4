import{_ as c,a,o as t,g as e,I as s}from"./chunks/framework.BMq9nYrq.js";const l="/images/recycle.jpg",y=JSON.parse('{"title":"recycle","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/modules/recycle.md","filePath":"src/guide/modules/recycle.md","lastUpdated":1721048877000}'),o={name:"src/guide/modules/recycle.md"};function d(n,r,i,m,p,u){return t(),a("div",null,r[0]||(r[0]=[e("h1",{id:"recycle",tabindex:"-1"},[s("recycle "),e("a",{class:"header-anchor",href:"#recycle","aria-label":'Permalink to "recycle"'},"​")],-1),e("p",null,"数据回收站",-1),e("img",{src:l},null,-1)]))}const f=c(o,[["render",d]]);export{y as __pageData,f as default};
