import{_ as d,a,o as e,ah as r}from"./chunks/framework.BMq9nYrq.js";const u=JSON.parse('{"title":"space","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/modules/space.md","filePath":"src/guide/modules/space.md","lastUpdated":1721048877000}'),l={name:"src/guide/modules/space.md"};function o(s,t,h,c,n,i){return e(),a("div",null,t[0]||(t[0]=[r('<h1 id="space" tabindex="-1">space <a class="header-anchor" href="#space" aria-label="Permalink to &quot;space&quot;">​</a></h1><p>文件、图片上传，文件空间</p><h2 id="cl-upload-space" tabindex="-1">cl-upload-space <a class="header-anchor" href="#cl-upload-space" aria-label="Permalink to &quot;cl-upload-space&quot;">​</a></h2><p>文件空间选择器</p><h3 id="参数" tabindex="-1">参数 <a class="header-anchor" href="#参数" aria-label="Permalink to &quot;参数&quot;">​</a></h3><table tabindex="0"><thead><tr><th>参数</th><th>说明</th><th>类型</th><th>可选值</th><th>默认值</th></tr></thead><tbody><tr><td>v-model/modelValue</td><td>图片地址</td><td>string</td><td></td><td></td></tr><tr><td>title</td><td>弹窗标题</td><td>string</td><td></td><td>文件空间</td></tr><tr><td>text</td><td>打开按钮文本</td><td>string</td><td></td><td>点击上传</td></tr><tr><td>accept</td><td>文件类型</td><td>string</td><td></td><td></td></tr><tr><td>limit</td><td>最大选择数量</td><td>number</td><td></td><td>9</td></tr><tr><td>showBtn</td><td>是否显示上传按钮</td><td>boolean</td><td></td><td>true</td></tr></tbody></table><h2 id="cl-upload-panel" tabindex="-1">cl-upload-panel <a class="header-anchor" href="#cl-upload-panel" aria-label="Permalink to &quot;cl-upload-panel&quot;">​</a></h2><p>文件管理面板</p><h3 id="参数-1" tabindex="-1">参数 <a class="header-anchor" href="#参数-1" aria-label="Permalink to &quot;参数&quot;">​</a></h3><table tabindex="0"><thead><tr><th>参数</th><th>说明</th><th>类型</th><th>可选值</th><th>默认值</th></tr></thead><tbody><tr><td>accept</td><td>文件类型</td><td>string</td><td></td><td></td></tr><tr><td>limit</td><td>最大选择数量</td><td>number</td><td></td><td>9</td></tr><tr><td>selectable</td><td>是否可选</td><td>boolean</td><td></td><td>false</td></tr></tbody></table>',10)]))}const b=d(l,[["render",o]]);export{u as __pageData,b as default};
