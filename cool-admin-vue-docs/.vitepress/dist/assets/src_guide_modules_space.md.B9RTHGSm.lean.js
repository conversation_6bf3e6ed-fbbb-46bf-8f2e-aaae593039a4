import{_ as d,a,o as e,ah as r}from"./chunks/framework.BMq9nYrq.js";const u=JSON.parse('{"title":"space","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/modules/space.md","filePath":"src/guide/modules/space.md","lastUpdated":1721048877000}'),l={name:"src/guide/modules/space.md"};function o(s,t,h,c,n,i){return e(),a("div",null,t[0]||(t[0]=[r("",10)]))}const b=d(l,[["render",o]]);export{u as __pageData,b as default};
