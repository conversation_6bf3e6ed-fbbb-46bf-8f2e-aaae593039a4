import{_ as t,a as s,o as r,g as a,I as o}from"./chunks/framework.BMq9nYrq.js";const d="/images/task.jpg",_=JSON.parse('{"title":"task","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/modules/task.md","filePath":"src/guide/modules/task.md","lastUpdated":1721048877000}'),n={name:"src/guide/modules/task.md"};function l(i,e,c,m,p,k){return r(),s("div",null,e[0]||(e[0]=[a("h1",{id:"task",tabindex:"-1"},[o("task "),a("a",{class:"header-anchor",href:"#task","aria-label":'Permalink to "task"'},"​")],-1),a("p",null,"任务管理",-1),a("img",{src:d},null,-1)]))}const f=t(n,[["render",l]]);export{_ as __pageData,f as default};
