import{_ as s,a,o as t,g as e,I as o}from"./chunks/framework.BMq9nYrq.js";const f=JSON.parse('{"title":"user","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/modules/user.md","filePath":"src/guide/modules/user.md","lastUpdated":1721064544000}'),d={name:"src/guide/modules/user.md"};function u(n,r,l,c,i,m){return t(),a("div",null,r[0]||(r[0]=[e("h1",{id:"user",tabindex:"-1"},[o("user "),e("a",{class:"header-anchor",href:"#user","aria-label":'Permalink to "user"'},"​")],-1),e("p",null,"用户模块",-1)]))}const _=s(d,[["render",u]]);export{f as __pageData,_ as default};
