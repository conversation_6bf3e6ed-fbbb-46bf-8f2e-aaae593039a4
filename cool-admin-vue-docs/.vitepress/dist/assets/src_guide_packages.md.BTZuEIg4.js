import{_ as a,a as i,o as e,ah as t}from"./chunks/framework.BMq9nYrq.js";const k=JSON.parse('{"title":"packages","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/packages.md","filePath":"src/guide/packages.md","lastUpdated":1722396272000}'),p={name:"src/guide/packages.md"};function n(l,s,c,r,d,h){return e(),i("div",null,s[0]||(s[0]=[t(`<h1 id="packages" tabindex="-1">packages <a class="header-anchor" href="#packages" aria-label="Permalink to &quot;packages&quot;">​</a></h1><p>npm 源码包。</p><h2 id="目录结构" tabindex="-1">目录结构 <a class="header-anchor" href="#目录结构" aria-label="Permalink to &quot;目录结构&quot;">​</a></h2><div class="language-js vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">packages</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    ├──crud</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    └──vite</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">-</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">plugin</span></span></code></pre></div><h2 id="crud" tabindex="-1">crud <a class="header-anchor" href="#crud" aria-label="Permalink to &quot;crud&quot;">​</a></h2><p><code>cl-crud</code> 的组件库，页面的快速 CRUD 都依赖于它。</p><p>如需要自定 crud，编辑文件 <code>src/plugins/crud/config.ts</code>：</p><div class="language-ts vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">ts</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// npm</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// import Crud, { locale, setFocus } from &quot;@cool-vue/crud&quot;;</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// import &quot;@cool-vue/crud/dist/index.css&quot;;</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 调试、自定义crud</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> Crud, { locale, setFocus } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;/~/crud/src&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;/~/crud/src/static/index.scss&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span></code></pre></div><h2 id="vite-plugin" tabindex="-1">vite-plugin <a class="header-anchor" href="#vite-plugin" aria-label="Permalink to &quot;vite-plugin&quot;">​</a></h2><p>vite 扩展插件，主要处理以下几点：</p><ul><li><p>注入虚拟模块 <code>virtual:eps</code> <code>virtual:ctx</code> <code>virtual:svg-register</code> <code>virtual:svg-icons</code> 等</p></li><li><p>生成 eps</p></li><li><p>解析 svg，使用 svgo 压缩</p></li><li><p>创建菜单文件</p></li><li><p>script 标签绑定值</p></li><li><p>生成 admin / uniapp 的上下文</p></li></ul>`,11)]))}const u=a(p,[["render",n]]);export{k as __pageData,u as default};
