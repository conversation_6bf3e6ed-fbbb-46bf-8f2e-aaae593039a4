import{_ as a,a as i,o as e,ah as t}from"./chunks/framework.BMq9nYrq.js";const k=JSON.parse('{"title":"packages","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/packages.md","filePath":"src/guide/packages.md","lastUpdated":1722396272000}'),p={name:"src/guide/packages.md"};function n(l,s,c,r,d,h){return e(),i("div",null,s[0]||(s[0]=[t("",11)]))}const u=a(p,[["render",n]]);export{k as __pageData,u as default};
