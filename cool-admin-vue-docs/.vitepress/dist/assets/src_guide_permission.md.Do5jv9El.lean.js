import{_ as i,a,o as n,ah as e}from"./chunks/framework.BMq9nYrq.js";const t="/images/service-permission.png",p="/images/permission.png",g=JSON.parse('{"title":"permission","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/permission.md","filePath":"src/guide/permission.md","lastUpdated":1721048877000}'),l={name:"src/guide/permission.md"};function h(r,s,k,d,o,c){return n(),a("div",null,s[0]||(s[0]=[e("",16)]))}const m=i(l,[["render",h]]);export{g as __pageData,m as default};
