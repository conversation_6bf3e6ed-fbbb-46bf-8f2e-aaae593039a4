import{_ as i,a,o as t,ah as n}from"./chunks/framework.BMq9nYrq.js";const l="/images/switch.png",h="/images/column-custom.png",y=JSON.parse('{"title":"Crud","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/plugins/crud.md","filePath":"src/guide/plugins/crud.md","lastUpdated":1738995647000}'),p={name:"src/guide/plugins/crud.md"};function k(e,s,d,E,r,g){return t(),a("div",null,s[0]||(s[0]=[n("",33)]))}const o=i(p,[["render",k]]);export{y as __pageData,o as default};
