import{_ as e,a as t,o,ah as s}from"./chunks/framework.BMq9nYrq.js";const r="/images/dev-tools-eps.jpg",i="/images/dev-tools-proxy.jpg",l="/images/dev-tools-dict.jpg",d="/images/dev-tools-account.jpg",f=JSON.parse('{"title":"开发工具","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/plugins/dev-tools.md","filePath":"src/guide/plugins/dev-tools.md","lastUpdated":*************}'),n={name:"src/guide/plugins/dev-tools.md"};function c(p,a,_,h,m,u){return o(),t("div",null,a[0]||(a[0]=[s('<h1 id="开发工具" tabindex="-1">开发工具 <a class="header-anchor" href="#开发工具" aria-label="Permalink to &quot;开发工具&quot;">​</a></h1><p>只在开发环境生效，方便开发调试。</p><h2 id="接口管理" tabindex="-1">接口管理 <a class="header-anchor" href="#接口管理" aria-label="Permalink to &quot;接口管理&quot;">​</a></h2><p>可以查看所有的接口，并进行筛选和配置，方便管理和调试。</p><img src="'+r+'"><h2 id="代理管理" tabindex="-1">代理管理 <a class="header-anchor" href="#代理管理" aria-label="Permalink to &quot;代理管理&quot;">​</a></h2><p>展示了所有的代理配置，可以通过可视化界面轻松切换代理，简化开发过程。</p><img src="'+i+'"><h2 id="字典管理" tabindex="-1">字典管理 <a class="header-anchor" href="#字典管理" aria-label="Permalink to &quot;字典管理&quot;">​</a></h2><p>查看和管理所有的字典数据，确保数据的一致性和准确性。</p><img src="'+l+'"><h2 id="账号切换" tabindex="-1">账号切换 <a class="header-anchor" href="#账号切换" aria-label="Permalink to &quot;账号切换&quot;">​</a></h2><p>允许查看所有的账号，并通过可视化界面进行账号切换，方便测试和调试不同用户权限。</p><img src="'+d+'">',14)]))}const b=e(n,[["render",c]]);export{f as __pageData,b as default};
