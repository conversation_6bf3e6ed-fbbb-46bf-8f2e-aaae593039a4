import{_ as e,a as t,o,ah as s}from"./chunks/framework.BMq9nYrq.js";const r="/images/dev-tools-eps.jpg",i="/images/dev-tools-proxy.jpg",l="/images/dev-tools-dict.jpg",d="/images/dev-tools-account.jpg",f=JSON.parse('{"title":"开发工具","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/plugins/dev-tools.md","filePath":"src/guide/plugins/dev-tools.md","lastUpdated":*************}'),n={name:"src/guide/plugins/dev-tools.md"};function c(p,a,_,h,m,u){return o(),t("div",null,a[0]||(a[0]=[s("",14)]))}const b=e(n,[["render",c]]);export{f as __pageData,b as default};
