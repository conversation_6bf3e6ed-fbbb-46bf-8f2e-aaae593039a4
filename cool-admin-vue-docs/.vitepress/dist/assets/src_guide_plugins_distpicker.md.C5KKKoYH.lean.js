import{_ as i,a,o as t,ah as n}from"./chunks/framework.BMq9nYrq.js";const g=JSON.parse('{"title":"distpicker","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/plugins/distpicker.md","filePath":"src/guide/plugins/distpicker.md","lastUpdated":1721048877000}'),l={name:"src/guide/plugins/distpicker.md"};function p(e,s,h,k,r,E){return t(),a("div",null,s[0]||(s[0]=[n("",4)]))}const c=i(l,[["render",p]]);export{g as __pageData,c as default};
