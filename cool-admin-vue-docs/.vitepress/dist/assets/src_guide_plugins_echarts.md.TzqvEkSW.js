import{_ as t,a as s,o as c,g as e,I as r}from"./chunks/framework.BMq9nYrq.js";const f=JSON.parse('{"title":"echarts","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/plugins/echarts.md","filePath":"src/guide/plugins/echarts.md","lastUpdated":1722266295000}'),n={name:"src/guide/plugins/echarts.md"};function h(l,a,o,d,i,p){return c(),s("div",null,a[0]||(a[0]=[e("h1",{id:"echarts",tabindex:"-1"},[r("echarts "),e("a",{class:"header-anchor",href:"#echarts","aria-label":'Permalink to "echarts"'},"​")],-1),e("p",null,[e("a",{href:"https://echarts.apache.org/zh/index.html",target:"_blank",rel:"noreferrer"},"echarts"),r(" 配置，基于 "),e("a",{href:"https://vue-echarts.dev/",target:"_blank",rel:"noreferrer"},"vue-echarts")],-1)]))}const g=t(n,[["render",h]]);export{f as __pageData,g as default};
