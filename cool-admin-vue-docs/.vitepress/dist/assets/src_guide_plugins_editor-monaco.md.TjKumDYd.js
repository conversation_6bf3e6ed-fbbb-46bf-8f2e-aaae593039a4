import{d as a,r,y as l,a as n,o as i,ah as s,g as c,D as m}from"./chunks/framework.BMq9nYrq.js";const p=JSON.parse('{"title":"editor-monaco","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/plugins/editor-monaco.md","filePath":"src/guide/plugins/editor-monaco.md","lastUpdated":1721048877000}'),u={name:"src/guide/plugins/editor-monaco.md"},f=a({...u,setup(h){const d=r([{label:"A",children:[{label:"B",children:[{label:"C",children:[{label:"D"}]}]}]}]);return(_,t)=>{const e=l("cl-editor-monaco");return i(),n("div",null,[t[1]||(t[1]=s('<h1 id="editor-monaco" tabindex="-1">editor-monaco <a class="header-anchor" href="#editor-monaco" aria-label="Permalink to &quot;editor-monaco&quot;">​</a></h1><p><a href="https://microsoft.github.io/monaco-editor/index.html" target="_blank" rel="noreferrer">monaco</a> 代码编辑器。</p><table tabindex="0"><thead><tr><th>参数</th><th>说明</th><th>类型</th><th>可选值</th><th>默认值</th></tr></thead><tbody><tr><td>modelValue</td><td>绑定值</td><td>string</td><td></td><td></td></tr><tr><td>options</td><td>配置选项</td><td>object</td><td></td><td></td></tr><tr><td>language</td><td>语言类型</td><td>string</td><td></td><td>json</td></tr><tr><td>height</td><td>高度</td><td>string / number</td><td></td><td>500</td></tr><tr><td>disabled</td><td>是否禁用</td><td>boolean</td><td></td><td>false</td></tr><tr><td>autofocus</td><td>自动聚焦</td><td>boolean</td><td></td><td>false</td></tr><tr><td>autoFormatCode</td><td>自动格式化</td><td>boolean</td><td></td><td>true</td></tr><tr><td>border</td><td>是否带边框</td><td>boolean</td><td></td><td>true</td></tr></tbody></table><h2 id="示例" tabindex="-1">示例 <a class="header-anchor" href="#示例" aria-label="Permalink to &quot;示例&quot;">​</a></h2>',4)),c("template",null,[m(e,{modelValue:d.value,"onUpdate:modelValue":t[0]||(t[0]=o=>d.value=o)},null,8,["modelValue"])])])}}});export{p as __pageData,f as default};
