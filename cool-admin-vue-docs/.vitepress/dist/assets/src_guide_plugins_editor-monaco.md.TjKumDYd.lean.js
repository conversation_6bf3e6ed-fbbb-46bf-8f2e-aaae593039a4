import{d as a,r,y as l,a as n,o as i,ah as s,g as c,D as m}from"./chunks/framework.BMq9nYrq.js";const p=JSON.parse('{"title":"editor-monaco","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/plugins/editor-monaco.md","filePath":"src/guide/plugins/editor-monaco.md","lastUpdated":1721048877000}'),u={name:"src/guide/plugins/editor-monaco.md"},f=a({...u,setup(h){const d=r([{label:"A",children:[{label:"B",children:[{label:"C",children:[{label:"D"}]}]}]}]);return(_,t)=>{const e=l("cl-editor-monaco");return i(),n("div",null,[t[1]||(t[1]=s("",4)),c("template",null,[m(e,{modelValue:d.value,"onUpdate:modelValue":t[0]||(t[0]=o=>d.value=o)},null,8,["modelValue"])])])}}});export{p as __pageData,f as default};
