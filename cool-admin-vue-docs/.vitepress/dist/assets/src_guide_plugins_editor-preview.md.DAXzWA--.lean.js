import{_ as s,a as i,o as a,ah as d}from"./chunks/framework.BMq9nYrq.js";const g=JSON.parse('{"title":"editor-preview","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/plugins/editor-preview.md","filePath":"src/guide/plugins/editor-preview.md","lastUpdated":1738995647000}'),n={name:"src/guide/plugins/editor-preview.md"};function e(l,t,h,p,r,k){return a(),i("div",null,t[0]||(t[0]=[d("",5)]))}const o=s(n,[["render",e]]);export{g as __pageData,o as default};
