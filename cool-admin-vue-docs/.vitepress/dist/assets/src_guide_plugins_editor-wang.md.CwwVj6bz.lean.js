import{_ as s,a as i,o as a,ah as n}from"./chunks/framework.BMq9nYrq.js";const o=JSON.parse('{"title":"editor-wang","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/plugins/editor-wang.md","filePath":"src/guide/plugins/editor-wang.md","lastUpdated":1721048877000}'),l={name:"src/guide/plugins/editor-wang.md"};function e(d,t,h,p,r,k){return a(),i("div",null,t[0]||(t[0]=[n("",5)]))}const E=s(l,[["render",e]]);export{o as __pageData,E as default};
