import{_ as s,a,o as i,ah as t}from"./chunks/framework.BMq9nYrq.js";const k=JSON.parse('{"title":"element-ui","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/plugins/element-ui.md","filePath":"src/guide/plugins/element-ui.md","lastUpdated":1722266295000}'),n={name:"src/guide/plugins/element-ui.md"};function l(p,e,r,h,d,o){return i(),a("div",null,e[0]||(e[0]=[t(`<h1 id="element-ui" tabindex="-1">element-ui <a class="header-anchor" href="#element-ui" aria-label="Permalink to &quot;element-ui&quot;">​</a></h1><p><a href="https://element-plus.org/zh-CN/" target="_blank" rel="noreferrer">element-plus</a> 配置</p><h2 id="目录结构" tabindex="-1">目录结构 <a class="header-anchor" href="#目录结构" aria-label="Permalink to &quot;目录结构&quot;">​</a></h2><div class="language-js vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">/</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">src</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">/</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">plugins</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    ├──css</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">/</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">index.scss 自定义样式</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    └──config.ts 配置</span></span></code></pre></div>`,4)]))}const u=s(n,[["render",l]]);export{k as __pageData,u as default};
