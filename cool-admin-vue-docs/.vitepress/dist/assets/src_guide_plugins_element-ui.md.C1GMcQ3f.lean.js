import{_ as s,a,o as i,ah as t}from"./chunks/framework.BMq9nYrq.js";const k=JSON.parse('{"title":"element-ui","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/plugins/element-ui.md","filePath":"src/guide/plugins/element-ui.md","lastUpdated":1722266295000}'),n={name:"src/guide/plugins/element-ui.md"};function l(p,e,r,h,d,o){return i(),a("div",null,e[0]||(e[0]=[t("",4)]))}const u=s(n,[["render",l]]);export{k as __pageData,u as default};
