import{_ as i,a,o as t,ah as n}from"./chunks/framework.BMq9nYrq.js";const g=JSON.parse('{"title":"excel","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/plugins/excel.md","filePath":"src/guide/plugins/excel.md","lastUpdated":1721048877000}'),l={name:"src/guide/plugins/excel.md"};function h(p,s,k,e,E,d){return t(),a("div",null,s[0]||(s[0]=[n("",11)]))}const y=i(l,[["render",h]]);export{g as __pageData,y as default};
