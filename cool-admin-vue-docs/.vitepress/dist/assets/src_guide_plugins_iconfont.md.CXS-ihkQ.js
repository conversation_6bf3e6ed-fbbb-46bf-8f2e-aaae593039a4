import{_ as a,a as i,o as n,ah as t}from"./chunks/framework.BMq9nYrq.js";const k=JSON.parse('{"title":"iconfont","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/plugins/iconfont.md","filePath":"src/guide/plugins/iconfont.md","lastUpdated":1738995647000}'),e={name:"src/guide/plugins/iconfont.md"};function o(c,s,l,p,r,d){return n(),i("div",null,s[0]||(s[0]=[t(`<h1 id="iconfont" tabindex="-1">iconfont <a class="header-anchor" href="#iconfont" aria-label="Permalink to &quot;iconfont&quot;">​</a></h1><p>iconfont 插件，支持 iconfont 图标库。</p><h2 id="配置" tabindex="-1">配置 <a class="header-anchor" href="#配置" aria-label="Permalink to &quot;配置&quot;">​</a></h2><p>在 <code>plugins/iconfont/config.ts</code> 中配置：</p><div class="language-js vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">const</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> urls</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> [</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  // 如：at.alicdn.com/t/c/font_4803959_e2to11yi7pu.css</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">];</span></span></code></pre></div>`,5)]))}const f=a(e,[["render",o]]);export{k as __pageData,f as default};
