import{_ as a,a as i,o as n,ah as t}from"./chunks/framework.BMq9nYrq.js";const k=JSON.parse('{"title":"iconfont","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/plugins/iconfont.md","filePath":"src/guide/plugins/iconfont.md","lastUpdated":1738995647000}'),e={name:"src/guide/plugins/iconfont.md"};function o(c,s,l,p,r,d){return n(),i("div",null,s[0]||(s[0]=[t("",5)]))}const f=a(e,[["render",o]]);export{k as __pageData,f as default};
