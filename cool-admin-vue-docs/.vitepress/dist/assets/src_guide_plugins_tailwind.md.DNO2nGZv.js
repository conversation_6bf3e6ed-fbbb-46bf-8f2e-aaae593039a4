import{_ as t,a as i,o as e,ah as n}from"./chunks/framework.BMq9nYrq.js";const h=JSON.parse('{"title":"tailwind","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/plugins/tailwind.md","filePath":"src/guide/plugins/tailwind.md","lastUpdated":1738995647000}'),r={name:"src/guide/plugins/tailwind.md"};function l(d,a,s,o,c,_){return e(),i("div",null,a[0]||(a[0]=[n('<h1 id="tailwind" tabindex="-1">tailwind <a class="header-anchor" href="#tailwind" aria-label="Permalink to &quot;tailwind&quot;">​</a></h1><p>tailwind 插件，支持 tailwind 样式。</p><h2 id="文档" tabindex="-1">文档 <a class="header-anchor" href="#文档" aria-label="Permalink to &quot;文档&quot;">​</a></h2><p><a href="https://tailwindcss.com/" target="_blank" rel="noreferrer">tailwindcss</a></p>',4)]))}const u=t(r,[["render",l]]);export{h as __pageData,u as default};
