import{_ as t,a as i,o as e,ah as n}from"./chunks/framework.BMq9nYrq.js";const h=JSON.parse('{"title":"tailwind","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/plugins/tailwind.md","filePath":"src/guide/plugins/tailwind.md","lastUpdated":1738995647000}'),r={name:"src/guide/plugins/tailwind.md"};function l(d,a,s,o,c,_){return e(),i("div",null,a[0]||(a[0]=[n("",4)]))}const u=t(r,[["render",l]]);export{h as __pageData,u as default};
