import{_ as i,a,o as n,ah as l}from"./chunks/framework.BMq9nYrq.js";const g=JSON.parse('{"title":"theme","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/plugins/theme.md","filePath":"src/guide/plugins/theme.md","lastUpdated":1738995647000}'),h={name:"src/guide/plugins/theme.md"};function p(k,s,E,t,e,r){return n(),a("div",null,s[0]||(s[0]=[l("",5)]))}const y=i(h,[["render",p]]);export{g as __pageData,y as default};
