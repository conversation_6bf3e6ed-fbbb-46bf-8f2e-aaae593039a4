import{_ as i,a,o as t,ah as l}from"./chunks/framework.BMq9nYrq.js";const o=JSON.parse('{"title":"upload","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/plugins/upload.md","filePath":"src/guide/plugins/upload.md","lastUpdated":1738995647000}'),h={name:"src/guide/plugins/upload.md"};function n(e,s,d,p,k,r){return t(),a("div",null,s[0]||(s[0]=[l("",28)]))}const g=i(h,[["render",n]]);export{o as __pageData,g as default};
