import{_ as i,a,o as t,ah as n}from"./chunks/framework.BMq9nYrq.js";const l="/images/view-group.png",h="/images/view-group-phone2.png",p="/images/view-group-phone.png",y=JSON.parse('{"title":"view","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/plugins/view.md","filePath":"src/guide/plugins/view.md","lastUpdated":1723097906000}'),e={name:"src/guide/plugins/view.md"};function k(d,s,E,r,g,o){return t(),a("div",null,s[0]||(s[0]=[n("",29)]))}const u=i(e,[["render",k]]);export{y as __pageData,u as default};
