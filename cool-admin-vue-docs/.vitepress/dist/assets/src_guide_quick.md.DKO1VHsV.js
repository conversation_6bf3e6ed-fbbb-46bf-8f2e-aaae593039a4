import{_ as s}from"./chunks/admin.7MgUyfIE.js";import{_ as e,a as i,o as t,ah as l}from"./chunks/framework.BMq9nYrq.js";const g=JSON.parse('{"title":"快速开始","description":"","frontmatter":{},"headers":[],"relativePath":"src/guide/quick.md","filePath":"src/guide/quick.md","lastUpdated":1738995647000}'),n={name:"src/guide/quick.md"};function h(o,a,r,p,d,c){return t(),i("div",null,a[0]||(a[0]=[l(`<h1 id="快速开始" tabindex="-1">快速开始 <a class="header-anchor" href="#快速开始" aria-label="Permalink to &quot;快速开始&quot;">​</a></h1><h2 id="开发工具" tabindex="-1">开发工具 <a class="header-anchor" href="#开发工具" aria-label="Permalink to &quot;开发工具&quot;">​</a></h2><p>推荐使用<a href="https://code.visualstudio.com/" target="_blank" rel="noreferrer">vscode</a>，进行项目开发，<code>cool-admin</code>为其内置了一些代码片段及代码提示，加快开发效率。</p><h2 id="拉取代码" tabindex="-1">拉取代码 <a class="header-anchor" href="#拉取代码" aria-label="Permalink to &quot;拉取代码&quot;">​</a></h2><div class="language-shell vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">shell</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">git</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> clone</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> https://github.com/cool-team-official/cool-admin-vue.git</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">或</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">git</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> clone</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> https://gitee.com/cool-team-official/cool-admin-vue.git</span></span></code></pre></div><h2 id="安装依赖" tabindex="-1">安装依赖 <a class="header-anchor" href="#安装依赖" aria-label="Permalink to &quot;安装依赖&quot;">​</a></h2><div class="language-shell vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">shell</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> i</span></span></code></pre></div><h2 id="运行应用程序" tabindex="-1">运行应用程序 <a class="header-anchor" href="#运行应用程序" aria-label="Permalink to &quot;运行应用程序&quot;">​</a></h2><div class="language-shell vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">shell</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> dev</span></span></code></pre></div><p>在浏览器中预览网站 <a href="http://localhost:9000" target="_blank" rel="noreferrer">http://localhost:9000</a></p><p>账号：admin 密码：123456</p><img src="`+s+'"><h2 id="异常" tabindex="-1">异常 ！！！ <a class="header-anchor" href="#异常" aria-label="Permalink to &quot;异常 ！！！&quot;">​</a></h2><p>如遇到验证码无法显示，请运行<a href="https://node.cool-admin.com" target="_blank" rel="noreferrer">后端服务</a></p>',14)]))}const m=e(n,[["render",h]]);export{g as __pageData,m as default};
