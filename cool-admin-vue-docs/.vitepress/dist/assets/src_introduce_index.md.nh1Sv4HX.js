import{_ as r}from"./chunks/admin.7MgUyfIE.js";import{_ as a}from"./chunks/flow.Dzy7_fvw.js";import{_ as l,a as i,o as t,ah as o}from"./chunks/framework.BMq9nYrq.js";const b=JSON.parse('{"title":"简介","description":"","frontmatter":{},"headers":[],"relativePath":"src/introduce/index.md","filePath":"src/introduce/index.md","lastUpdated":1738995647000}'),n={name:"src/introduce/index.md"};function s(h,e,d,p,c,m){return t(),i("div",null,e[0]||(e[0]=[o('<h1 id="简介" tabindex="-1">简介 <a class="header-anchor" href="#简介" aria-label="Permalink to &quot;简介&quot;">​</a></h1><p><img src="'+r+'" alt="" data-zoomable=""></p><h2 id="技术选型" tabindex="-1">技术选型 <a class="header-anchor" href="#技术选型" aria-label="Permalink to &quot;技术选型&quot;">​</a></h2><ul><li><a href="https://v3.cn.vuejs.org/" target="_blank" rel="noreferrer">Vue3</a></li><li><a href="https://element-plus.org/" target="_blank" rel="noreferrer">Element Plus</a></li><li><a href="https://vitejs.dev/" target="_blank" rel="noreferrer">Vite</a></li><li><a href="https://pinia.vuejs.org/" target="_blank" rel="noreferrer">Pinia</a></li><li><a href="https://axios-http.com/" target="_blank" rel="noreferrer">Axios</a></li><li><a href="https://echarts.apache.org/zh/index.html" target="_blank" rel="noreferrer">ECharts</a></li><li><a href="https://router.vuejs.org/zh/index.html" target="_blank" rel="noreferrer">Vue Router</a></li><li><a href="https://www.typescriptlang.org/" target="_blank" rel="noreferrer">TypeScript</a></li></ul><h2 id="为什么选择-cool-admin" tabindex="-1">为什么选择 Cool Admin？ <a class="header-anchor" href="#为什么选择-cool-admin" aria-label="Permalink to &quot;为什么选择 Cool Admin？&quot;">​</a></h2><p>随着技术不断地发展，特别是最近 Ai 相关的技术发展，以往的框架已经越来越不能满足现代化的开发需求。</p><p>Cool Admin 做为后来者有后发优势，主要特点：</p><ul><li>Ai 编码，从页面到后端代码，部分功能实现零代码；</li><li>Ai 流程编排，专门为 Ai 开发设计的，Ai 开发几乎不用写代码，只需拖一拖即可；</li><li>扩展插件，可插拔，如支付、短信这类功能的插件可以通过后台动态安装卸载，灵活又不臃肿；</li><li>代码简洁，不像一般代码生成器生成一堆冗余代码，Cool 只需极少编码即可实现大部分需求；</li><li>......</li></ul><p><img src="'+r+'" alt="" data-zoomable=""></p><p><img src="'+a+'" alt="" data-zoomable=""></p><h2 id="内置功能" tabindex="-1">内置功能 <a class="header-anchor" href="#内置功能" aria-label="Permalink to &quot;内置功能&quot;">​</a></h2><ul><li>用户管理：呈现公司组织部门树形结构，用户是系统操作者，该功能主要完成系统用户配置。</li><li>菜单管理：配置系统菜单，操作权限标识等。</li><li>角色管理：角色菜单权限分配、设置角色按机构进行数据范围权限划分。</li><li>参数管理：对系统动态配置常用参数。</li><li>字典管理：对系统中经常使用的一些较为固定的数据进行维护。</li><li>请求日志：接口的请求入参日志，便于问题排查。</li><li>操作日志：系统正常操作日志记录和查询；系统异常信息日志记录和查询。</li><li>定时任务：在线（添加、修改、删除）任务调度包含执行结果日志。</li><li>文件管理：支持静态资源文件上传云端进行云管理。</li><li>数据回收站：数据有 30 天的保留，支持回滚操作。</li><li>前后端插件：支持动态安装、卸载插件，实现功能可插拔。（后端插件开发中...）</li></ul>',12)]))}const g=l(n,[["render",s]]);export{b as __pageData,g as default};
