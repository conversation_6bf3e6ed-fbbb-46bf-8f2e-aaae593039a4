import{_ as e}from"./chunks/admin.7MgUyfIE.js";import{_ as r,a as o,o as t,ah as s}from"./chunks/framework.BMq9nYrq.js";const u=JSON.parse('{"title":"演示","description":"","frontmatter":{},"headers":[],"relativePath":"src/introduce/show.md","filePath":"src/introduce/show.md","lastUpdated":1738995647000}'),c={name:"src/introduce/show.md"};function n(d,a,i,h,l,m){return t(),o("div",null,a[0]||(a[0]=[s('<h1 id="演示" tabindex="-1">演示 <a class="header-anchor" href="#演示" aria-label="Permalink to &quot;演示&quot;">​</a></h1><p><img src="'+e+'" alt=""></p><p>→ <a href="https://show.cool-admin.com" target="_blank" rel="noreferrer">https://show.cool-admin.com</a></p><p>账户：admin 密码：123456</p><h1 id="crud-示例" tabindex="-1">CRUD 示例 <a class="header-anchor" href="#crud-示例" aria-label="Permalink to &quot;CRUD 示例&quot;">​</a></h1><p>→ <a href="https://show.cool-admin.com/demo/crud" target="_blank" rel="noreferrer">https://show.cool-admin.com/demo/crud</a></p>',6)]))}const f=r(c,[["render",n]]);export{u as __pageData,f as default};
