import{_ as e}from"./chunks/admin.7MgUyfIE.js";import{_ as r,a as o,o as t,ah as s}from"./chunks/framework.BMq9nYrq.js";const u=JSON.parse('{"title":"演示","description":"","frontmatter":{},"headers":[],"relativePath":"src/introduce/show.md","filePath":"src/introduce/show.md","lastUpdated":1738995647000}'),c={name:"src/introduce/show.md"};function n(d,a,i,h,l,m){return t(),o("div",null,a[0]||(a[0]=[s("",6)]))}const f=r(c,[["render",n]]);export{u as __pageData,f as default};
