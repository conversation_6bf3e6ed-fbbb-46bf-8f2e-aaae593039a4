import{_ as o,a as e,o as t,ah as r}from"./chunks/framework.BMq9nYrq.js";const s=JSON.parse('{"title":"源码","description":"","frontmatter":{},"headers":[],"relativePath":"src/introduce/src.md","filePath":"src/introduce/src.md","lastUpdated":1722935627000}'),c={name:"src/introduce/src.md"};function i(l,a,m,d,p,n){return t(),e("div",null,a[0]||(a[0]=[r('<h1 id="源码" tabindex="-1">源码 <a class="header-anchor" href="#源码" aria-label="Permalink to &quot;源码&quot;">​</a></h1><p>项目开源免费的，遵循 <a href="https://baike.baidu.com/item/MIT%E8%AE%B8%E5%8F%AF%E8%AF%81/6671281" target="_blank" rel="noreferrer">MIT</a> 开源协议，意味着您无需支付任何费用，也无需授权，即可将它应用到您的产品中。</p><p>为了使项目能够持续，不要吝啬你们免费的<code>Star</code>哦，如果你们发现问题或者想要贡献代码，欢迎<code>pr</code>、<code>issue</code></p><div class="warning custom-block"><p class="custom-block-title">注意！！！</p><p>这并不意味着您可以将 cool-admin 应用到非法的领域，比如涉及赌博，暴力等方面。如因此产生纠纷等法律问题，cool-admin 不承担任何责任。</p></div><h2 id="后端-java" tabindex="-1">后端 Java <a class="header-anchor" href="#后端-java" aria-label="Permalink to &quot;后端 Java&quot;">​</a></h2><p>Cool-Admin-Java 的后端源码，欢迎大家一起共建维护，积极贡献代码。</p><p><a href="https://github.com/cool-team-official/cool-admin-java" target="_blank" rel="noreferrer">https://github.com/cool-team-official/cool-admin-java</a></p><p>或</p><p><a href="https://gitee.com/cool-team-official/cool-admin-java" target="_blank" rel="noreferrer">https://gitee.com/cool-team-official/cool-admin-java</a></p><p>或</p><p><a href="https://gitcode.com/cool_team/cool-admin-java" target="_blank" rel="noreferrer">https://gitcode.com/cool_team/cool-admin-java</a></p><h2 id="后端-midway" tabindex="-1">后端 Midway <a class="header-anchor" href="#后端-midway" aria-label="Permalink to &quot;后端 Midway&quot;">​</a></h2><p>Cool-Admin-Midway 的后端源码，欢迎大家一起共建维护，积极贡献代码。</p><p><a href="https://github.com/cool-team-official/cool-admin-midway" target="_blank" rel="noreferrer">https://github.com/cool-team-official/cool-admin-midway</a></p><p>或</p><p><a href="https://gitee.com/cool-team-official/cool-admin-midway" target="_blank" rel="noreferrer">https://gitee.com/cool-team-official/cool-admin-midway</a></p><p>或</p><p><a href="https://gitcode.com/cool_team/cool-admin-midway" target="_blank" rel="noreferrer">https://gitcode.com/cool_team/cool-admin-midway</a></p><h2 id="前端" tabindex="-1">前端 <a class="header-anchor" href="#前端" aria-label="Permalink to &quot;前端&quot;">​</a></h2><p>Cool-Admin 的前端源码，欢迎大家一起共建维护，积极贡献代码。</p><p><a href="https://github.com/cool-team-official/cool-admin-vue" target="_blank" rel="noreferrer">https://github.com/cool-team-official/cool-admin-vue</a></p><p>或</p><p><a href="https://gitee.com/cool-team-official/cool-admin-vue" target="_blank" rel="noreferrer">https://gitee.com/cool-team-official/cool-admin-vue</a></p><p>或</p><p><a href="https://gitcode.com/cool_team/cool-admin-vue" target="_blank" rel="noreferrer">https://gitcode.com/cool_team/cool-admin-vue</a></p><h2 id="文档" tabindex="-1">文档 <a class="header-anchor" href="#文档" aria-label="Permalink to &quot;文档&quot;">​</a></h2><p>如果文档有缺失，欢迎提交<code>pr</code>，您的贡献可能会给其他人来带巨大的帮助。</p><p><a href="https://github.com/cool-team-official/cool-admin-vue-docs" target="_blank" rel="noreferrer">https://github.com/cool-team-official/cool-admin-vue-docs</a></p><p>或</p><p><a href="https://gitee.com/cool-team-official/cool-admin-vue-docs" target="_blank" rel="noreferrer">https://gitee.com/cool-team-official/cool-admin-vue-docs</a></p>',30)]))}const f=o(c,[["render",i]]);export{s as __pageData,f as default};
