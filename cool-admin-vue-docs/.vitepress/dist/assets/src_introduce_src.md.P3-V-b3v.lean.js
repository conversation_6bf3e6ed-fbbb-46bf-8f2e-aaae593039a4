import{_ as o,a as e,o as t,ah as r}from"./chunks/framework.BMq9nYrq.js";const s=JSON.parse('{"title":"源码","description":"","frontmatter":{},"headers":[],"relativePath":"src/introduce/src.md","filePath":"src/introduce/src.md","lastUpdated":1722935627000}'),c={name:"src/introduce/src.md"};function i(l,a,m,d,p,n){return t(),e("div",null,a[0]||(a[0]=[r("",30)]))}const f=o(c,[["render",i]]);export{s as __pageData,f as default};
