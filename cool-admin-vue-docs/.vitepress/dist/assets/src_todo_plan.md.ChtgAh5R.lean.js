import{_ as t,a as r,o,g as a,I as s}from"./chunks/framework.BMq9nYrq.js";const _=JSON.parse('{"title":"计划","description":"","frontmatter":{},"headers":[],"relativePath":"src/todo/plan.md","filePath":"src/todo/plan.md","lastUpdated":1722266295000}'),n={name:"src/todo/plan.md"};function d(l,e,c,p,i,m){return o(),r("div",null,e[0]||(e[0]=[a("h1",{id:"计划",tabindex:"-1"},[s("计划 "),a("a",{class:"header-anchor",href:"#计划","aria-label":'Permalink to "计划"'},"​")],-1),a("p",null,"以下是我们的一些计划，如果你有更好的建议，欢迎联系我们。",-1)]))}const h=t(n,[["render",d]]);export{_ as __pageData,h as default};
