import{_ as e,a as l,o as i,ah as o}from"./chunks/framework.BMq9nYrq.js";const h=JSON.parse('{"title":"更新","description":"","frontmatter":{},"headers":[],"relativePath":"src/todo/update.md","filePath":"src/todo/update.md","lastUpdated":1739157467000}'),t={name:"src/todo/update.md"};function r(d,a,c,u,s,n){return i(),l("div",null,a[0]||(a[0]=[o('<h1 id="更新" tabindex="-1">更新 <a class="header-anchor" href="#更新" aria-label="Permalink to &quot;更新&quot;">​</a></h1><p>因为有你们的支持，cool-admin 会持续不断地更新，如果方便请点下 Star 🌟，这将会给我们带来更多的动力！！！</p><p><a href="https://github.com/cool-team-official/cool-admin-vue" target="_blank" rel="noreferrer">源码地址</a></p><h2 id="v8-0-0-2025-02-10" tabindex="-1">v8.0.0（2025-02-10） <a class="header-anchor" href="#v8-0-0-2025-02-10" aria-label="Permalink to &quot;v8.0.0（2025-02-10）&quot;">​</a></h2><p><a href="/src/todo/upgrade.html">7.x 升级方案</a></p><ul><li>支持多语言 vue-i18n</li><li>添加 @cool-vue/ai 插件，支持 ai 翻译等</li><li>添加 dev-tools 插件，快捷切换代理，查看 eps 等</li><li>支持 tailwindcss</li><li>细调主题、颜色等</li><li>@cool-vue/vite-plugin 更新至 8.0.0</li><li>@cool-vue/crud 更新至 8.0.0</li></ul><h2 id="v7-3-0-2024-10-20" tabindex="-1">v7.3.0（2024-10-20） <a class="header-anchor" href="#v7-3-0-2024-10-20" aria-label="Permalink to &quot;v7.3.0（2024-10-20）&quot;">​</a></h2><ul><li>支持 /goods/:id 动态参数路由</li><li>eps replace 异常处理</li><li>@cool-vue/vite-plugin 更新至 7.2.4</li><li>@cool-vue/crud 更新至 7.2.2</li></ul><h2 id="v7-2-0-2024-7-29" tabindex="-1">v7.2.0（2024-7-29） <a class="header-anchor" href="#v7-2-0-2024-7-29" aria-label="Permalink to &quot;v7.2.0（2024-7-29）&quot;">​</a></h2><ul><li>优化 svg 加载方式，不插入 index.html 文件中</li><li>使用 svgo 压缩文件</li><li>@cool-vue/vite-plugin 更新至 7.2.1</li><li>@cool-vue/crud 更新至 7.2.0</li></ul>',10)]))}const v=e(t,[["render",r]]);export{h as __pageData,v as default};
