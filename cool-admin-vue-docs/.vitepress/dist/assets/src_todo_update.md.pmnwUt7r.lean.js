import{_ as e,a as l,o as i,ah as o}from"./chunks/framework.BMq9nYrq.js";const h=JSON.parse('{"title":"更新","description":"","frontmatter":{},"headers":[],"relativePath":"src/todo/update.md","filePath":"src/todo/update.md","lastUpdated":1739157467000}'),t={name:"src/todo/update.md"};function r(d,a,c,u,s,n){return i(),l("div",null,a[0]||(a[0]=[o("",10)]))}const v=e(t,[["render",r]]);export{h as __pageData,v as default};
