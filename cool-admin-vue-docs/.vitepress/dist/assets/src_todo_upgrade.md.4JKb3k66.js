import{_ as i,a,o as t,ah as e}from"./chunks/framework.BMq9nYrq.js";const c=JSON.parse('{"title":"升级方案（v8.x）","description":"","frontmatter":{},"headers":[],"relativePath":"src/todo/upgrade.md","filePath":"src/todo/upgrade.md","lastUpdated":1739157467000}'),l={name:"src/todo/upgrade.md"};function n(h,s,p,k,d,r){return t(),a("div",null,s[0]||(s[0]=[e(`<h1 id="升级方案-v8-x" tabindex="-1">升级方案（v8.x） <a class="header-anchor" href="#升级方案-v8-x" aria-label="Permalink to &quot;升级方案（v8.x）&quot;">​</a></h1><p>以下是从 7.x 升级到 8.x 的方案</p><div class="danger custom-block"><p class="custom-block-title">警告！！！</p><p>我们不建议将已经在运营的项目直接升级到最新的大版本，因为可能会遇到一些无法预知的问题。</p></div><h2 id="升级步骤" tabindex="-1">升级步骤 <a class="header-anchor" href="#升级步骤" aria-label="Permalink to &quot;升级步骤&quot;">​</a></h2><h3 id="_1-代码迁移" tabindex="-1">1. 代码迁移 <a class="header-anchor" href="#_1-代码迁移" aria-label="Permalink to &quot;1. 代码迁移&quot;">​</a></h3><ul><li>获取最新的代码；</li><li>将原有项目的<code>模块</code>、<code>插件</code>复制到新项目中；</li><li>检查原有项目框架中的模块改动，例如 <code>base</code> 模块，如有修改，根据改动内容进行调整；</li><li>8.x 使用 <code>cl-search</code> 作为搜索组件，支持更多配置和扩展；</li><li>移除了模块中的<code>service</code>注入，自定义请求使用 <code>service.request()</code>；</li><li>移除了地址栏的<code>proxy</code>参数，<code>切换代理</code>改用可视化操作；</li><li>使用多语言时，必须使用 <code>$t()</code> <code>t()</code> 方法，翻译时会全文检索：</li></ul><div class="language-html vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">html</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">div</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;你好&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">div</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">替换成：</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">div</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;{{ $t(&#39;你好&#39;)}}&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">div</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span></code></pre></div><div class="language-js vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">const</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">t</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> useI18n</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">();</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">ElMessage.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">success</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;你好&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">);</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 替换成：</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">ElMessage.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">success</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">t</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;你好&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">));</span></span></code></pre></div>`,8)]))}const E=i(l,[["render",n]]);export{c as __pageData,E as default};
