import{_ as i,a,o as t,ah as e}from"./chunks/framework.BMq9nYrq.js";const c=JSON.parse('{"title":"升级方案（v8.x）","description":"","frontmatter":{},"headers":[],"relativePath":"src/todo/upgrade.md","filePath":"src/todo/upgrade.md","lastUpdated":1739157467000}'),l={name:"src/todo/upgrade.md"};function n(h,s,p,k,d,r){return t(),a("div",null,s[0]||(s[0]=[e("",8)]))}const E=i(l,[["render",n]]);export{c as __pageData,E as default};
