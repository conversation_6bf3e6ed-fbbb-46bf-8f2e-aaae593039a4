<!DOCTYPE html>
<html lang="en-US" dir="ltr">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>cl-form | Cool Admin</title>
    <meta name="description" content="一个很酷的后台管理系统开发框架">
    <meta name="generator" content="VitePress v1.6.3">
    <link rel="preload stylesheet" href="/assets/style.CL4Prwvv.css" as="style">
    <link rel="preload stylesheet" href="/vp-icons.css" as="style">
    
    <script type="module" src="/assets/app.B22iYFvx.js"></script>
    <link rel="preload" href="/assets/inter-roman-latin.Di8DUHzh.woff2" as="font" type="font/woff2" crossorigin="">
    <link rel="modulepreload" href="/assets/chunks/theme.9FcTwSv5.js">
    <link rel="modulepreload" href="/assets/chunks/framework.BMq9nYrq.js">
    <link rel="modulepreload" href="/assets/chunks/index.umd.min.DvzFVQw3.js">
    <link rel="modulepreload" href="/assets/chunks/index.CSHP5FlZ.js">
    <link rel="modulepreload" href="/assets/chunks/index.BjsZv4Lw.js">
    <link rel="modulepreload" href="/assets/src_guide_crud_form.md.LfUa36IP.lean.js">
    <script id="check-dark-mode">(()=>{const e=localStorage.getItem("vitepress-theme-appearance")||"auto",a=window.matchMedia("(prefers-color-scheme: dark)").matches;(!e||e==="auto"?a:e==="dark")&&document.documentElement.classList.add("dark")})();</script>
    <script id="check-mac-os">document.documentElement.classList.toggle("mac",/Mac|iPhone|iPod|iPad/i.test(navigator.platform));</script>
  </head>
  <body>
    <div id="app"><div class="Layout" data-v-6a8df82f data-v-eb4c655e><!--[--><!--]--><!--[--><span tabindex="-1" data-v-bec96377></span><a href="#VPContent" class="VPSkipLink visually-hidden" data-v-bec96377>Skip to content</a><!--]--><!----><header class="VPNav" data-v-eb4c655e data-v-2877b773><div class="VPNavBar" data-v-2877b773 data-v-78672aa7><div class="wrapper" data-v-78672aa7><div class="container" data-v-78672aa7><div class="title" data-v-78672aa7><div class="VPNavBarTitle has-sidebar" data-v-78672aa7 data-v-94e7aa9a><a class="title" href="/" data-v-94e7aa9a><!--[--><!--]--><!--[--><img class="VPImage logo" src="/logo.png" alt data-v-6969ec2f><!--]--><span data-v-94e7aa9a>Cool Admin</span><!--[--><!--]--></a></div></div><div class="content" data-v-78672aa7><div class="content-body" data-v-78672aa7><!--[--><!--]--><div class="VPNavBarSearch search" data-v-78672aa7><!--[--><!----><div id="local-search"><button type="button" class="DocSearch DocSearch-Button" aria-label="Search"><span class="DocSearch-Button-Container"><span class="vp-icon DocSearch-Search-Icon"></span><span class="DocSearch-Button-Placeholder">Search</span></span><span class="DocSearch-Button-Keys"><kbd class="DocSearch-Button-Key"></kbd><kbd class="DocSearch-Button-Key">K</kbd></span></button></div><!--]--></div><nav aria-labelledby="main-nav-aria-label" class="VPNavBarMenu menu" data-v-78672aa7 data-v-97baaf5c><span id="main-nav-aria-label" class="visually-hidden" data-v-97baaf5c> Main Navigation </span><!--[--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/src/introduce/index.html" tabindex="0" data-v-97baaf5c data-v-428e00cd><!--[--><span data-v-428e00cd>介绍</span><!--]--></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/src/guide/quick.html" tabindex="0" data-v-97baaf5c data-v-428e00cd><!--[--><span data-v-428e00cd>教程</span><!--]--></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/src/guide/plugins/crud.html" tabindex="0" data-v-97baaf5c data-v-428e00cd><!--[--><span data-v-428e00cd>CRUD 组件</span><!--]--></a><!--]--><!--[--><a class="VPLink link vp-external-link-icon VPNavBarMenuLink" href="https://cool-js.com/plugin" target="_blank" rel="noreferrer" tabindex="0" data-v-97baaf5c data-v-428e00cd><!--[--><span data-v-428e00cd>🔥插件市场</span><!--]--></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/src/about/index.html" tabindex="0" data-v-97baaf5c data-v-428e00cd><!--[--><span data-v-428e00cd>交流合作</span><!--]--></a><!--]--><!--[--><div class="VPFlyout VPNavBarMenuGroup" data-v-97baaf5c data-v-e81ac63d><button type="button" class="button" aria-haspopup="true" aria-expanded="false" data-v-e81ac63d><span class="text" data-v-e81ac63d><!----><span data-v-e81ac63d>v8.0.0</span><span class="vpi-chevron-down text-icon" data-v-e81ac63d></span></span></button><div class="menu" data-v-e81ac63d><div class="VPMenu" data-v-e81ac63d data-v-d56355cd><div class="items" data-v-d56355cd><!--[--><!--[--><div class="VPMenuLink" data-v-d56355cd data-v-c7e3eb75><a class="VPLink link" href="/src/todo/update.html" data-v-c7e3eb75><!--[--><span data-v-c7e3eb75>更新日志</span><!--]--></a></div><!--]--><!--]--></div><!--[--><!--]--></div></div></div><!--]--><!--[--><div class="VPFlyout VPNavBarMenuGroup" data-v-97baaf5c data-v-e81ac63d><button type="button" class="button" aria-haspopup="true" aria-expanded="false" data-v-e81ac63d><span class="text" data-v-e81ac63d><!----><span data-v-e81ac63d>更多</span><span class="vpi-chevron-down text-icon" data-v-e81ac63d></span></span></button><div class="menu" data-v-e81ac63d><div class="VPMenu" data-v-e81ac63d data-v-d56355cd><div class="items" data-v-d56355cd><!--[--><!--[--><div class="VPMenuLink" data-v-d56355cd data-v-c7e3eb75><a class="VPLink link vp-external-link-icon" href="https://node.cool-admin.com" target="_blank" rel="noreferrer" data-v-c7e3eb75><!--[--><span data-v-c7e3eb75>Cool Admin(Nodejs版)</span><!--]--></a></div><!--]--><!--[--><div class="VPMenuLink" data-v-d56355cd data-v-c7e3eb75><a class="VPLink link vp-external-link-icon" href="https://java.cool-admin.com" target="_blank" rel="noreferrer" data-v-c7e3eb75><!--[--><span data-v-c7e3eb75>Cool Admin(Java版)</span><!--]--></a></div><!--]--><!--[--><div class="VPMenuLink" data-v-d56355cd data-v-c7e3eb75><a class="VPLink link vp-external-link-icon" href="https://uni-docs.cool-js.com" target="_blank" rel="noreferrer" data-v-c7e3eb75><!--[--><span data-v-c7e3eb75>Uni（基于uni-app跨端移动端开发）</span><!--]--></a></div><!--]--><!--]--></div><!--[--><!--]--></div></div></div><!--]--><!--]--></nav><!----><div class="VPNavBarAppearance appearance" data-v-78672aa7 data-v-4361c235><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" title aria-checked="false" data-v-4361c235 data-v-fc47fc5d data-v-5c495361><span class="check" data-v-5c495361><span class="icon" data-v-5c495361><!--[--><span class="vpi-sun sun" data-v-fc47fc5d></span><span class="vpi-moon moon" data-v-fc47fc5d></span><!--]--></span></span></button></div><div class="VPSocialLinks VPNavBarSocialLinks social-links" data-v-78672aa7 data-v-d970829e data-v-3f7b2744><!--[--><a class="VPSocialLink no-icon" href="https://github.com/cool-team-official/cool-admin-vue" aria-label="github" target="_blank" rel="noopener" data-v-3f7b2744 data-v-54ae48b6><span class="vpi-social-github"></span></a><!--]--></div><div class="VPFlyout VPNavBarExtra extra" data-v-78672aa7 data-v-77380643 data-v-e81ac63d><button type="button" class="button" aria-haspopup="true" aria-expanded="false" aria-label="extra navigation" data-v-e81ac63d><span class="vpi-more-horizontal icon" data-v-e81ac63d></span></button><div class="menu" data-v-e81ac63d><div class="VPMenu" data-v-e81ac63d data-v-d56355cd><!----><!--[--><!--[--><!----><div class="group" data-v-77380643><div class="item appearance" data-v-77380643><p class="label" data-v-77380643>主题</p><div class="appearance-action" data-v-77380643><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" title aria-checked="false" data-v-77380643 data-v-fc47fc5d data-v-5c495361><span class="check" data-v-5c495361><span class="icon" data-v-5c495361><!--[--><span class="vpi-sun sun" data-v-fc47fc5d></span><span class="vpi-moon moon" data-v-fc47fc5d></span><!--]--></span></span></button></div></div></div><div class="group" data-v-77380643><div class="item social-links" data-v-77380643><div class="VPSocialLinks social-links-list" data-v-77380643 data-v-3f7b2744><!--[--><a class="VPSocialLink no-icon" href="https://github.com/cool-team-official/cool-admin-vue" aria-label="github" target="_blank" rel="noopener" data-v-3f7b2744 data-v-54ae48b6><span class="vpi-social-github"></span></a><!--]--></div></div></div><!--]--><!--]--></div></div></div><!--[--><!--]--><button type="button" class="VPNavBarHamburger hamburger" aria-label="mobile navigation" aria-expanded="false" aria-controls="VPNavScreen" data-v-78672aa7 data-v-330e573a><span class="container" data-v-330e573a><span class="top" data-v-330e573a></span><span class="middle" data-v-330e573a></span><span class="bottom" data-v-330e573a></span></span></button></div></div></div></div><div class="divider" data-v-78672aa7><div class="divider-line" data-v-78672aa7></div></div></div><!----></header><div class="VPLocalNav has-sidebar empty" data-v-eb4c655e data-v-3746dd0c><div class="container" data-v-3746dd0c><button class="menu" aria-expanded="false" aria-controls="VPSidebarNav" data-v-3746dd0c><span class="vpi-align-left menu-icon" data-v-3746dd0c></span><span class="menu-text" data-v-3746dd0c>菜单</span></button><div class="VPLocalNavOutlineDropdown" style="--vp-vh:0px;" data-v-3746dd0c data-v-d33580be><button data-v-d33580be>回到顶部</button><!----></div></div></div><aside class="VPSidebar" data-v-eb4c655e data-v-21179b8d><div class="curtain" data-v-21179b8d></div><nav class="nav" id="VPSidebarNav" aria-labelledby="sidebar-aria-label" tabindex="-1" data-v-21179b8d><span class="visually-hidden" id="sidebar-aria-label" data-v-21179b8d> Sidebar Navigation </span><!--[--><!--[--><div class="ad" data-v-6a8df82f><!--[--><!--]--></div><!--]--><!--]--><!--[--><div class="no-transition group" data-v-6081356c><section class="VPSidebarItem level-0" data-v-6081356c data-v-c5627c3b><div class="item" role="button" tabindex="0" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><h2 class="text" data-v-c5627c3b>介绍</h2><!----></div><div class="items" data-v-c5627c3b><!--[--><div class="VPSidebarItem level-1 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/introduce/index.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>简介</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/introduce/show.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>演示 / 示例</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/introduce/src.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>源码</p><!--]--></a><!----></div><!----></div><!--]--></div></section></div><div class="no-transition group" data-v-6081356c><section class="VPSidebarItem level-0 has-active" data-v-6081356c data-v-c5627c3b><div class="item" role="button" tabindex="0" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><h2 class="text" data-v-c5627c3b>教程</h2><!----></div><div class="items" data-v-c5627c3b><!--[--><div class="VPSidebarItem level-1 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/quick.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>快速开始</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/ai.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>Ai编码</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/flow.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>Ai流程编排</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/dev.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>模块/插件开发</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/permission.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>权限</p><!--]--></a><!----></div><!----></div><section class="VPSidebarItem level-1" data-v-c5627c3b data-v-c5627c3b><div class="item" role="button" tabindex="0" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><h3 class="text" data-v-c5627c3b>目录结构</h3><!----></div><div class="items" data-v-c5627c3b><!--[--><div class="VPSidebarItem level-2 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/packages.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>packages</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-2 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/config.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>config</p><!--]--></a><!----></div><!----></div><section class="VPSidebarItem level-2" data-v-c5627c3b data-v-c5627c3b><div class="item" role="button" tabindex="0" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><h4 class="text" data-v-c5627c3b>cool</h4><!----></div><div class="items" data-v-c5627c3b><!--[--><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/cool/index.vue.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>index.vue</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/cool/router.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>router</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/cool/service.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>service</p><!--]--></a><!----></div><!----></div><!--]--></div></section><section class="VPSidebarItem level-2" data-v-c5627c3b data-v-c5627c3b><div class="item" role="button" tabindex="0" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><h4 class="text" data-v-c5627c3b>modules</h4><!----></div><div class="items" data-v-c5627c3b><!--[--><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/modules/base.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>base</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/modules/dict.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>dict</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/modules/helper.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>helper</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/modules/recycle.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>recycle</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/modules/space.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>space</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/modules/task.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>task</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/modules/user.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>user</p><!--]--></a><!----></div><!----></div><!--]--></div></section><section class="VPSidebarItem level-2" data-v-c5627c3b data-v-c5627c3b><div class="item" role="button" tabindex="0" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><h4 class="text" data-v-c5627c3b>plugins</h4><!----></div><div class="items" data-v-c5627c3b><!--[--><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/plugins/crud.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>crud</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/plugins/dev-tools.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>dev-tools</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/plugins/distpicker.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>distpicker</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/plugins/echarts.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>echarts</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/plugins/editor-preview.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>editor-preview</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/plugins/editor-wang.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>editor-wang</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/plugins/element-ui.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>element-ui</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/plugins/excel.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>excel</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/plugins/i18n.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>i18n</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/plugins/iconfont.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>iconfont</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/plugins/tailwind.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>tailwind</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/plugins/theme.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>theme</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/plugins/upload.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>upload</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-3 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/plugins/view.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>view</p><!--]--></a><!----></div><!----></div><!--]--></div></section><div class="VPSidebarItem level-2 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/App.vue.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>App.vue</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-2 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/main.ts.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>main.ts</p><!--]--></a><!----></div><!----></div><!--]--></div></section><section class="VPSidebarItem level-1 has-active" data-v-c5627c3b data-v-c5627c3b><div class="item" role="button" tabindex="0" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><h3 class="text" data-v-c5627c3b>CRUD 组件</h3><!----></div><div class="items" data-v-c5627c3b><!--[--><div class="VPSidebarItem level-2 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/crud/crud.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>cl-crud</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-2 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/crud/form.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>cl-form</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-2 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/crud/upsert.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>cl-upsert</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-2 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/crud/table.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>cl-table</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-2 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/crud/add-btn.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>cl-add-btn</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-2 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/crud/adv-search.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>cl-adv-search</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-2 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/crud/dialog.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>cl-dialog</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-2 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/crud/search-key.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>cl-search-key</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-2 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/crud/search.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>cl-search</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-2 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/crud/refresh-btn.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>cl-refresh-btn</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-2 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/crud/context-menu.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>cl-context-menu</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-2 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/crud/multi-delete-btn.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>cl-multi-delete-btn</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-2 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/guide/crud/pagination.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>cl-pagination</p><!--]--></a><!----></div><!----></div><!--]--></div></section><!--]--></div></section></div><div class="no-transition group" data-v-6081356c><section class="VPSidebarItem level-0" data-v-6081356c data-v-c5627c3b><!----><div class="items" data-v-c5627c3b><!--[--><div class="VPSidebarItem level-1 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/todo/update.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>更新日志</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c5627c3b data-v-c5627c3b><div class="item" data-v-c5627c3b><div class="indicator" data-v-c5627c3b></div><a class="VPLink link link" href="/src/about/index.html" data-v-c5627c3b><!--[--><p class="text" data-v-c5627c3b>交流合作</p><!--]--></a><!----></div><!----></div><!--]--></div></section></div><!--]--><!--[--><!--]--></nav></aside><div class="VPContent has-sidebar" id="VPContent" data-v-eb4c655e data-v-bf3e0352><div class="VPDoc has-sidebar has-aside" data-v-bf3e0352 data-v-2eca86da><!--[--><!--]--><div class="container" data-v-2eca86da><div class="aside" data-v-2eca86da><div class="aside-curtain" data-v-2eca86da></div><div class="aside-container" data-v-2eca86da><div class="aside-content" data-v-2eca86da><div class="VPDocAside" data-v-2eca86da data-v-55fda58b><!--[--><!--]--><!--[--><!--]--><nav aria-labelledby="doc-outline-aria-label" class="VPDocAsideOutline" data-v-55fda58b data-v-c0faa9ae><div class="content" data-v-c0faa9ae><div class="outline-marker" data-v-c0faa9ae></div><div aria-level="2" class="outline-title" id="doc-outline-aria-label" role="heading" data-v-c0faa9ae>页面导航</div><ul class="VPDocOutlineItem root" data-v-c0faa9ae data-v-d7778d7d><!--[--><!--]--></ul></div></nav><!--[--><!--]--><div class="spacer" data-v-55fda58b></div><!--[--><!--]--><!----><!--[--><!--]--><!--[--><!--]--></div></div></div></div><div class="content" data-v-2eca86da><div class="content-container" data-v-2eca86da><!--[--><!--]--><main class="main" data-v-2eca86da><div style="position:relative;" class="vp-doc _src_guide_crud_form" data-v-2eca86da><div><h1 id="cl-form" tabindex="-1">cl-form <a class="header-anchor" href="#cl-form" aria-label="Permalink to &quot;cl-form&quot;">​</a></h1><p>自定义表单组件，动态数据配置到渲染</p><ul><li><p>新增 <a href="#多层级展示">children</a> 参数，多层级展示</p></li><li><p>新增 <a href="#获取组件实例">ref</a> 参数，获取组件实例</p></li><li><p>新增 <a href="#插件">插件</a>：<code>setFocus</code>（自动聚焦）</p></li></ul><h2 id="useform" tabindex="-1">useForm <a class="header-anchor" href="#useform" aria-label="Permalink to &quot;useForm&quot;">​</a></h2><p><code>cl-form</code> 标签绑定 <code>ref</code> 值后使用 <code>useForm</code> 加载组件</p><ul><li><code>const</code> 定义必须与 <code>ref</code> 一致</li></ul><div class="language-html vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">html</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">cl-form</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> ref</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;Form&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> /&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">script</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> lang</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;ts&quot;</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> setup</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { useForm } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;@cool-vue/crud&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  const</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> Form</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> useForm</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">();</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">script</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span></code></pre></div><p>如果存在使用多个 <code>cl-form</code> 组件的情况，配置不同的 <code>ref</code> 值即可：</p><div class="language-html vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">html</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">cl-form</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> ref</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;UserForm&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> /&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">cl-form</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> ref</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;GoodsForm&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> /&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">script</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> lang</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;ts&quot;</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> setup</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { useForm } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;@cool-vue/crud&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  const</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> UserForm</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> useForm</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">();</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  const</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> GoodsForm</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> useForm</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">();</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">script</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span></code></pre></div><div class="tip custom-block"><p class="custom-block-title">TIP</p><p>子组件可以也可以使用 <code>const Form = useForm()</code> 获取 <code>cl-form</code> 实例。同样 <a href="#ref">Ref</a> 上的值与方法都能使用</p><div class="language-html vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">html</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">cl-form</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> ref</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;Form&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> /&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">script</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> lang</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;ts&quot;</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> setup</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { useForm } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;@cool-vue/crud&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  const</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> Form</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> useForm</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">();</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  function</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> open</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">() {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    Form.value.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">open</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">({</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      items: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">          label: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;昵称&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">          prop: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">          component: {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">            vm: Test,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">          },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      ],</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    });</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  }</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">script</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span></code></pre></div><p>Test.vue</p><div class="language-html vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">html</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  &lt;!-- 绑定name --&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">el-input</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> v-model</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;Form.form.name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> /&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">script</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> lang</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;ts&quot;</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> setup</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { useForm } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;@cool-vue/crud&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  const</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> Form</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> useForm</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">();</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">script</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span></code></pre></div></div><h2 id="基础用法" tabindex="-1">基础用法 <a class="header-anchor" href="#基础用法" aria-label="Permalink to &quot;基础用法&quot;">​</a></h2><!----><h2 id="分组显示" tabindex="-1">分组显示 <a class="header-anchor" href="#分组显示" aria-label="Permalink to &quot;分组显示&quot;">​</a></h2><p>配置 <code>type</code> 参数为 <code>tabs</code>，<code>labels</code> 作为分组列表：</p><ul><li>label 分组名称</li><li>value 分组标识</li></ul><!----><h2 id="多层级展示" tabindex="-1">多层级展示 <a class="header-anchor" href="#多层级展示" aria-label="Permalink to &quot;多层级展示&quot;">​</a></h2><p>在部分情况下，需要某几个 <code>item</code> 合并成一块，如基础信息、上报信息等等。使用 <code>children</code> 参数：</p><!----><h2 id="获取组件实例" tabindex="-1">获取组件实例 <a class="header-anchor" href="#获取组件实例" aria-label="Permalink to &quot;获取组件实例&quot;">​</a></h2><p>在部分情况下，想要获取到组件的实例，有 2 种方法：</p><ul><li><code>ref</code> 参数</li></ul><div class="language-ts vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">ts</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">const</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">refs</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">setRefs</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> useCool</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">();</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">const</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> Form</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> useForm</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">();</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">Form.value.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">open</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">({</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  items: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      label: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;昵称&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      prop: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      component: {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        name: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;el-input&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        ref: </span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">setRefs</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">),</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  ],</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  on: {</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    open</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">() {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      refs.name.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">focus</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">();</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">});</span></span></code></pre></div><ul><li>插槽</li></ul><div class="language-html vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">html</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">cl-form</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> ref</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;Form&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> #slot-name</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;{ scope }&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">el-input</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> :ref</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;setRefs(&#39;name&#39;)&quot;</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> v-model</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;scope.name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> /&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  &lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">cl-form</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span></code></pre></div><h2 id="钩子函数" tabindex="-1">钩子函数 <a class="header-anchor" href="#钩子函数" aria-label="Permalink to &quot;钩子函数&quot;">​</a></h2><ul><li><p><code>open(data)</code> 打开后</p></li><li><p><code>close(action: &#39;close&#39; | &#39;submit&#39;, done)</code> 关闭前</p></li><li><p><code>submit(data, { close, done })</code> 提交时</p></li></ul><!----><h2 id="属性" tabindex="-1">属性 <a class="header-anchor" href="#属性" aria-label="Permalink to &quot;属性&quot;">​</a></h2><table tabindex="0"><thead><tr><th>参数</th><th>说明</th><th>类型</th><th>可选值</th><th>默认值</th></tr></thead><tbody><tr><td>inner</td><td>是否只显示表单</td><td>boolean</td><td></td><td>false</td></tr><tr><td>inline</td><td>是否内联表单</td><td>boolean</td><td></td><td>false</td></tr></tbody></table><h2 id="ref" tabindex="-1">Ref <a class="header-anchor" href="#ref" aria-label="Permalink to &quot;Ref&quot;">​</a></h2><table tabindex="0"><thead><tr><th>名称</th><th>说明</th><th>类型</th></tr></thead><tbody><tr><td>form</td><td>表单值</td><td>{}</td></tr><tr><td>open</td><td>打开表单</td><td>(<a href="#openoptions">OpenOptions</a>, <a href="#plugins">Plugins</a>) =&gt; Ref</td></tr><tr><td>close</td><td>关闭表单</td><td>() =&gt; void</td></tr><tr><td>done</td><td>关闭 saving 状态</td><td>() =&gt; void</td></tr><tr><td>clear</td><td>清空表单值</td><td>() =&gt; void</td></tr><tr><td>reset</td><td>重置表单值</td><td>() =&gt; void</td></tr><tr><td>showLoading</td><td>显示加载框</td><td>() =&gt; void</td></tr><tr><td>hiddenLoading</td><td>隐藏加载框</td><td>() =&gt; void</td></tr><tr><td>setTitle</td><td>设置标题</td><td>(title) =&gt; void</td></tr><tr><td>setData</td><td>根据对象层级设置参数</td><td>(prop, value) =&gt; void</td></tr><tr><td>setOptions</td><td>设置下拉列表</td><td>(prop, value) =&gt; void</td></tr><tr><td>setProps</td><td>设置组件参数</td><td>(prop, props) =&gt; void</td></tr><tr><td>getForm</td><td>获取表单值</td><td>(prop?) =&gt; any</td></tr><tr><td>setForm</td><td>设置表单值</td><td>(prop, value) =&gt; void</td></tr><tr><td>toggleItem</td><td>切换 hidden 值</td><td>(prop, flag?) =&gt; void</td></tr><tr><td>hideItem</td><td>隐藏</td><td>(props) =&gt; void</td></tr><tr><td>showItem</td><td>显示</td><td>(props) =&gt; void</td></tr><tr><td>resetFields</td><td>对整个表单进行重置</td><td>() =&gt; void</td></tr><tr><td>clearValidate</td><td>移除表单项的校验结果</td><td>(props: array | string] =&gt; void</td></tr><tr><td>validateField</td><td>对部分表单字段进行校验的方法</td><td>(props: array | string, callback) =&gt; void</td></tr><tr><td>validate</td><td>对整个表单进行校验的方法</td><td>(callback(valid: boolean)) =&gt; void</td></tr><tr><td>changeTab</td><td>切换选项栏，items 中存在 <code>type=&quot;tabs&quot;</code> 时可用</td><td>(name) =&gt; void</td></tr><tr><td>submit</td><td>表单提交</td><td>(callback(data: any)) =&gt; Promise&lt;any&gt;</td></tr></tbody></table><h3 id="插件" tabindex="-1">插件 <a class="header-anchor" href="#插件" aria-label="Permalink to &quot;插件&quot;">​</a></h3><p>表单的插件。为了满足产品的另一个无理需求（打开的时候自动聚焦第一个，或者指定输入框），so 有了该参数。</p><p>先看看效果：</p><!--[--><!----><!----><!--]--><p>再看看代码：</p><p><code>setFocus(prop: string)</code> 插件是给第一个选项组件（如下的 <code>el-input</code>）或者指定 <code>prop</code> 的组件执行 <code>focus()</code> 方法</p><div class="language-html vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">html</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">el-button</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> type</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;primary&quot;</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> @click</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;open&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;点我！！&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">el-button</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">cl-form</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> ref</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;Form&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> /&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">script</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> lang</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;ts&quot;</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> setup</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { setFocus, useForm } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;@cool-vue/crud&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  const</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> Form</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> useForm</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">();</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  function</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> open</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">() {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    Form.value?.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">open</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        title: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;setFocus 插件&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        props: {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">          labelPosition: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;top&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        items: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">          {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">            label: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;获取 ref，打开后聚焦&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">            prop: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">            component: {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">              name: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;el-input&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">              props: {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">                placeholder: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;请填写昵称&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">              },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">            },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">          },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        ],</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      },</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">      // 配置插件</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      [</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">setFocus</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">()]</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    );</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  }</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">script</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span></code></pre></div><p>最后解释下插件的源码：</p><div class="language-js vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 添加描述 ClForm.Plugin 方便代码提示</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">export</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> function</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> setFocus</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">prop</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> string</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">)</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">:</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> ClForm</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">Plugin</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">	const</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">refs</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">setRefs</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> useRefs</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">();</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">	// 返回一个方法</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">	// exposed 是表单对外暴露的变量，既是 Ref</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">	// onOpen 表单打开时事件</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">	// onClose 表单关闭时事件</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">	// onSubmit 表单提交时事件</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">	return</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> ({ </span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">exposed</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">onOpen</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">onClose</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;"> onSubmit</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> }) </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">		// 获取要匹配的值，为空则取第一个</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">		const</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> name</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> prop </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">||</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> exposed.config.items[</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">].prop;</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">		if</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> (name) {</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">			// 获取配置中与 prop 匹配的选项 item</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">			exposed.config.items.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">find</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">((</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">e</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">) </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">				if</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> (e.prop </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">==</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> name) {</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">					if</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> (e.component) {</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">						// 获取组件的ref</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">						e.component.ref </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> setRefs</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(name);</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">					}</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">				}</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">			});</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">			// 打开的时候调用 focus 方法</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">			onOpen</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(() </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">				refs[name].</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">focus</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">();</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">			});</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">		}</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">	};</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><h3 id="openoptions" tabindex="-1">OpenOptions <a class="header-anchor" href="#openoptions" aria-label="Permalink to &quot;OpenOptions&quot;">​</a></h3><p>表单打开的配置</p><table tabindex="0"><thead><tr><th>参数</th><th>说明</th><th>类型</th><th>可选值</th><th>默认值</th></tr></thead><tbody><tr><td>title</td><td>标题</td><td>string</td><td></td><td></td></tr><tr><td>width</td><td>宽度</td><td>string</td><td></td><td></td></tr><tr><td>items</td><td>表单项</td><td>[Items#items)</td><td></td><td></td></tr><tr><td>props</td><td>el-form 参数</td><td><a href="#formprops">FormProps</a></td><td></td><td></td></tr><tr><td>form</td><td>表单值</td><td>object</td><td></td><td></td></tr><tr><td>on</td><td>事件监听</td><td>object</td><td></td><td></td></tr><tr><td>on.open</td><td>表单打开</td><td>function(form)</td><td></td><td></td></tr><tr><td>on.close</td><td>表单关闭</td><td>function(done)</td><td></td><td></td></tr><tr><td>on.submit</td><td>表单提交</td><td>function(data, {close,done})</td><td></td><td></td></tr><tr><td>dialog</td><td>对话框参数</td><td>object</td><td></td><td></td></tr><tr><td>dialog.hiddenHeader</td><td>隐藏头部</td><td>boolean</td><td></td><td>false</td></tr><tr><td>dialog.controls</td><td>头部操作按钮</td><td>array&lt;&#39;fullscreen&#39; | &#39;close&#39;&gt;</td><td></td><td>[&quot;fullscreen&quot;, &quot;close&quot;]</td></tr><tr><td>op</td><td>底部操作按钮</td><td>object</td><td></td><td></td></tr><tr><td>op.hidden</td><td>是否隐藏</td><td>boolean</td><td></td><td>false</td></tr><tr><td>op.saveButtonText</td><td>保存按钮文案</td><td>string</td><td></td><td>保存</td></tr><tr><td>op.closeButtonText</td><td>关闭按钮文案</td><td>string</td><td></td><td>取消</td></tr><tr><td>op.buttons</td><td>按钮组</td><td>array&lt;&#39;close&#39; | &#39;save&#39; | &#39;slot-${string}&#39;&gt;</td><td></td><td>[&quot;close&quot;, &quot;save&quot;]</td></tr></tbody></table><h3 id="formprops" tabindex="-1">FormProps <a class="header-anchor" href="#formprops" aria-label="Permalink to &quot;FormProps&quot;">​</a></h3><table tabindex="0"><thead><tr><th>参数</th><th>说明</th><th>类型</th><th>可选值</th><th>默认值</th></tr></thead><tbody><tr><td>inline</td><td>行内表单模式</td><td>boolean</td><td></td><td>false</td></tr><tr><td>label-width</td><td>表单域标签的宽度</td><td>string</td><td></td><td>120px</td></tr><tr><td>label-position</td><td>表单域标签的位置</td><td>string</td><td>left / top / right</td><td>right</td></tr><tr><td>label-suffix</td><td>表单域标签的后缀</td><td>string</td><td></td><td></td></tr><tr><td>hide-required-asterisk</td><td>是否显示必填字段的标签旁边的红色星号</td><td>boolean</td><td></td><td>false</td></tr><tr><td>show-message</td><td>是否显示校验错误信息</td><td>boolean</td><td></td><td>true</td></tr><tr><td>inline-message</td><td>是否以行内形式展示校验信息</td><td>boolean</td><td></td><td>false</td></tr><tr><td>status-icon</td><td>是否在输入框中显示校验结果反馈图标</td><td>boolean</td><td></td><td>false</td></tr><tr><td>validate-on-rule-change</td><td>是否在 <code>rules</code> 属性改变后立即触发一次验证</td><td>boolean</td><td></td><td>true</td></tr><tr><td>size</td><td>用于控制该表单内组件的尺寸</td><td>string</td><td>large / default /small</td><td>default</td></tr><tr><td>disabled</td><td>是否禁用该表单内的所有组件</td><td>boolean</td><td></td><td>false</td></tr></tbody></table><h3 id="items" tabindex="-1">Items <a class="header-anchor" href="#items" aria-label="Permalink to &quot;Items&quot;">​</a></h3><table tabindex="0"><thead><tr><th>参数</th><th>说明</th><th>类型</th><th>可选值</th><th>默认值</th></tr></thead><tbody><tr><td>type</td><td>类型</td><td>string</td><td>tabs</td><td></td></tr><tr><td>prop</td><td>字段</td><td>string</td><td></td><td></td></tr><tr><td>value</td><td>默认值，对应组件 <code>component</code> 的 <code>v-model</code></td><td>any</td><td></td><td></td></tr><tr><td>props</td><td>对应 <code>component</code> 组件的 prop 参数</td><td>object</td><td></td><td></td></tr><tr><td>label</td><td>标签文本</td><td>string, RenderOptions</td><td></td><td></td></tr><tr><td><a href="#多层级展示">children</a></td><td>子集</td><td>Items</td><td></td><td></td></tr><tr><td><a href="#component">component</a></td><td>组件渲染</td><td><a href="#component">Component</a></td><td></td><td></td></tr><tr><td>component.name</td><td>组件标签名、 slot 名</td><td>string</td><td></td><td></td></tr><tr><td>component.vm</td><td>组件渲染节点</td><td>Vue.Component</td><td></td><td></td></tr><tr><td>component.style</td><td>组件样式</td><td>object</td><td></td><td></td></tr><tr><td>component.props</td><td>组件参数</td><td>object</td><td></td><td></td></tr><tr><td>component.ref</td><td>组件绑定值</td><td>setRefs(string)</td><td></td><td></td></tr><tr><td><a href="#component">prepend</a></td><td>添加到 <code>component</code> 组件前</td><td><a href="#component">Component</a></td><td></td><td></td></tr><tr><td><a href="#component">append</a></td><td>添加到 <code>component</code> 组件后</td><td><a href="#component">Component</a></td><td></td><td></td></tr><tr><td>collapse</td><td>是否折叠</td><td>boolean</td><td></td><td></td></tr><tr><td>rules</td><td>验证规则</td><td>array, object</td><td></td><td></td></tr><tr><td>required</td><td>是否必填，自动填充 rules</td><td>boolean</td><td></td><td>false</td></tr><tr><td><a href="#hidden">hidden</a></td><td>是否隐藏</td><td>boolean, string, function</td><td></td><td>false</td></tr><tr><td>span</td><td>栅格占据的列数</td><td>number</td><td></td><td>24</td></tr><tr><td>flex</td><td>是否横向拉升元素</td><td>boolean</td><td></td><td>true</td></tr><tr><td>group</td><td>分组显示</td><td>string</td><td></td><td></td></tr><tr><td><a href="#hook">hook</a></td><td>钩子模式</td><td>array / string / object / function</td><td></td><td></td></tr><tr><td>hook.bind</td><td>表单值绑定时触发数据更新</td><td>array / string / object / function</td><td></td><td></td></tr><tr><td>hook.submit</td><td>表单提交时触发数据更新</td><td>array / string / object / function</td><td></td><td></td></tr></tbody></table><div class="tip custom-block"><p class="custom-block-title">TIP</p><ol><li>静态配置</li></ol><div class="language-js vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">const</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> Form</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> useForm</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">();</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">Form.value?.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">open</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">({</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  items: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      label: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;昵称&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      prop: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;nickName&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      component: {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        name: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;el-input&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  ],</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">});</span></span></code></pre></div><ol start="2"><li>动态配置，如在 <code>upsert</code> 中新增、编辑显示不同的状态</li></ol><div class="language-js vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">const</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> Form</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> useForm</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">();</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 是否禁用</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">const</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> disabled</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> ref</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">false</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">);</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">Form.value?.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">open</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">({</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  items: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    () </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">      return</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        label: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;昵称&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        prop: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;nickName&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        component: {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">          name: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;el-input&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">          props: {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">            disabled,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">          },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      };</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  ],</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">});</span></span></code></pre></div></div><h3 id="component" tabindex="-1">Component <a class="header-anchor" href="#component" aria-label="Permalink to &quot;Component&quot;">​</a></h3><p>表单项的元素通过 <code>component</code> 渲染，该参数支持 4 中渲染方式：</p><ol><li>绑定标签</li></ol><ul><li><p>该组件必须是全局注册</p></li><li><p>该方式会自动绑定组件的 <code>v-model</code></p></li><li><p><code>props</code> 为该组件参数</p></li><li><p>对 <code>el-select</code> <code>el-checkbox-group</code> <code>el-radio-group</code> 支持配置列表数据 <code>options</code></p></li></ul><div class="language-js vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">Form.value?.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">open</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">({</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  items: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      label: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;昵称&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      prop: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      component: {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        name: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;el-input&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        props: {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">          clearable: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">true</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  ],</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">});</span></span></code></pre></div><div class="language-js vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">Form.value?.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">open</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">({</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  items: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      label: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;职业&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      prop: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;work&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      component: {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        name: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;el-select&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        options: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">          {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">            label: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;程序员&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">            value: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">          },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">          {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">            label: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;设计师&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">            value: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">          },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        ],</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  ],</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">});</span></span></code></pre></div><ol start="2"><li>万能插槽，适用于各种场景</li></ol><ul><li><p>必须以 <code>slot-</code> 开头命名</p></li><li><p><code>scope</code> 为表单值</p></li></ul><div class="language-js vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">Form.value?.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">open</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">({</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  items: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      label: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;昵称&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      prop: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      component: {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        name: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;slot-name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  ],</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">});</span></span></code></pre></div><div class="language-html vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">html</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">cl-form</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> #slot-name</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;{ scope }&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">el-input</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> v-model</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;scope.name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> /&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  &lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">cl-form</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span></code></pre></div><ol start="3"><li>使用 <code>tsx</code> 标签渲染</li></ol><ul><li><code>&lt;script lang=&quot;tsx&quot;&gt;</code></li></ul><div class="language-html vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">html</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">script</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> lang</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;tsx&quot;</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> setup</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  Form.value?.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">open</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">({</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    items: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        label: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;昵称&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        prop: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        component: &lt;</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">el-alert</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> title</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;无效昵称&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> /&gt;,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    ],</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  });</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">script</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span></code></pre></div><ol start="4"><li>使用 <code>.vue</code> 、 <code>.tsx</code> 文件或者 <code>render</code> 方法</li></ol><ul><li><p>绑定在 <code>vm</code> 上</p></li><li><p>该方式会自动绑定组件的 <code>v-model</code>，但是需要自己处理 <code>update:modelValue</code> 值的接收及更新</p></li><li><p><code>props</code> 为该组件参数</p></li></ul><div class="language-ts vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">ts</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">Form.value?.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">open</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">({</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">	items: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">		{</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">			label: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;昵称&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">			prop: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">			component: {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">				vm: {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">					name: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;test-name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">					// 接收值</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">					props: {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">						modelValue: String</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">					},</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">					setup</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">props</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> any</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, { </span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">emit</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> }</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> any</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">) {</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">						const</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> value</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> ref</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">string</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;();</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">						// 监听值变化，如果在 cl-upsert 下，第一次会为 undefined</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">						watch</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">							() </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> props.modelValue,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">							(</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">val</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">) </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">								value.value </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> val;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">							},</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">							{</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">								immediate: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">true</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">							}</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">						);</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">						return</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">							value,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">							// 更新值</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">							onInput</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">val</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> string</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">) {</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">								emit</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;update:modelValue&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, val);</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">							}</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">						};</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">					},</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">					render</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">ctx</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> any</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">) {</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">						// 绑定值</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">						return</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> &lt;</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">el</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">-</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">input</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> v</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">-</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">model</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">={</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">ctx</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">.</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">value</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">} </span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">onInput</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">={</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">ctx</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">.</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">onInput</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">} /&gt;;</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">						// 也可以使用 h 的方式渲染</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">						// return h(resolveComponent(&#39;el-input&#39;))</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">					}</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">				}</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">				props: {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">					type: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;a&quot;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">				}</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">			}</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">		}</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">	]</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">});</span></span></code></pre></div><div class="language-js vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> Test </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;./test.vue&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">Form.value?.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">open</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">({</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  items: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      label: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;昵称&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      prop: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      component: {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        vm: Test,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        props: {</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">          type: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;a&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    },</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  ],</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">});</span></span></code></pre></div><h3 id="hook" tabindex="-1">Hook <a class="header-anchor" href="#hook" aria-label="Permalink to &quot;Hook&quot;">​</a></h3><p>该参数设计于为了更方便的接收、提交参数。</p><p>当有这么一个场景，后端返回给你的 <code>idList</code> 是用 <code>,</code> 拼接的，如：</p><div class="language-js vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  idList</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;1,2,3&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><p>前端是需要你用 <code>el-select</code> 的组件展示，且需要多选模式 <code>multiple</code>。那一般的操作都是获取数据后对数据 <code>split</code> 分割，再绑定于 <code>value</code> 上。</p><p>这时候就可以用到 <code>hook</code> 参数，它可以在绑定 <code>value</code> 的时候预先处理数据：</p><div class="language-js vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	label</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;角色列表&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	prop</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;ids&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	hook</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: {</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">		bind</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: [</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;split&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;number&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">], </span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 通道流程，分割 -&gt; 转成number -&gt; 绑定值</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">	},</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	component</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: {</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">		name</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;el-select&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">		props</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: {</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">			multiple</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">true</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">		},</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">		options</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">			{</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">				label: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;李逍遥&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">				value: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">1</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">			},</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">			{</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">				label: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;景天&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">				value: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">2</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">			},</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">			{</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">				label: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;宇文拓&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">				value: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">3</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">			}</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">		]</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">	}</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 绑定的数据：</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	ids</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: [</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">2</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">3</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">]</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><p>当然有些 <code>讨厌</code> 的后端又想让你以 <code>1,2,3</code> 逗号拼接的方式提交。那你也可以用 <code>hook</code> 参数处理，用 <code>join</code> 的方式拼接：</p><div class="language-js vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	label</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;角色列表&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	prop</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;ids&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	hook</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: {</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">		bind</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: [</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;split&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;number&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">],  </span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 绑定通道流程，分割 -&gt; 转成number -&gt; 绑定值</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">		submit</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: [</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;join&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">],	</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 提交通道流程，逗号拼接 -&gt; 提交</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">	},</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	component</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: {</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">		name</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;el-select&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">		props</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: {</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">			multiple</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">true</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">		},</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">		options</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">			{</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">				label: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;李逍遥&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">				value: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">1</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">			},</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">			{</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">				label: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;景天&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">				value: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">2</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">			},</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">			{</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">				label: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;宇文拓&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">				value: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">3</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">			}</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">		]</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">	}</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 提交的数据：</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	ids</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;1,2,3&#39;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><p>hook 已有的方法</p><table tabindex="0"><thead><tr><th>名称</th><th>说明</th></tr></thead><tbody><tr><td>number</td><td>转成 <code>number</code>, 如果值是数组，那每一项都会被操作到</td></tr><tr><td>string</td><td>转成 <code>string</code>, 如果值是数组，那每一项都会被操作到</td></tr><tr><td>split</td><td>字符串以 <code>,</code> 分割为数组</td></tr><tr><td>join</td><td>数组以 <code>,</code> 拼接为字符串</td></tr><tr><td>boolean</td><td>转成 <code>boolean</code></td></tr><tr><td>booleanNumber</td><td>接收一个 boolean 值，返回 1 或 0</td></tr><tr><td>datetimeRange</td><td>在提交中会根据 <code>prop</code> 自动转换为 <code>start[prop]</code> 和 <code>end[prop]</code></td></tr><tr><td>splitJoin</td><td>绑定时 <code>split(&quot;,&quot;)</code>，提交时 <code>join(&quot;,&quot;)</code></td></tr><tr><td>json</td><td>绑定时 <code>JSON.parse()</code>，提交时 <code>JSON.stringify()</code></td></tr><tr><td>empty</td><td>等于<code>&quot;&quot;</code>时修改为 <code>undefined</code></td></tr></tbody></table><p>当然你也可以用自定义：</p><div class="language-js vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 例如 value 为：&quot;1,2,3&quot;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	hook</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: {</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">		bind</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: (</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">value</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, { </span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">form</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> }) </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">			// value 是与 prop 绑定的值</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">			// form 是表单值</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">			return</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> value.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">split</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;,&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">).</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">map</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(Number).</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">filter</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(Boolean); </span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 结果为：[1, 2, 3]</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">		},</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">		submit</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: (</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">value</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, { </span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">form</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> }) </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">			return</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> value.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">join</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;,&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">); </span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 结果为：&quot;1,2,3&quot;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">		}</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">	}</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><p>也可以多个处理：</p><div class="language-js vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">hook</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: {</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	bind</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: [</span></span>
<span class="line"><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">		&#39;split&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 1 分割</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">		() </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">			// 2 自定义</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">		},</span></span>
<span class="line"><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">		&#39;number&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 3 转成 number</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">	],</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	submit</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: [</span></span>
<span class="line"><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">		&#39;join&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 1 拼接</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">		() </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">			// 2 自定义</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">		}</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">	]</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><p style="color:red;">注册全局hook</p><div class="language-js vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { registerFormHook } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;@cool-vue/crud&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">registerFormHook</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pca&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, (</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">value</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, { </span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">method</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">form</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">prop</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> }) </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  if</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> (method </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">==</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;bind&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">) {</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">    return</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> [form.province, form.city, form.district];</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">else</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">    const</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> [</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">province</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">city</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">district</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">] </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> value </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">||</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> [];</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    form.province </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> province;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    form.city </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> city;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    form.district </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> district;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    form[prop] </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> undefined</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  }</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">});</span></span></code></pre></div><h3 id="hidden" tabindex="-1">Hidden <a class="header-anchor" href="#hidden" aria-label="Permalink to &quot;Hidden&quot;">​</a></h3><div class="language-js vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 默认填入 boolean</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	label</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;姓名&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	prop</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	hidden</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">false</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// scope 为表单数据，自定义返回值</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	label</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;姓名&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	prop</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	hidden</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: ({ </span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">scope</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> }) </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">		return</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> !</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">scope.showName</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">	}</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 使用 ref</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">const</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> isHidden</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> ref</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">false</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">)</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	label</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;姓名&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	prop</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	hidden</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: isHidden</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 使用 computed</span></span>
<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">const</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> isHidden</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> computed</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(() </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> false</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">)</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	label</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;姓名&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	prop</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">	hidden</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: isHidden</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div></div></div></main><footer class="VPDocFooter" data-v-2eca86da data-v-b4f8e1c3><!--[--><!--]--><div class="edit-info" data-v-b4f8e1c3><div class="edit-link" data-v-b4f8e1c3><a class="VPLink link vp-external-link-icon no-icon edit-link-button" href="https://github.com/cool-team-official/cool-admin-vue-docs/blob/main/src/guide/crud/form.md" target="_blank" rel="noreferrer" data-v-b4f8e1c3><!--[--><span class="vpi-square-pen edit-link-icon" data-v-b4f8e1c3></span> 在GitHub上编辑<!--]--></a></div><div class="last-updated" data-v-b4f8e1c3><p class="VPLastUpdated" data-v-b4f8e1c3 data-v-8f99b711>最后更新于: <time datetime="2024-07-15T17:29:04.000Z" data-v-8f99b711></time></p></div></div><nav class="prev-next" aria-labelledby="doc-footer-aria-label" data-v-b4f8e1c3><span class="visually-hidden" id="doc-footer-aria-label" data-v-b4f8e1c3>Pager</span><div class="pager" data-v-b4f8e1c3><a class="VPLink link pager-link prev" href="/src/guide/crud/crud.html" data-v-b4f8e1c3><!--[--><span class="desc" data-v-b4f8e1c3>上一页</span><span class="title" data-v-b4f8e1c3>cl-crud</span><!--]--></a></div><div class="pager" data-v-b4f8e1c3><a class="VPLink link pager-link next" href="/src/guide/crud/upsert.html" data-v-b4f8e1c3><!--[--><span class="desc" data-v-b4f8e1c3>下一页</span><span class="title" data-v-b4f8e1c3>cl-upsert</span><!--]--></a></div></nav></footer><!--[--><!--]--></div></div></div><!--[--><!--]--></div></div><footer class="VPFooter has-sidebar" data-v-eb4c655e data-v-dbcfea6c><div class="container" data-v-dbcfea6c><p class="message" data-v-dbcfea6c>COOL为开发者而生</p><p class="copyright" data-v-dbcfea6c><a href="https://beian.miit.gov.cn">Copyright © COOL | 闽ICP备**********号</a></p></div></footer><!--[--><!--]--></div></div>
    <script>window.__VP_HASH_MAP__=JSON.parse("{\"index.md\":\"D0g3JbeM\",\"readme.md\":\"DsAtAE3r\",\"src_about_index.md\":\"BNFp5QDb\",\"src_guide_ai.md\":\"wmOrXn42\",\"src_guide_app.vue.md\":\"DU2kYlcz\",\"src_guide_config.md\":\"_7Asinuc\",\"src_guide_cool_index.vue.md\":\"Jl2e4Gui\",\"src_guide_cool_router.md\":\"Bcu-nNiA\",\"src_guide_cool_service.md\":\"Cj9X5NKm\",\"src_guide_crud_add-btn.md\":\"C_fkqzWx\",\"src_guide_crud_adv-search.md\":\"Cuu1gyRN\",\"src_guide_crud_context-menu.md\":\"DHy8bh4x\",\"src_guide_crud_crud.md\":\"CIJsN-K-\",\"src_guide_crud_dialog.md\":\"pyelD6mR\",\"src_guide_crud_form.md\":\"LfUa36IP\",\"src_guide_crud_multi-delete-btn.md\":\"Ced4saY0\",\"src_guide_crud_pagination.md\":\"BSbvc1R2\",\"src_guide_crud_refresh-btn.md\":\"CwgDZEmr\",\"src_guide_crud_search-key.md\":\"C4ZejlZv\",\"src_guide_crud_search.md\":\"CH0u9c09\",\"src_guide_crud_table.md\":\"atnu8smG\",\"src_guide_crud_upsert.md\":\"BxnfNq2J\",\"src_guide_dev.md\":\"iiyXfDGu\",\"src_guide_flow.md\":\"aa2BWB0s\",\"src_guide_main.ts.md\":\"BACyZY6k\",\"src_guide_modules_base.md\":\"BDfv21zj\",\"src_guide_modules_dict.md\":\"BpsV3OuU\",\"src_guide_modules_helper.md\":\"sQiJUTYT\",\"src_guide_modules_recycle.md\":\"C6zx9-Ff\",\"src_guide_modules_space.md\":\"B9RTHGSm\",\"src_guide_modules_task.md\":\"DZeZe6CO\",\"src_guide_modules_user.md\":\"CYeAylM7\",\"src_guide_packages.md\":\"BTZuEIg4\",\"src_guide_permission.md\":\"Do5jv9El\",\"src_guide_plugins_crud.md\":\"CBXrNvet\",\"src_guide_plugins_dev-tools.md\":\"BtyyMXs4\",\"src_guide_plugins_distpicker.md\":\"C5KKKoYH\",\"src_guide_plugins_echarts.md\":\"TzqvEkSW\",\"src_guide_plugins_editor-monaco.md\":\"TjKumDYd\",\"src_guide_plugins_editor-preview.md\":\"DAXzWA--\",\"src_guide_plugins_editor-wang.md\":\"CwwVj6bz\",\"src_guide_plugins_element-ui.md\":\"C1GMcQ3f\",\"src_guide_plugins_excel.md\":\"BVNADENm\",\"src_guide_plugins_i18n.md\":\"G4eCsXUO\",\"src_guide_plugins_iconfont.md\":\"CXS-ihkQ\",\"src_guide_plugins_tailwind.md\":\"DNO2nGZv\",\"src_guide_plugins_theme.md\":\"CWOnCL2S\",\"src_guide_plugins_upload.md\":\"C-n-8dPX\",\"src_guide_plugins_view.md\":\"DI414zw8\",\"src_guide_quick.md\":\"DKO1VHsV\",\"src_introduce_index.md\":\"nh1Sv4HX\",\"src_introduce_show.md\":\"B99dq_sZ\",\"src_introduce_src.md\":\"P3-V-b3v\",\"src_todo_plan.md\":\"ChtgAh5R\",\"src_todo_update.md\":\"pmnwUt7r\",\"src_todo_upgrade.md\":\"4JKb3k66\"}");window.__VP_SITE_DATA__=JSON.parse("{\"lang\":\"en-US\",\"dir\":\"ltr\",\"title\":\"Cool Admin\",\"description\":\"一个很酷的后台管理系统开发框架\",\"base\":\"/\",\"head\":[],\"router\":{\"prefetchLinks\":true},\"appearance\":true,\"themeConfig\":{\"logo\":\"/logo.png\",\"search\":{\"provider\":\"local\"},\"footer\":{\"message\":\"COOL为开发者而生\",\"copyright\":\"<a href=\\\"https://beian.miit.gov.cn\\\">Copyright © COOL | 闽ICP备**********号</a>\"},\"docFooter\":{\"prev\":\"上一页\",\"next\":\"下一页\"},\"outline\":{\"label\":\"页面导航\",\"level\":[2,3]},\"lastUpdated\":{\"text\":\"最后更新于\",\"formatOptions\":{\"dateStyle\":\"short\",\"timeStyle\":\"medium\"}},\"langMenuLabel\":\"多语言\",\"returnToTopLabel\":\"回到顶部\",\"sidebarMenuLabel\":\"菜单\",\"darkModeSwitchLabel\":\"主题\",\"lightModeSwitchTitle\":\"切换到浅色模式\",\"darkModeSwitchTitle\":\"切换到深色模式\",\"editLink\":{\"text\":\"在GitHub上编辑\",\"pattern\":\"https://github.com/cool-team-official/cool-admin-vue-docs/blob/main/:path\"},\"nav\":[{\"text\":\"介绍\",\"link\":\"/src/introduce/index.md\",\"noIcon\":false},{\"text\":\"教程\",\"link\":\"/src/guide/quick.md\"},{\"text\":\"CRUD 组件\",\"link\":\"/src/guide/plugins/crud\"},{\"text\":\"🔥插件市场\",\"link\":\"https://cool-js.com/plugin\"},{\"text\":\"交流合作\",\"link\":\"/src/about/index.md\"},{\"text\":\"v8.0.0\",\"items\":[{\"text\":\"更新日志\",\"link\":\"/src/todo/update.md\"}]},{\"text\":\"更多\",\"items\":[{\"text\":\"Cool Admin(Nodejs版)\",\"link\":\"https://node.cool-admin.com\"},{\"text\":\"Cool Admin(Java版)\",\"link\":\"https://java.cool-admin.com\"},{\"text\":\"Uni（基于uni-app跨端移动端开发）\",\"link\":\"https://uni-docs.cool-js.com\"}]}],\"sidebar\":[{\"text\":\"介绍\",\"items\":[{\"text\":\"简介\",\"link\":\"/src/introduce/index.md\"},{\"text\":\"演示 / 示例\",\"link\":\"/src/introduce/show.md\"},{\"text\":\"源码\",\"link\":\"/src/introduce/src.md\"}]},{\"text\":\"教程\",\"items\":[{\"text\":\"快速开始\",\"link\":\"/src/guide/quick.md\"},{\"text\":\"Ai编码\",\"link\":\"/src/guide/ai.md\"},{\"text\":\"Ai流程编排\",\"link\":\"/src/guide/flow.md\"},{\"text\":\"模块/插件开发\",\"link\":\"/src/guide/dev.md\"},{\"text\":\"权限\",\"link\":\"/src/guide/permission.md\"},{\"text\":\"目录结构\",\"items\":[{\"text\":\"packages\",\"link\":\"/src/guide/packages.md\"},{\"text\":\"config\",\"link\":\"/src/guide/config.md\"},{\"text\":\"cool\",\"items\":[{\"text\":\"index.vue\",\"link\":\"/src/guide/cool/index.vue.md\"},{\"text\":\"router\",\"link\":\"/src/guide/cool/router.md\"},{\"text\":\"service\",\"link\":\"/src/guide/cool/service.md\"}]},{\"text\":\"modules\",\"items\":[{\"text\":\"base\",\"link\":\"/src/guide/modules/base.md\"},{\"text\":\"dict\",\"link\":\"/src/guide/modules/dict.md\"},{\"text\":\"helper\",\"link\":\"/src/guide/modules/helper.md\"},{\"text\":\"recycle\",\"link\":\"/src/guide/modules/recycle.md\"},{\"text\":\"space\",\"link\":\"/src/guide/modules/space.md\"},{\"text\":\"task\",\"link\":\"/src/guide/modules/task.md\"},{\"text\":\"user\",\"link\":\"/src/guide/modules/user.md\"}]},{\"text\":\"plugins\",\"items\":[{\"text\":\"crud\",\"link\":\"/src/guide/plugins/crud.md\"},{\"text\":\"dev-tools\",\"link\":\"/src/guide/plugins/dev-tools.md\"},{\"text\":\"distpicker\",\"link\":\"/src/guide/plugins/distpicker.md\"},{\"text\":\"echarts\",\"link\":\"/src/guide/plugins/echarts.md\"},{\"text\":\"editor-preview\",\"link\":\"/src/guide/plugins/editor-preview.md\"},{\"text\":\"editor-wang\",\"link\":\"/src/guide/plugins/editor-wang.md\"},{\"text\":\"element-ui\",\"link\":\"/src/guide/plugins/element-ui.md\"},{\"text\":\"excel\",\"link\":\"/src/guide/plugins/excel.md\"},{\"text\":\"i18n\",\"link\":\"/src/guide/plugins/i18n.md\"},{\"text\":\"iconfont\",\"link\":\"/src/guide/plugins/iconfont.md\"},{\"text\":\"tailwind\",\"link\":\"/src/guide/plugins/tailwind.md\"},{\"text\":\"theme\",\"link\":\"/src/guide/plugins/theme.md\"},{\"text\":\"upload\",\"link\":\"/src/guide/plugins/upload.md\"},{\"text\":\"view\",\"link\":\"/src/guide/plugins/view.md\"}]},{\"text\":\"App.vue\",\"link\":\"/src/guide/App.vue.md\"},{\"text\":\"main.ts\",\"link\":\"/src/guide/main.ts.md\"}]},{\"text\":\"CRUD 组件\",\"items\":[{\"text\":\"cl-crud\",\"link\":\"/src/guide/crud/crud.md\"},{\"text\":\"cl-form\",\"link\":\"/src/guide/crud/form.md\"},{\"text\":\"cl-upsert\",\"link\":\"/src/guide/crud/upsert.md\"},{\"text\":\"cl-table\",\"link\":\"/src/guide/crud/table.md\"},{\"text\":\"cl-add-btn\",\"link\":\"/src/guide/crud/add-btn.md\"},{\"text\":\"cl-adv-search\",\"link\":\"/src/guide/crud/adv-search.md\"},{\"text\":\"cl-dialog\",\"link\":\"/src/guide/crud/dialog.md\"},{\"text\":\"cl-search-key\",\"link\":\"/src/guide/crud/search-key.md\"},{\"text\":\"cl-search\",\"link\":\"/src/guide/crud/search.md\"},{\"text\":\"cl-refresh-btn\",\"link\":\"/src/guide/crud/refresh-btn.md\"},{\"text\":\"cl-context-menu\",\"link\":\"/src/guide/crud/context-menu.md\"},{\"text\":\"cl-multi-delete-btn\",\"link\":\"/src/guide/crud/multi-delete-btn.md\"},{\"text\":\"cl-pagination\",\"link\":\"/src/guide/crud/pagination.md\"}]}]},{\"text\":\"更新日志\",\"link\":\"/src/todo/update.md\"},{\"text\":\"交流合作\",\"link\":\"/src/about/index.md\"}],\"socialLinks\":[{\"icon\":\"github\",\"link\":\"https://github.com/cool-team-official/cool-admin-vue\"}]},\"locales\":{},\"scrollOffset\":134,\"cleanUrls\":false}");</script>
    
  </body>
</html>