:root {
  --vp-home-hero-name-color: transparent;
  --vp-home-hero-name-background: -webkit-linear-gradient(
    120deg,
    #bd34fe 30%,
    #41d1ff
  );

  --vp-home-hero-image-background-image: linear-gradient(
    -45deg,
    #bd34fe 50%,
    #47caff 50%
  );
  --vp-home-hero-image-filter: blur(44px);
}

@media (min-width: 640px) {
  :root {
    --vp-home-hero-image-filter: blur(56px);
  }
}

@media (min-width: 960px) {
  :root {
    --vp-home-hero-image-filter: blur(68px);
  }
}

@media (min-width: 960px) {
  :root {
    .image-src {
      max-width: 400px;
      max-height: 320px;
    }
  }
}

@media (min-width: 960px) {
  .VPHero.has-image {
    .name {
      padding-bottom: 20px;
    }
    .text {
      padding-bottom: 2px;
    }
  }
}

.medium-zoom-overlay {
  z-index: 1000;
}

.medium-zoom-image {
  z-index: 1000;
}
