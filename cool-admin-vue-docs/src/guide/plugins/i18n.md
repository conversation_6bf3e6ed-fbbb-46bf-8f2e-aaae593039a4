# 国际化

国际化插件，支持多语言切换。

## 配置

在 `config/index.ts` 中配置：

```js
{
  i18n: {
    locale: "zh-cn",
    languages: [
      {
        label: "中文",
        value: "zh-cn",
      },
      {
        label: "繁体中文",
        value: "zh-tw",
      },
      {
        label: "English",
        value: "en",
      },
    ];
  }
}
```

## 使用

需要翻译的内容需要在 `$t()` `t()` 中使用，例如：

```js
// html
$t("你好");

// js
const { t } = useI18n();
t("你好");
```

内容使用 `中文`，ai 会全文检索并自动翻译成其他语言。

## 翻译

安装翻译工具

```cmd
pnpm i @cool-vue/ai -g
```

运行命令

```cmd
创建翻译文件
cool-i18n create

清除翻译文件
cool-i18n clear

添加模块/插件翻译文件，例如：
cool-i18n add plugins/crud
cool-i18n add module/user
```

执行完成后，会在对应的模块、插件目录下生成 `locale` 目录。

## 语言配置对照表

- 简体中文（zh-cn）
- 美国英语（en）
- 阿塞拜疆语（az）
- 德语（de）
- 葡萄牙语（pt）
- 西班牙语（es）
- 丹麦语（da）
- 法语（fr）
- 挪威语（nb-NO）
- 繁体中文（zh-tw）
- 意大利语（it）
- 韩语（ko）
- 日语（ja）
- 荷兰语（nl）
- 越南语（vi）
- 俄语（ru）
- 土耳其语（tr）
- 巴西葡萄牙语（pt-br）
- 波斯语（fa）
- 泰语（th）
- 印度尼西亚语（id）
- 保加利亚语（bg）
- 普什图语（pa）
- 波兰语（pl）
- 芬兰语（fi）
- 瑞典语（sv）
- 希腊语（el）
- 斯洛伐克语（sk）
- 加泰罗尼亚语（ca）
- 捷克语（cs）
- 乌克兰语（uk）
- 土库曼语（tk）
- 泰米尔语（ta）
- 拉脱维亚语（lv）
- 南非荷兰语（af）
- 爱沙尼亚语（et）
- 斯洛文尼亚语（sl）
- 阿拉伯语（ar）
- 希伯来语（he）
- 立陶宛语（lt）
- 蒙古语（mn）
- 哈萨克语（kk）
- 匈牙利语（hu）
- 罗马尼亚语（ro）
- 库尔德语（ku）
- 库尔德语 (ckb)
- 维吾尔语 (ug-cn)
- 高棉语 (km)
- 塞尔维亚语 (sr)
- 巴斯克语 (eu)
- 吉尔吉斯语 (ky)
- 亚美尼亚语 (hy-am)
- 克罗地亚语（hr）
- 世界语 (eo)
- 孟加拉语 (bn)
- 马来语 (ms)
