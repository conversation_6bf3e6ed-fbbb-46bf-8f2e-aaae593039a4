# 升级方案（v8.x）

以下是从 7.x 升级到 8.x 的方案

:::danger 警告！！！

我们不建议将已经在运营的项目直接升级到最新的大版本，因为可能会遇到一些无法预知的问题。
:::

## 升级步骤

### 1. 代码迁移

- 获取最新的代码；
- 将原有项目的`模块`、`插件`复制到新项目中；
- 检查原有项目框架中的模块改动，例如 `base` 模块，如有修改，根据改动内容进行调整；
- 8.x 使用 `cl-search` 作为搜索组件，支持更多配置和扩展；
- 移除了模块中的`service`注入，自定义请求使用 `service.request()`；
- 移除了地址栏的`proxy`参数，`切换代理`改用可视化操作；
- 使用多语言时，必须使用 `$t()` `t()` 方法，翻译时会全文检索：

```html
<div>你好</div>
替换成：
<div>{{ $t('你好')}}</div>
```

```js
const { t } = useI18n();

ElMessage.success("你好");
// 替换成：
ElMessage.success(t("你好"));
```
