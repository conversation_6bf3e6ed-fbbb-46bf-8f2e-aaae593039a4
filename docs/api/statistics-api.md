# 统计报表 API 接口文档

本文档描述了一物一码系统中统计报表相关的 API 接口，包括验证统计和活动统计。

## 基础信息

- **基础路径**: `/opoc/statistics`
- **请求方式**: GET
- **响应格式**: JSON
- **认证方式**: 需要 Token 认证

## 1. 验证统计 API

### 1.1 获取验证统计概览

获取验证统计的概览数据，包括总验证次数、今日验证次数、真假比例等。

**请求路径**

```
GET /verification/overview
```

**请求参数**

无

**响应示例**

```json
{
  "code": 1000,
  "message": "操作成功",
  "data": {
    "totalCount": 1250,
    "todayCount": 45,
    "validCount": 1180,
    "invalidCount": 70,
    "validRate": 94.4,
    "lastWeekTrend": [
      {
        "time": "2024-05-18",
        "value": 35,
        "valid": 33,
        "invalid": 2
      },
      {
        "time": "2024-05-19",
        "value": 42,
        "valid": 40,
        "invalid": 2
      },
      // ...更多数据
    ]
  }
}
```

**响应参数说明**

| 参数名 | 类型 | 说明 |
|-------|------|------|
| totalCount | number | 总验证次数 |
| todayCount | number | 今日验证次数 |
| validCount | number | 真品验证次数 |
| invalidCount | number | 假冒验证次数 |
| validRate | number | 真品率(%) |
| lastWeekTrend | array | 最近一周的验证趋势 |
| lastWeekTrend[].time | string | 日期 |
| lastWeekTrend[].value | number | 验证总次数 |
| lastWeekTrend[].valid | number | 真品次数 |
| lastWeekTrend[].invalid | number | 假冒次数 |

### 1.2 获取验证趋势

获取指定时间范围内的验证趋势数据。

**请求路径**

```
GET /verification/trend
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| timeRange | string | 否 | 时间范围类型，可选值：day, week, month, year, custom，默认为 month |
| startTime | string | 否 | 开始时间，当 timeRange=custom 时必填，格式：YYYY-MM-DD |
| endTime | string | 否 | 结束时间，当 timeRange=custom 时必填，格式：YYYY-MM-DD |

**响应示例**

```json
{
  "code": 1000,
  "message": "操作成功",
  "data": [
    {
      "time": "2024-04-25",
      "value": 35,
      "valid": 33,
      "invalid": 2
    },
    {
      "time": "2024-04-26",
      "value": 42,
      "valid": 40,
      "invalid": 2
    },
    // ...更多数据
  ]
}
```

**响应参数说明**

| 参数名 | 类型 | 说明 |
|-------|------|------|
| time | string | 日期 |
| value | number | 验证总次数 |
| valid | number | 真品次数 |
| invalid | number | 假冒次数 |

### 1.3 获取验证地理分布

获取指定时间范围内的验证地理分布数据。

**请求路径**

```
GET /verification/geo
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| timeRange | string | 否 | 时间范围类型，可选值：day, week, month, year, custom，默认为 month |
| startTime | string | 否 | 开始时间，当 timeRange=custom 时必填，格式：YYYY-MM-DD |
| endTime | string | 否 | 结束时间，当 timeRange=custom 时必填，格式：YYYY-MM-DD |

**响应示例**

```json
{
  "code": 1000,
  "message": "操作成功",
  "data": [
    {
      "location": "北京市",
      "count": 120
    },
    {
      "location": "上海市",
      "count": 98
    },
    // ...更多数据
  ]
}
```

**响应参数说明**

| 参数名 | 类型 | 说明 |
|-------|------|------|
| location | string | 地理位置 |
| count | number | 验证次数 |

## 2. 活动统计 API

### 2.1 获取活动统计概览

获取活动统计的概览数据，包括活动总数、参与人次、中奖率等。

**请求路径**

```
GET /activity/overview
```

**请求参数**

无

**响应示例**

```json
{
  "code": 1000,
  "message": "操作成功",
  "data": {
    "totalActivities": 15,
    "ongoingActivities": 3,
    "totalParticipations": 2500,
    "totalWinners": 350,
    "winRate": 14.0,
    "lastWeekTrend": [
      {
        "time": "2024-05-18",
        "value": 120,
        "winners": 18
      },
      {
        "time": "2024-05-19",
        "value": 135,
        "winners": 20
      },
      // ...更多数据
    ]
  }
}
```

**响应参数说明**

| 参数名 | 类型 | 说明 |
|-------|------|------|
| totalActivities | number | 活动总数 |
| ongoingActivities | number | 进行中的活动数 |
| totalParticipations | number | 参与总人次 |
| totalWinners | number | 中奖总人次 |
| winRate | number | 中奖率(%) |
| lastWeekTrend | array | 最近一周的参与趋势 |
| lastWeekTrend[].time | string | 日期 |
| lastWeekTrend[].value | number | 参与人次 |
| lastWeekTrend[].winners | number | 中奖人次 |

### 2.2 获取活动参与趋势

获取指定时间范围内的活动参与趋势数据。

**请求路径**

```
GET /activity/trend
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| timeRange | string | 否 | 时间范围类型，可选值：day, week, month, year, custom，默认为 month |
| startTime | string | 否 | 开始时间，当 timeRange=custom 时必填，格式：YYYY-MM-DD |
| endTime | string | 否 | 结束时间，当 timeRange=custom 时必填，格式：YYYY-MM-DD |

**响应示例**

```json
{
  "code": 1000,
  "message": "操作成功",
  "data": [
    {
      "time": "2024-04-25",
      "value": 120,
      "winners": 18
    },
    {
      "time": "2024-04-26",
      "value": 135,
      "winners": 20
    },
    // ...更多数据
  ]
}
```

**响应参数说明**

| 参数名 | 类型 | 说明 |
|-------|------|------|
| time | string | 日期 |
| value | number | 参与人次 |
| winners | number | 中奖人次 |

### 2.3 获取活动排行榜

获取活动参与人次排行榜。

**请求路径**

```
GET /activity/ranking
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| limit | number | 否 | 返回数量限制，默认为 10 |

**响应示例**

```json
{
  "code": 1000,
  "message": "操作成功",
  "data": [
    {
      "id": 5,
      "activityName": "618购物节抽奖",
      "participationCount": 850,
      "winnerCount": 120
    },
    {
      "id": 3,
      "activityName": "新品上市有奖问答",
      "participationCount": 720,
      "winnerCount": 80
    },
    // ...更多数据
  ]
}
```

**响应参数说明**

| 参数名 | 类型 | 说明 |
|-------|------|------|
| id | number | 活动ID |
| activityName | string | 活动名称 |
| participationCount | number | 参与人次 |
| winnerCount | number | 中奖人次 |

## 错误码说明

| 错误码 | 说明 |
|-------|------|
| 1000 | 操作成功 |
| 1001 | 参数错误 |
| 1002 | 未授权 |
| 1003 | 服务器内部错误 |
| 1004 | 数据不存在 |
