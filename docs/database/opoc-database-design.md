# 一物一码系统数据库设计文档

## 1. 概述

本文档描述了一物一码系统的数据库设计，包括表结构、字段说明、索引设计和关系模型。该系统主要用于产品防伪溯源，支持产品码生成、验证、溯源等功能。

## 2. 数据库表设计

### 2.1 产品码表 (opoc_product_code)

存储系统中所有的产品唯一标识码。

| 字段名 | 类型 | 是否为空 | 默认值 | 说明 |
|-------|------|---------|-------|------|
| id | int(11) | 否 | AUTO_INCREMENT | 主键ID |
| tenantId | int(11) | 是 | NULL | 租户ID |
| code | varchar(255) | 否 | | 标识码 |
| productId | int(11) | 是 | NULL | 产品ID |
| status | int(11) | 否 | 0 | 标识码状态（0:未使用, 1:已绑定, 2:已激活, 3:已失效） |
| activateTime | datetime | 是 | NULL | 激活时间 |
| verifyCount | int(11) | 否 | 0 | 验证次数 |
| verifyLimit | int(11) | 是 | 0 | 验证次数限制 |
| lastVerifyTime | datetime | 是 | NULL | 最后验证时间 |
| createTime | datetime(6) | 否 | CURRENT_TIMESTAMP(6) | 创建时间 |
| updateTime | datetime(6) | 否 | CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) | 更新时间 |

**索引设计**：
- 主键索引：`id`
- 唯一索引：`code`
- 普通索引：`productId`, `status`

### 2.2 产品信息表 (opoc_product_info)

存储产品的基本信息。

| 字段名 | 类型 | 是否为空 | 默认值 | 说明 |
|-------|------|---------|-------|------|
| id | int(11) | 否 | AUTO_INCREMENT | 主键ID |
| tenantId | int(11) | 是 | NULL | 租户ID |
| productCode | varchar(100) | 否 | | 产品编码 |
| productName | varchar(200) | 否 | | 产品名称 |
| skuCode | varchar(100) | 是 | NULL | SKU编码 |
| skuName | varchar(200) | 是 | NULL | SKU名称 |
| batchNo | varchar(100) | 是 | NULL | 批次号 |
| produceDate | datetime | 是 | NULL | 生产日期 |
| expireDate | datetime | 是 | NULL | 过期日期 |
| image | varchar(500) | 是 | NULL | 产品图片URL |
| description | text | 是 | NULL | 产品描述 |
| status | int(11) | 否 | 1 | 产品状态（1:正常, 2:已过期, 3:已报废） |
| createTime | datetime(6) | 否 | CURRENT_TIMESTAMP(6) | 创建时间 |
| updateTime | datetime(6) | 否 | CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) | 更新时间 |

**索引设计**：
- 主键索引：`id`
- 普通索引：`productCode`, `skuCode`, `batchNo`, `status`

### 2.3 验证日志表 (opoc_verification_log)

记录产品码的验证历史。

| 字段名 | 类型 | 是否为空 | 默认值 | 说明 |
|-------|------|---------|-------|------|
| id | int(11) | 否 | AUTO_INCREMENT | 主键ID |
| tenantId | int(11) | 是 | NULL | 租户ID |
| codeId | int(11) | 否 | | 标识码ID |
| verifyTime | datetime | 否 | | 验证时间 |
| verifyResult | int(11) | 否 | 0 | 验证结果（0:真, 1:假） |
| verifyLocation | varchar(255) | 是 | NULL | 验证地理位置 |
| longitude | decimal(10,6) | 是 | NULL | 经度 |
| latitude | decimal(10,6) | 是 | NULL | 纬度 |
| ip | varchar(50) | 是 | NULL | IP地址 |
| deviceInfo | varchar(255) | 是 | NULL | 设备信息 |
| createTime | datetime(6) | 否 | CURRENT_TIMESTAMP(6) | 创建时间 |
| updateTime | datetime(6) | 否 | CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) | 更新时间 |

**索引设计**：
- 主键索引：`id`
- 普通索引：`codeId`, `verifyTime`, `verifyResult`

### 2.4 溯源记录表 (opoc_traceability)

记录产品的溯源信息。

| 字段名 | 类型 | 是否为空 | 默认值 | 说明 |
|-------|------|---------|-------|------|
| id | int(11) | 否 | AUTO_INCREMENT | 主键ID |
| tenantId | int(11) | 是 | NULL | 租户ID |
| code | varchar(100) | 否 | | 产品码 |
| productId | int(11) | 是 | NULL | 产品ID |
| productName | varchar(100) | 是 | NULL | 产品名称 |
| nodeType | varchar(50) | 否 | | 节点类型 |
| nodeName | varchar(100) | 否 | | 节点名称 |
| nodeTime | datetime | 否 | | 节点时间 |
| operator | varchar(50) | 是 | NULL | 操作人 |
| location | varchar(100) | 是 | NULL | 位置 |
| longitude | decimal(10,6) | 是 | NULL | 经度 |
| latitude | decimal(10,6) | 是 | NULL | 纬度 |
| status | int(11) | 否 | 0 | 状态（0:正常, 1:异常） |
| remark | varchar(500) | 是 | NULL | 备注 |
| imageUrls | varchar(1000) | 是 | NULL | 图片URL，多个用逗号分隔 |
| extData | text | 是 | NULL | 扩展数据，JSON格式 |
| createTime | datetime(6) | 否 | CURRENT_TIMESTAMP(6) | 创建时间 |
| updateTime | datetime(6) | 否 | CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) | 更新时间 |

**索引设计**：
- 主键索引：`id`
- 普通索引：`code`, `productId`, `nodeType`, `nodeTime`, `status`

### 2.5 营销活动表 (opoc_marketing_activity)

存储营销活动信息。

| 字段名 | 类型 | 是否为空 | 默认值 | 说明 |
|-------|------|---------|-------|------|
| id | int(11) | 否 | AUTO_INCREMENT | 主键ID |
| tenantId | int(11) | 是 | NULL | 租户ID |
| activityName | varchar(100) | 否 | | 活动名称 |
| activityType | varchar(50) | 否 | | 活动类型 |
| startTime | datetime | 否 | | 开始时间 |
| endTime | datetime | 否 | | 结束时间 |
| rules | text | 是 | NULL | 活动规则，JSON格式 |
| rewards | text | 是 | NULL | 奖励设置，JSON格式 |
| status | int(11) | 否 | 0 | 状态（0:未开始, 1:进行中, 2:已结束, 3:已取消） |
| createTime | datetime(6) | 否 | CURRENT_TIMESTAMP(6) | 创建时间 |
| updateTime | datetime(6) | 否 | CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) | 更新时间 |

**索引设计**：
- 主键索引：`id`
- 普通索引：`activityType`, `startTime`, `endTime`, `status`

### 2.6 活动参与记录表 (opoc_activity_record)

记录用户参与活动的信息。

| 字段名 | 类型 | 是否为空 | 默认值 | 说明 |
|-------|------|---------|-------|------|
| id | int(11) | 否 | AUTO_INCREMENT | 主键ID |
| tenantId | int(11) | 是 | NULL | 租户ID |
| activityId | int(11) | 否 | | 活动ID |
| codeId | int(11) | 否 | | 产品码ID |
| userId | int(11) | 是 | NULL | 用户ID |
| participateTime | datetime | 否 | | 参与时间 |
| result | varchar(50) | 是 | NULL | 参与结果 |
| reward | varchar(500) | 是 | NULL | 获得奖励 |
| status | int(11) | 否 | 0 | 状态（0:进行中, 1:已完成, 2:已失效） |
| createTime | datetime(6) | 否 | CURRENT_TIMESTAMP(6) | 创建时间 |
| updateTime | datetime(6) | 否 | CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) | 更新时间 |

**索引设计**：
- 主键索引：`id`
- 普通索引：`activityId`, `codeId`, `userId`, `participateTime`, `status`

## 3. 表关系

- `opoc_product_code.productId` → `opoc_product_info.id`：产品码关联产品信息
- `opoc_verification_log.codeId` → `opoc_product_code.id`：验证日志关联产品码
- `opoc_traceability.productId` → `opoc_product_info.id`：溯源记录关联产品信息
- `opoc_traceability.code` → `opoc_product_code.code`：溯源记录关联产品码
- `opoc_activity_record.activityId` → `opoc_marketing_activity.id`：活动记录关联活动
- `opoc_activity_record.codeId` → `opoc_product_code.id`：活动记录关联产品码

## 4. 数据字典

### 4.1 产品码状态 (opoc_product_code.status)

| 值 | 说明 |
|----|------|
| 0 | 未使用 |
| 1 | 已绑定 |
| 2 | 已激活 |
| 3 | 已失效 |

### 4.2 产品状态 (opoc_product_info.status)

| 值 | 说明 |
|----|------|
| 1 | 正常 |
| 2 | 已过期 |
| 3 | 已报废 |

### 4.3 验证结果 (opoc_verification_log.verifyResult)

| 值 | 说明 |
|----|------|
| 0 | 真品 |
| 1 | 假冒 |

### 4.4 溯源节点状态 (opoc_traceability.status)

| 值 | 说明 |
|----|------|
| 0 | 正常 |
| 1 | 异常 |

### 4.5 活动状态 (opoc_marketing_activity.status)

| 值 | 说明 |
|----|------|
| 0 | 未开始 |
| 1 | 进行中 |
| 2 | 已结束 |
| 3 | 已取消 |

### 4.6 活动参与记录状态 (opoc_activity_record.status)

| 值 | 说明 |
|----|------|
| 0 | 进行中 |
| 1 | 已完成 |
| 2 | 已失效 |
