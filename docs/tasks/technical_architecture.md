# 一物一码系统技术架构设计

本文档描述了一物一码系统的技术架构设计，包括系统架构、数据库设计、接口设计和技术选型等内容。

## 1. 系统架构

### 1.1 整体架构

一物一码系统采用前后端分离的架构，主要包括以下几个部分：

1. **前端应用**
   - 管理后台（基于Vue.js）
   - 移动端应用（基于Vue.js + Vant/Uni-app）
   - 公众号/小程序（基于微信开发框架）

2. **后端服务**
   - API服务（基于Midway.js）
   - 业务逻辑层
   - 数据访问层

3. **数据存储**
   - 关系型数据库（MySQL）
   - 缓存服务（Redis）
   - 文件存储（OSS/本地存储）

4. **第三方集成**
   - ERP系统接口
   - CRM系统接口
   - WMS系统接口
   - 支付服务
   - 短信服务

### 1.2 技术架构图

```
+------------------+    +------------------+    +------------------+
|   前端应用层      |    |    API网关       |    |   第三方系统     |
|                  |    |                  |    |                  |
| +-------------+  |    | +-------------+  |    | +-------------+  |
| | 管理后台     |  |    | | 认证授权    |  |    | | ERP系统     |  |
| +-------------+  |    | +-------------+  |    | +-------------+  |
|                  |    |                  |    |                  |
| +-------------+  |    | +-------------+  |    | +-------------+  |
| | 移动端应用   |  |    | | 请求路由    |  |    | | CRM系统     |  |
| +-------------+  |    | +-------------+  |    | +-------------+  |
|                  |    |                  |    |                  |
| +-------------+  |    | +-------------+  |    | +-------------+  |
| | 小程序/公众号 |  |    | | 负载均衡    |  |    | | WMS系统     |  |
| +-------------+  |    | +-------------+  |    | +-------------+  |
+------------------+    +------------------+    +------------------+
          |                      |                      |
          v                      v                      v
+------------------------------------------------------------------+
|                           业务服务层                              |
|                                                                  |
| +-------------+  +-------------+  +-------------+  +-------------+|
| | 码生成管理  |  | 码绑定管理  |  | 防伪验证    |  | 溯源管理    ||
| +-------------+  +-------------+  +-------------+  +-------------+|
|                                                                  |
| +-------------+  +-------------+  +-------------+  +-------------+|
| | 营销活动    |  | 数据分析    |  | 用户管理    |  | 系统管理    ||
| +-------------+  +-------------+  +-------------+  +-------------+|
+------------------------------------------------------------------+
                                |
                                v
+------------------------------------------------------------------+
|                           数据存储层                              |
|                                                                  |
| +-------------+  +-------------+  +-------------+  +-------------+|
| | MySQL数据库 |  | Redis缓存   |  | 文件存储    |  | 日志系统    ||
| +-------------+  +-------------+  +-------------+  +-------------+|
+------------------------------------------------------------------+
```

## 2. 数据库设计

### 2.1 核心表结构

#### 产品码表（opoc_product_code）
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| code | varchar(255) | 标识码 |
| productId | int | 产品ID |
| status | int | 标识码状态（0:未使用, 1:已绑定, 2:已激活, 3:已失效） |
| activateTime | datetime | 激活时间 |
| verifyCount | int | 验证次数 |
| lastVerifyTime | datetime | 最后验证时间 |
| createTime | datetime | 创建时间 |
| updateTime | datetime | 更新时间 |

#### 产品信息表（opoc_product_info）
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| productCode | varchar(50) | 产品编码 |
| productName | varchar(100) | 产品名称 |
| skuCode | varchar(50) | 产品SKU编码 |
| skuName | varchar(50) | 产品SKU名称 |
| image | varchar(255) | 产品图片 |
| batchNo | varchar(50) | 生产批次 |
| produceDate | datetime | 生产日期 |
| expireDate | datetime | 有效期至 |
| status | int | 产品状态 |
| description | varchar(500) | 产品描述 |
| createTime | datetime | 创建时间 |
| updateTime | datetime | 更新时间 |

#### 验证日志表（opoc_verification_log）
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| codeId | int | 标识码ID |
| verifyTime | datetime | 验证时间 |
| verifyResult | int | 验证结果（0:真, 1:假） |
| verifyLocation | varchar(255) | 验证地理位置 |
| longitude | decimal(10,6) | 经度 |
| latitude | decimal(10,6) | 纬度 |
| ip | varchar(50) | IP地址 |
| deviceInfo | varchar(255) | 设备信息 |
| createTime | datetime | 创建时间 |

#### 溯源记录表（opoc_traceability）
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| code | varchar(100) | 产品码 |
| productId | int | 产品ID |
| productName | varchar(100) | 产品名称 |
| nodeType | varchar(50) | 节点类型 |
| nodeName | varchar(100) | 节点名称 |
| nodeTime | datetime | 节点时间 |
| operator | varchar(50) | 操作人 |
| location | varchar(100) | 位置 |
| longitude | decimal(10,6) | 经度 |
| latitude | decimal(10,6) | 纬度 |
| remark | varchar(500) | 备注 |
| createTime | datetime | 创建时间 |
| updateTime | datetime | 更新时间 |

### 2.2 待添加的表结构

#### 编码规则表（opoc_code_rule）
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| ruleName | varchar(50) | 规则名称 |
| ruleDesc | varchar(255) | 规则描述 |
| ruleContent | text | 规则内容（JSON格式） |
| status | int | 状态（0:禁用, 1:启用） |
| createTime | datetime | 创建时间 |
| updateTime | datetime | 更新时间 |

#### 营销活动表（opoc_marketing_activity）
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| activityName | varchar(100) | 活动名称 |
| activityType | int | 活动类型（1:抽奖, 2:积分, 3:优惠券） |
| startTime | datetime | 开始时间 |
| endTime | datetime | 结束时间 |
| ruleContent | text | 规则内容（JSON格式） |
| status | int | 状态（0:未开始, 1:进行中, 2:已结束, 3:已取消） |
| createTime | datetime | 创建时间 |
| updateTime | datetime | 更新时间 |

#### 活动参与记录表（opoc_activity_record）
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| activityId | int | 活动ID |
| codeId | int | 标识码ID |
| userId | int | 用户ID |
| participateTime | datetime | 参与时间 |
| rewardType | int | 奖励类型 |
| rewardContent | varchar(255) | 奖励内容 |
| status | int | 状态（0:未领取, 1:已领取） |
| createTime | datetime | 创建时间 |
| updateTime | datetime | 更新时间 |

## 3. 接口设计

### 3.1 RESTful API设计

系统采用RESTful风格的API设计，主要包括以下几类接口：

#### 码管理接口
- `POST /admin/opoc/code/generate` - 生成产品码
- `POST /admin/opoc/code/bind` - 绑定产品码
- `POST /admin/opoc/code/activate` - 激活产品码
- `POST /admin/opoc/code/batch-export` - 批量导出产品码
- `POST /admin/opoc/code/batch-bind` - 批量绑定产品码
- `GET /admin/opoc/code/info` - 获取产品码信息
- `GET /admin/opoc/code/list` - 获取产品码列表
- `GET /admin/opoc/code/page` - 分页获取产品码列表

#### 产品管理接口
- `POST /admin/opoc/product/add` - 添加产品信息
- `POST /admin/opoc/product/update` - 更新产品信息
- `POST /admin/opoc/product/delete` - 删除产品信息
- `POST /admin/opoc/product/batch-import` - 批量导入产品信息
- `GET /admin/opoc/product/info` - 获取产品信息
- `GET /admin/opoc/product/list` - 获取产品列表
- `GET /admin/opoc/product/page` - 分页获取产品列表

#### 验证接口
- `GET /app/opoc/code/verify` - 验证产品码
- `GET /admin/opoc/verification/stats` - 获取验证统计数据
- `GET /admin/opoc/verification/log` - 获取验证日志

#### 溯源接口
- `POST /admin/opoc/traceability/add` - 添加溯源节点
- `GET /app/opoc/traceability/report` - 获取溯源报告
- `GET /admin/opoc/traceability/warning` - 获取溯源预警信息

### 3.2 与第三方系统集成接口

#### ERP系统集成接口
- `POST /api/erp/sync-product` - 同步产品信息
- `POST /api/erp/sync-inventory` - 同步库存信息
- `GET /api/erp/product-info` - 获取ERP产品信息

#### CRM系统集成接口
- `POST /api/crm/sync-user` - 同步用户信息
- `POST /api/crm/sync-activity` - 同步营销活动信息

#### WMS系统集成接口
- `POST /api/wms/sync-logistics` - 同步物流信息
- `POST /api/wms/sync-inventory` - 同步库存信息

## 4. 技术选型

### 4.1 前端技术栈

- **框架**：Vue.js 3.x
- **UI组件库**：Element Plus
- **状态管理**：Pinia
- **路由**：Vue Router
- **HTTP客户端**：Axios
- **构建工具**：Vite
- **移动端UI**：Vant/Uni-app

### 4.2 后端技术栈

- **框架**：Midway.js (基于Node.js)
- **ORM**：TypeORM
- **数据库**：MySQL 8.x
- **缓存**：Redis
- **文件存储**：阿里云OSS/本地文件系统
- **身份认证**：JWT
- **日志**：Winston
- **任务调度**：Bull

### 4.3 部署环境

- **容器化**：Docker
- **编排**：Docker Compose
- **CI/CD**：Jenkins/GitHub Actions
- **监控**：Prometheus + Grafana
- **日志收集**：ELK Stack

## 5. 安全设计

### 5.1 数据安全

- 敏感数据加密存储
- 数据库定期备份
- 数据传输加密（HTTPS）

### 5.2 接口安全

- API密钥认证
- 请求签名验证
- 接口访问频率限制
- CSRF防护

### 5.3 身份认证与授权

- 基于JWT的身份认证
- 基于RBAC的权限控制
- 多因素认证（可选）

## 6. 性能优化

### 6.1 数据库优化

- 索引优化
- 查询优化
- 分表分库（根据数据量增长情况）

### 6.2 缓存策略

- 热点数据缓存
- 查询结果缓存
- 页面缓存

### 6.3 负载均衡

- 应用服务器集群
- 数据库读写分离
- CDN加速静态资源
