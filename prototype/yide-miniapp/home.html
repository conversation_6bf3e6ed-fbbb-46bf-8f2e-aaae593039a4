<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - 一物一码系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        }
        .phone-container {
            width: 390px;
            height: 844px;
            background-color: white;
            border-radius: 40px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background-color: #000;
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            font-size: 14px;
        }
        .bottom-tab {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background-color: white;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #eee;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #999;
        }
        .tab-item.active {
            color: #07C160;
        }
        .scan-button {
            width: 180px;
            height: 180px;
            border-radius: 50%;
            background: linear-gradient(135deg, #07C160, #10aeff);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 10px 25px rgba(7, 193, 96, 0.3);
            margin: 0 auto;
            transition: all 0.3s;
        }
        .scan-button:hover {
            transform: scale(1.05);
        }
        .feature-card {
            background-color: #f8f9fa;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .feature-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background-color: #e6f7ff;
            color: #0086f6;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <span>20:21</span>
            <div class="flex items-center">
                <i class="fas fa-signal mr-2"></i>
                <i class="fas fa-wifi mr-2"></i>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <div class="w-full h-14 bg-white flex items-center justify-center">
            <h1 class="text-lg font-medium">一物一码系统</h1>
        </div>
        
        <!-- 主体内容 -->
        <div class="p-6">
            <div class="text-center mb-4">
                <p class="text-sm text-gray-500 mb-10">扫描二维码，查询产品信息</p>
                
                <!-- 扫码按钮 -->
                <div class="scan-button mb-10">
                    <i class="fas fa-qrcode text-white text-6xl"></i>
                </div>
                
                <p class="text-sm text-gray-600 mt-2">点击扫码</p>
            </div>
            
            <!-- 功能介绍 -->
            <div class="mt-10">
                <h2 class="text-lg font-medium mb-4">功能介绍</h2>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div>
                        <h3 class="text-base font-medium">防伪验证</h3>
                        <p class="text-xs text-gray-500">验证产品真伪，确保您购买的是正品</p>
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon" style="background-color: #e6fff6; color: #00b578;">
                        <i class="fas fa-route"></i>
                    </div>
                    <div>
                        <h3 class="text-base font-medium">产品溯源</h3>
                        <p class="text-xs text-gray-500">查看产品全生命周期信息，了解产品来源</p>
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon" style="background-color: #fff0e6; color: #ff9500;">
                        <i class="fas fa-gift"></i>
                    </div>
                    <div>
                        <h3 class="text-base font-medium">营销活动</h3>
                        <p class="text-xs text-gray-500">参与丰富多彩的营销活动，赢取精美礼品</p>
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon" style="background-color: #f5e6ff; color: #8f4bff;">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div>
                        <h3 class="text-base font-medium">售后服务</h3>
                        <p class="text-xs text-gray-500">一码在手，售后无忧，便捷的售后服务体验</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 底部Tab栏 -->
        <div class="bottom-tab">
            <div class="tab-item active">
                <i class="fas fa-home text-xl"></i>
                <span class="text-xs mt-1">首页</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-search text-xl"></i>
                <span class="text-xs mt-1">查询</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-gift text-xl"></i>
                <span class="text-xs mt-1">活动</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-user text-xl"></i>
                <span class="text-xs mt-1">我的</span>
            </div>
        </div>
    </div>
</body>
</html> 