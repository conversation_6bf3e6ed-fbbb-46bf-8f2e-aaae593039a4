<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品详情 - 一物一码系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        }
        .phone-container {
            width: 390px;
            height: 844px;
            background-color: #f7f8fa;
            border-radius: 40px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background-color: #000;
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            font-size: 14px;
        }
        .nav-bar {
            height: 56px;
            background-color: white;
            display: flex;
            align-items: center;
            padding: 0 15px;
            position: relative;
        }
        .bottom-tab {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background-color: white;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #eee;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #999;
        }
        .tab-item.active {
            color: #07C160;
        }
        .product-image {
            width: 100%;
            height: 220px;
            object-fit: cover;
            border-radius: 10px;
        }
        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .info-label {
            width: 80px;
            color: #888;
            font-size: 14px;
        }
        .info-value {
            flex: 1;
            font-size: 14px;
        }
        .verification-result {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            background-color: white;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .authentic-badge {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #07C160;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
        }
        .action-button {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12px 0;
            background-color: white;
            border-radius: 8px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <span>20:21</span>
            <div class="flex items-center">
                <i class="fas fa-signal mr-2"></i>
                <i class="fas fa-wifi mr-2"></i>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <div class="nav-bar">
            <a href="javascript:history.back()">
                <i class="fas fa-chevron-left text-gray-600"></i>
            </a>
            <h1 class="text-lg font-medium absolute left-1/2 transform -translate-x-1/2">产品详情</h1>
        </div>
        
        <!-- 主体内容 -->
        <div class="p-4">
            <!-- 防伪验证结果 -->
            <div class="verification-result">
                <div class="authentic-badge">
                    <i class="fas fa-check text-2xl"></i>
                </div>
                <h2 class="text-lg font-bold text-center">正品</h2>
                <p class="text-sm text-gray-500 text-center mt-1">本次验证时间：2023-06-20 14:30:25</p>
                <p class="text-xs text-gray-400 text-center mt-1">验证次数：1次</p>
            </div>
            
            <!-- 产品图片 -->
            <img src="https://images.pexels.com/photos/6003387/pexels-photo-6003387.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="电动车" class="product-image mb-4">
            
            <!-- 产品基本信息 -->
            <div class="bg-white rounded-lg p-4 mb-4 shadow-sm">
                <h2 class="text-lg font-medium mb-4">产品信息</h2>
                
                <div class="info-item">
                    <span class="info-label">产品名称</span>
                    <span class="info-value font-medium">电动自行车 锂电标准版</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">产品编码</span>
                    <span class="info-value">XC20230615001</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">生产日期</span>
                    <span class="info-value">2023-06-15</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">生产批次</span>
                    <span class="info-value">XCB202306-A</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">电池类型</span>
                    <span class="info-value">20A锂电池</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">整车重量</span>
                    <span class="info-value">25kg</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">续航里程</span>
                    <span class="info-value">80km</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">最高时速</span>
                    <span class="info-value">25km/h</span>
                </div>
            </div>
            
            <!-- 功能按钮 -->
            <div class="grid grid-cols-2 gap-3 mb-5">
                <a href="trace-info.html" class="action-button">
                    <i class="fas fa-route text-blue-500 mr-2"></i>
                    <span class="text-sm">产品溯源</span>
                </a>
                <div class="action-button">
                    <i class="fas fa-headset text-green-500 mr-2"></i>
                    <span class="text-sm">售后服务</span>
                </div>
                <div class="action-button">
                    <i class="fas fa-gift text-orange-500 mr-2"></i>
                    <span class="text-sm">相关活动</span>
                </div>
                <div class="action-button">
                    <i class="fas fa-share-alt text-purple-500 mr-2"></i>
                    <span class="text-sm">分享</span>
                </div>
            </div>
        </div>
        
        <!-- 底部Tab栏 -->
        <div class="bottom-tab">
            <div class="tab-item">
                <i class="fas fa-home text-xl"></i>
                <span class="text-xs mt-1">首页</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-search text-xl"></i>
                <span class="text-xs mt-1">查询</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-gift text-xl"></i>
                <span class="text-xs mt-1">活动</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-user text-xl"></i>
                <span class="text-xs mt-1">我的</span>
            </div>
        </div>
    </div>
</body>
</html> 