<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 一物一码系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        }
        .phone-container {
            width: 390px;
            height: 844px;
            background-color: #f7f8fa;
            border-radius: 40px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background-color: #000;
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            font-size: 14px;
        }
        .bottom-tab {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background-color: white;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #eee;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #999;
        }
        .tab-item.active {
            color: #07C160;
        }
        .user-header {
            background: linear-gradient(135deg, #0086f6, #24b0ed);
            padding: 30px 20px;
            color: white;
            position: relative;
        }
        .avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            overflow: hidden;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }
        .avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .menu-group {
            background-color: white;
            border-radius: 10px;
            margin: 15px;
            overflow: hidden;
        }
        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px;
            position: relative;
        }
        .menu-item:not(:last-child)::after {
            content: '';
            position: absolute;
            left: 16px;
            right: 16px;
            bottom: 0;
            height: 1px;
            background-color: #f5f5f5;
        }
        .menu-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: #0086f6;
        }
        .menu-text {
            flex: 1;
            font-size: 15px;
        }
        .badge {
            background-color: #ff4d4f;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            margin-right: 5px;
        }
        .grid-menu {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            padding: 20px 15px;
            background-color: white;
            border-radius: 10px;
            margin: 0 15px 15px;
        }
        .grid-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 0;
        }
        .grid-icon {
            width: 50px;
            height: 50px;
            background-color: #f0f6ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #0086f6;
            margin-bottom: 8px;
        }
        .grid-text {
            font-size: 12px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <span>20:21</span>
            <div class="flex items-center">
                <i class="fas fa-signal mr-2"></i>
                <i class="fas fa-wifi mr-2"></i>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <div class="w-full h-14 bg-white flex items-center justify-center">
            <h1 class="text-lg font-medium">个人中心</h1>
        </div>
        
        <!-- 主体内容 -->
        <div class="pb-20 overflow-auto" style="height: calc(100% - 56px - 80px - 44px);">
            <!-- 用户信息头部 -->
            <div class="user-header">
                <div class="flex items-center">
                    <div class="avatar">
                        <img src="https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="用户头像">
                    </div>
                    <div class="ml-4">
                        <h2 class="text-xl font-medium">张小明</h2>
                        <p class="text-sm mt-1 opacity-80">积分：3,620</p>
                    </div>
                </div>
                <div class="flex justify-between mt-4">
                    <div class="text-center">
                        <div class="text-lg font-medium">5</div>
                        <div class="text-xs opacity-80">我的产品</div>
                    </div>
                    <div class="text-center">
                        <div class="text-lg font-medium">12</div>
                        <div class="text-xs opacity-80">查询记录</div>
                    </div>
                    <div class="text-center">
                        <div class="text-lg font-medium">8</div>
                        <div class="text-xs opacity-80">参与活动</div>
                    </div>
                </div>
            </div>
            
            <!-- 功能网格菜单 -->
            <div class="grid-menu">
                <div class="grid-item">
                    <div class="grid-icon">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <div class="grid-text">扫码查询</div>
                </div>
                <div class="grid-item">
                    <div class="grid-icon" style="background-color: #fff0f6; color: #eb2f96;">
                        <i class="fas fa-gift"></i>
                    </div>
                    <div class="grid-text">我的奖品</div>
                </div>
                <div class="grid-item">
                    <div class="grid-icon" style="background-color: #f6ffed; color: #52c41a;">
                        <i class="fas fa-bicycle"></i>
                    </div>
                    <div class="grid-text">我的车辆</div>
                </div>
                <div class="grid-item">
                    <div class="grid-icon" style="background-color: #fff7e6; color: #fa8c16;">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="grid-text">保养记录</div>
                </div>
            </div>
            
            <!-- 菜单列表 -->
            <div class="menu-group">
                <div class="menu-item">
                    <div class="menu-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <div class="menu-text">查询历史</div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                <div class="menu-item">
                    <div class="menu-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div class="menu-text">我的收藏</div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                <div class="menu-item">
                    <div class="menu-icon">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <div class="menu-text">优惠券</div>
                    <span class="badge">3</span>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <div class="menu-group">
                <div class="menu-item">
                    <div class="menu-icon">
                        <i class="fas fa-id-card"></i>
                    </div>
                    <div class="menu-text">个人资料</div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                <div class="menu-item">
                    <div class="menu-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="menu-text">消息通知</div>
                    <span class="badge">5</span>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                <div class="menu-item">
                    <div class="menu-icon">
                        <i class="fas fa-address-book"></i>
                    </div>
                    <div class="menu-text">通讯录管理</div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <div class="menu-group">
                <div class="menu-item">
                    <div class="menu-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="menu-text">联系客服</div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                <div class="menu-item">
                    <div class="menu-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="menu-text">帮助中心</div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                <a href="settings.html" class="menu-item">
                    <div class="menu-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="menu-text">设置</div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </a>
            </div>
        </div>
        
        <!-- 底部Tab栏 -->
        <div class="bottom-tab">
            <div class="tab-item">
                <i class="fas fa-home text-xl"></i>
                <span class="text-xs mt-1">首页</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-search text-xl"></i>
                <span class="text-xs mt-1">查询</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-gift text-xl"></i>
                <span class="text-xs mt-1">活动</span>
            </div>
            <div class="tab-item active">
                <i class="fas fa-user text-xl"></i>
                <span class="text-xs mt-1">我的</span>
            </div>
        </div>
    </div>
</body>
</html> 