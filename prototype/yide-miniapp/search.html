<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品查询 - 一物一码系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        }
        .phone-container {
            width: 390px;
            height: 844px;
            background-color: #f7f8fa;
            border-radius: 40px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background-color: #000;
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            font-size: 14px;
        }
        .bottom-tab {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background-color: white;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #eee;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #999;
        }
        .tab-item.active {
            color: #07C160;
        }
        .search-box {
            background-color: white;
            border-radius: 8px;
            padding: 10px 15px;
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .search-input {
            border: none;
            flex-grow: 1;
            padding: 8px 12px;
            font-size: 14px;
            outline: none;
        }
        .history-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background-color: white;
            border-radius: 10px;
            margin-bottom: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .history-icon {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            background-color: #f0f6ff;
            color: #418cff;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <span>20:21</span>
            <div class="flex items-center">
                <i class="fas fa-signal mr-2"></i>
                <i class="fas fa-wifi mr-2"></i>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <div class="w-full h-14 bg-white flex items-center justify-center">
            <h1 class="text-lg font-medium">产品查询</h1>
        </div>
        
        <!-- 主体内容 -->
        <div class="p-4">
            <!-- 搜索框 -->
            <div class="search-box">
                <i class="fas fa-search text-gray-400"></i>
                <input type="text" class="search-input" placeholder="请输入产品编码或扫描二维码">
                <button class="bg-blue-500 text-white py-1 px-3 rounded-md text-sm">查询</button>
            </div>
            
            <!-- 扫码按钮 -->
            <div class="flex justify-center my-6">
                <button class="flex items-center px-4 py-2 bg-white rounded-full shadow-sm">
                    <i class="fas fa-qrcode text-blue-500 mr-2"></i>
                    <span class="text-sm">扫描二维码</span>
                </button>
            </div>
            
            <!-- 历史记录 -->
            <div>
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-base font-medium">历史记录</h2>
                    <button class="text-sm text-gray-500">
                        <i class="fas fa-trash-alt mr-1"></i>
                        清空
                    </button>
                </div>
                
                <div class="history-item">
                    <div class="history-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex justify-between">
                            <h3 class="text-base font-medium">XC20230615001</h3>
                            <span class="text-xs text-gray-500">2023-06-15</span>
                        </div>
                        <p class="text-xs text-gray-500">电动车 - 锂电标准版</p>
                    </div>
                </div>
                
                <div class="history-item">
                    <div class="history-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex justify-between">
                            <h3 class="text-base font-medium">XC20230520003</h3>
                            <span class="text-xs text-gray-500">2023-05-20</span>
                        </div>
                        <p class="text-xs text-gray-500">电池 - 20A锂电池</p>
                    </div>
                </div>
                
                <div class="history-item">
                    <div class="history-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex justify-between">
                            <h3 class="text-base font-medium">XC20230428002</h3>
                            <span class="text-xs text-gray-500">2023-04-28</span>
                        </div>
                        <p class="text-xs text-gray-500">电动车 - 豪华增程版</p>
                    </div>
                </div>
                
                <div class="history-item">
                    <div class="history-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex justify-between">
                            <h3 class="text-base font-medium">XC20230410005</h3>
                            <span class="text-xs text-gray-500">2023-04-10</span>
                        </div>
                        <p class="text-xs text-gray-500">控制器 - 智能控制器S2</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 底部Tab栏 -->
        <div class="bottom-tab">
            <div class="tab-item">
                <i class="fas fa-home text-xl"></i>
                <span class="text-xs mt-1">首页</span>
            </div>
            <div class="tab-item active">
                <i class="fas fa-search text-xl"></i>
                <span class="text-xs mt-1">查询</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-gift text-xl"></i>
                <span class="text-xs mt-1">活动</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-user text-xl"></i>
                <span class="text-xs mt-1">我的</span>
            </div>
        </div>
    </div>
</body>
</html> 