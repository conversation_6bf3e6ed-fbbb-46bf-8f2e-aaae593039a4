<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置 - 一物一码系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        }
        .phone-container {
            width: 390px;
            height: 844px;
            background-color: #f7f8fa;
            border-radius: 40px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background-color: #000;
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            font-size: 14px;
        }
        .nav-bar {
            height: 56px;
            background-color: white;
            display: flex;
            align-items: center;
            padding: 0 15px;
            position: relative;
        }
        .bottom-tab {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background-color: white;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #eee;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #999;
        }
        .tab-item.active {
            color: #07C160;
        }
        .menu-group {
            background-color: white;
            border-radius: 10px;
            margin: 15px;
            overflow: hidden;
        }
        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px;
            position: relative;
        }
        .menu-item:not(:last-child)::after {
            content: '';
            position: absolute;
            left: 16px;
            right: 16px;
            bottom: 0;
            height: 1px;
            background-color: #f5f5f5;
        }
        .menu-text {
            flex: 1;
            font-size: 15px;
        }
        .switch {
            position: relative;
            width: 50px;
            height: 28px;
            background-color: #e9e9eb;
            border-radius: 28px;
            transition: all 0.3s;
        }
        .switch.on {
            background-color: #07C160;
        }
        .switch-handle {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.15);
            transition: all 0.3s;
        }
        .switch.on .switch-handle {
            left: 24px;
        }
        .version-info {
            text-align: center;
            font-size: 13px;
            color: #999;
            padding: 30px 0;
        }
        .logout-btn {
            background-color: white;
            color: #ff4d4f;
            border-radius: 10px;
            text-align: center;
            margin: 15px;
            padding: 16px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <span>20:21</span>
            <div class="flex items-center">
                <i class="fas fa-signal mr-2"></i>
                <i class="fas fa-wifi mr-2"></i>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <div class="nav-bar">
            <a href="javascript:history.back()">
                <i class="fas fa-chevron-left text-gray-600"></i>
            </a>
            <h1 class="text-lg font-medium absolute left-1/2 transform -translate-x-1/2">设置</h1>
        </div>
        
        <!-- 主体内容 -->
        <div class="pb-20 overflow-auto" style="height: calc(100% - 100px);">
            <!-- 通用设置 -->
            <div class="menu-group">
                <div class="menu-item">
                    <div class="menu-text">账号与安全</div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                <div class="menu-item">
                    <div class="menu-text">隐私设置</div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                <div class="menu-item">
                    <div class="menu-text">通知管理</div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <!-- 功能设置 -->
            <div class="menu-group">
                <div class="menu-item">
                    <div class="menu-text">消息推送</div>
                    <div class="switch on">
                        <div class="switch-handle"></div>
                    </div>
                </div>
                <div class="menu-item">
                    <div class="menu-text">扫码自动保存</div>
                    <div class="switch on">
                        <div class="switch-handle"></div>
                    </div>
                </div>
                <div class="menu-item">
                    <div class="menu-text">活动提醒</div>
                    <div class="switch">
                        <div class="switch-handle"></div>
                    </div>
                </div>
                <div class="menu-item">
                    <div class="menu-text">开启位置服务</div>
                    <div class="switch">
                        <div class="switch-handle"></div>
                    </div>
                </div>
            </div>
            
            <!-- 其他设置 -->
            <div class="menu-group">
                <div class="menu-item">
                    <div class="menu-text">清除缓存</div>
                    <span class="text-gray-400 text-sm">12.8MB</span>
                </div>
                <div class="menu-item">
                    <div class="menu-text">关于我们</div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                <div class="menu-item">
                    <div class="menu-text">用户协议</div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                <div class="menu-item">
                    <div class="menu-text">隐私政策</div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                <div class="menu-item">
                    <div class="menu-text">联系我们</div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <!-- 版本信息 -->
            <div class="version-info">
                <p>当前版本 1.0.5</p>
                <p class="mt-2">Copyright © 2023 一物一码系统</p>
            </div>
            
            <!-- 退出登录 -->
            <div class="logout-btn">
                退出登录
            </div>
        </div>
        
        <!-- 底部Tab栏 -->
        <div class="bottom-tab">
            <div class="tab-item">
                <i class="fas fa-home text-xl"></i>
                <span class="text-xs mt-1">首页</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-search text-xl"></i>
                <span class="text-xs mt-1">查询</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-gift text-xl"></i>
                <span class="text-xs mt-1">活动</span>
            </div>
            <div class="tab-item active">
                <i class="fas fa-user text-xl"></i>
                <span class="text-xs mt-1">我的</span>
            </div>
        </div>
    </div>
</body>
</html> 