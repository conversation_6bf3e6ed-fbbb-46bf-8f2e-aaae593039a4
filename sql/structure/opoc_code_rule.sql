-- ----------------------------
-- Table structure for opoc_code_rule
-- ----------------------------
DROP TABLE IF EXISTS `opoc_code_rule`;
CREATE TABLE `opoc_code_rule` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `tenantId` int(11) DEFAULT NULL COMMENT '租户ID',
  `createTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `name` varchar(50) NOT NULL COMMENT '规则名称',
  `type` varchar(20) NOT NULL COMMENT '规则类型',
  `prefix` varchar(20) DEFAULT NULL COMMENT '规则前缀',
  `length` int(11) NOT NULL DEFAULT '8' COMMENT '规则长度',
  `includeLetters` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否包含字母',
  `includeNumbers` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否包含数字',
  `includeSpecialChars` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否包含特殊字符',
  `isDefault` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认规则',
  `description` varchar(255) DEFAULT NULL COMMENT '规则描述',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态 0-禁用 1-启用',
  PRIMARY KEY (`id`),
  KEY `IDX_opoc_code_rule_name` (`name`),
  KEY `IDX_opoc_code_rule_type` (`type`),
  KEY `IDX_opoc_code_rule_isDefault` (`isDefault`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='编码规则表';

-- ----------------------------
-- Records of opoc_code_rule
-- ----------------------------
BEGIN;
INSERT INTO `opoc_code_rule` VALUES (1, NULL, NOW(6), NOW(6), '默认混合编码', 'mixed', 'YW', 16, 1, 1, 0, 1, '默认的字母数字混合编码规则', 1);
INSERT INTO `opoc_code_rule` VALUES (2, NULL, NOW(6), NOW(6), '纯数字编码', 'number', 'N', 12, 0, 1, 0, 0, '仅包含数字的编码规则', 1);
INSERT INTO `opoc_code_rule` VALUES (3, NULL, NOW(6), NOW(6), '纯字母编码', 'letter', 'L', 12, 1, 0, 0, 0, '仅包含字母的编码规则', 1);
COMMIT;
