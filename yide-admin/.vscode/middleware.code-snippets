{"middleware": {"prefix": "middleware", "body": ["import { Middleware } from '@midwayjs/core';", "import { NextFunction, Context } from '@midwayjs/koa';", "import { IMiddleware } from '@midwayjs/core';", "", "/**", " * 描述", " */", "@Middleware()", "export class XxxMiddleware implements IMiddleware<Context, NextFunction> {", "  resolve() {", "    return async (ctx: Context, next: NextFunction) => {", "      // 控制器前执行的逻辑", "      const startTime = Date.now();", "      // 执行下一个 Web 中间件，最后执行到控制器", "      await next();", "      // 控制器之后执行的逻辑", "      console.log(Date.now() - startTime);", "    };", "  }", "}", ""], "description": "cool-admin middleware代码片段"}}