<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<meta http-equiv="X-UA-Compatible" content="ie=edge" />
	<title>COOL-AMIND 一个很酷的后台权限管理系统</title>
	<meta name="keywords" content="cool-admin，后台管理系统，vue，element-ui，nodejs" />
	<meta name="description" content="element-ui、midway.js、mysql、redis、node.js、前后端分离、权限管理、快速开发， COOL-AMIND 一个很酷的后台权限管理系统" />
	<link rel="stylesheet" href="css/welcome.css">
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon" />

<body>
<div class="reveal">HELLO COOL-ADMIN AI快速开发框架</div>

<!-- 添加底部说明 -->
<div class="footer-bar">
	<span>当前版本：v8.x</span>
	<div class="notice">
		<span>本项目采用前后端分离架构，这是后端服务。</span>
		<span>前端项目请访问：</span>
		<a class="link" target="_blank" href="https://vue.cool-admin.com/">COOL-ADMIN 前端</a>
	</div>
</div>

<script src="js/welcome.js"></script>
</body>

</html>
