import { ModuleConfig } from '@cool-midway/core';

/**
 * 一物一码模块配置
 */
export default () => {
  return {
    // 模块名称
    name: '一物一码',
    // 模块描述
    description: '一物一码管理系统，实现产品防伪溯源',
    // 中间件，只对本模块有效
    middlewares: [],
    // 全局中间件，影响所有模块
    globalMiddlewares: [],
    // 模块加载顺序，默认为0，值越大越优先加载
    order: 0,
    // 模块配置
    config: {
      // 码生成配置
      code: {
        // 码长度
        length: 32,
        // 码前缀
        prefix: 'YW',
        // 验证次数限制
        verifyLimit: 10,
      },
      // 接口配置
      api: {
        // 接口版本
        version: 'v1',
        // 接口前缀
        prefix: '/opoc',
      },
    },
  } as ModuleConfig;
};
