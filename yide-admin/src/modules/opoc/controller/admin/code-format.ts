import { Base<PERSON><PERSON>roller, CoolController } from '@cool-midway/core'
import { Body, Get, Inject, Post, Provide } from '@midwayjs/core'
import { OpocCodeFormatService } from '../../service/code-format'

/**
 * 码格式选择管理
 */
@Provide()
@CoolController()
export class AdminOpocCodeFormatController extends BaseController {
  @Inject()
  opocCodeFormatService: OpocCodeFormatService;

  /**
   * 保存码格式配置
   */
  @Post('/save')
  async save(@Body() body: {
    type: 'random' | 'number' | 'letter' | 'mixed' | 'custom';
    length: number;
    includeLetters?: boolean;
    includeNumbers?: boolean;
    includeSpecialChars?: boolean;
    prefix?: string;
  }) {
    return this.ok(await this.opocCodeFormatService.saveFormat(body));
  }

  /**
   * 获取码格式配置
   */
  @Get('/info')
  async info() {
    return this.ok(await this.opocCodeFormatService.getFormat());
  }

  /**
   * 生成预览码
   */
  @Post('/preview')
  async preview(@Body() body: {
    type: 'random' | 'number' | 'letter' | 'mixed' | 'custom';
    length: number;
    includeLetters?: boolean;
    includeNumbers?: boolean;
    includeSpecialChars?: boolean;
    prefix?: string;
  }) {
    return this.ok(await this.opocCodeFormatService.generatePreview(body));
  }
}
