import { BaseController, CoolController } from '@cool-midway/core';
import { Body, Inject, Post, Provide } from '@midwayjs/core';
import { OpocCodeRuleEntity } from '../../entity/code-rule';
import { OpocCodeRuleService } from '../../service/code-rule';

/**
 * 编码规则配置
 */
@Provide()
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: OpocCodeRuleEntity,
  service: OpocCodeRuleService,
  pageQueryOp: {
    keyWordLikeFields: ['name', 'type', 'prefix'],
    fieldEq: ['status', 'isDefault'],
  },
})
export class AdminOpocCodeRuleController extends BaseController {
  @Inject()
  opocCodeRuleService: OpocCodeRuleService;

  /**
   * 设置默认规则
   */
  @Post('/setDefault')
  async setDefault(@Body('id') id: number) {
    return this.ok(await this.opocCodeRuleService.setDefault(id));
  }

  /**
   * 生成预览码
   */
  @Post('/preview')
  async preview(@Body() params: {
    type: string;
    length: number;
    includeLetters?: boolean;
    includeNumbers?: boolean;
    includeSpecialChars?: boolean;
    prefix?: string;
  }) {
    return this.ok(await this.opocCodeRuleService.generatePreview(params));
  }
}
