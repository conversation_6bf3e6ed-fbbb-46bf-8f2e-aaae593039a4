import { Base<PERSON><PERSON>roller, CoolController } from '@cool-midway/core'
import { Body, Inject, Post, Provide } from '@midwayjs/core'
import { OpocProductCodeEntity } from '../../entity/product-code'
import { OpocProductCodeService } from '../../service/product-code'
import { SetVerifyLimitParams } from '../../typings/opoc'

/**
 * 产品码验证次数限制管理
 */
@Provide()
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: OpocProductCodeEntity,
  service: OpocProductCodeService,
})
export class AdminOpocCodeVerifyLimitController extends BaseController {
  @Inject()
  opocProductCodeService: OpocProductCodeService;

  /**
   * 设置验证次数限制
   */
  @Post('/setVerifyLimit')
  async setVerifyLimit(@Body() body: SetVerifyLimitParams) {
    return this.ok(await this.opocProductCodeService.setVerifyLimit(body));
  }

  /**
   * 批量设置验证次数限制
   */
  @Post('/batchSetVerifyLimit')
  async batchSetVerifyLimit(@Body() body: { ids: number[], verifyLimit: number }) {
    const { ids, verifyLimit } = body;
    return this.ok(await this.opocProductCodeService.batchSetVerifyLimit(ids, verifyLimit));
  }
}
