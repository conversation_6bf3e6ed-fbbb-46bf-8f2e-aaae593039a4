import { BaseController, CoolController } from '@cool-midway/core';
import { Get, Inject, Provide, Query } from '@midwayjs/core';
import { OpocGeoAnalysisService } from '../../service/geo-analysis';
import { TimeRangeType } from '../../util/statistics';

/**
 * 地理位置分析
 */
@Provide()
@CoolController({
  api: [],
  description: '地理位置分析'
})
export class AdminGeoAnalysisController extends BaseController {
  @Inject()
  opocGeoAnalysisService: OpocGeoAnalysisService;

  /**
   * 按地理位置分组统计验证次数
   * @param timeRange 时间范围类型
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  @Get('/verification/geoStat')
  async getGeoStat(
    @Query('timeRange') timeRange: TimeRangeType = TimeRangeType.MONTH,
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string
  ) {
    return this.ok(await this.opocGeoAnalysisService.getGeoStat(timeRange, startTime, endTime));
  }

  /**
   * 检测地理位置异常
   * @param codeId 产品码ID
   * @param timeThreshold 时间阈值（毫秒）
   * @param speedThreshold 速度阈值（米/秒）
   */
  @Get('/verification/geoAnomaly')
  async detectGeoAnomaly(
    @Query('codeId') codeId: number,
    @Query('timeThreshold') timeThreshold?: number,
    @Query('speedThreshold') speedThreshold?: number
  ) {
    return this.ok(await this.opocGeoAnalysisService.detectGeoAnomaly(
      codeId,
      timeThreshold,
      speedThreshold
    ));
  }

  /**
   * 获取热力图数据
   * @param timeRange 时间范围类型
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  @Get('/verification/heatmap')
  async getHeatMapData(
    @Query('timeRange') timeRange: TimeRangeType = TimeRangeType.MONTH,
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string
  ) {
    return this.ok(await this.opocGeoAnalysisService.getHeatMapData(timeRange, startTime, endTime));
  }

  /**
   * 获取轨迹数据
   * @param codeId 产品码ID
   */
  @Get('/verification/trajectory')
  async getTrajectoryData(
    @Query('codeId') codeId: number
  ) {
    return this.ok(await this.opocGeoAnalysisService.getTrajectoryData(codeId));
  }

  /**
   * 获取地理位置分布统计
   * @param timeRange 时间范围类型
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  @Get('/verification/geoDistribution')
  async getGeoDistribution(
    @Query('timeRange') timeRange: TimeRangeType = TimeRangeType.MONTH,
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string
  ) {
    return this.ok(await this.opocGeoAnalysisService.getGeoDistribution(timeRange, startTime, endTime));
  }
}
