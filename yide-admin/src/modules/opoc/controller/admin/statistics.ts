import { BaseController } from '@cool-midway/core';
import { Body, Get, Inject, Post, Provide, Query } from '@midwayjs/core';
import { OpocStatisticsService } from '../../service/statistics';
import { TimeRangeType } from '../../util/statistics';

/**
 * 统计报表
 */
@Provide()
export class OpocStatisticsController extends BaseController {
  @Inject()
  opocStatisticsService: OpocStatisticsService;

  /**
   * 获取验证统计概览
   */
  @Get('/verification/overview')
  async getVerificationOverview() {
    return this.ok(await this.opocStatisticsService.getVerificationOverview());
  }

  /**
   * 获取验证趋势
   * @param timeRange 时间范围类型
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  @Get('/verification/trend')
  async getVerificationTrend(
    @Query('timeRange') timeRange: TimeRangeType = TimeRangeType.MONTH,
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string
  ) {
    return this.ok(await this.opocStatisticsService.getVerificationTrend(timeRange, startTime, endTime));
  }

  /**
   * 获取验证地理分布
   * @param timeRange 时间范围类型
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  @Get('/verification/geo')
  async getVerificationGeoDistribution(
    @Query('timeRange') timeRange: TimeRangeType = TimeRangeType.MONTH,
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string
  ) {
    return this.ok(await this.opocStatisticsService.getVerificationGeoDistribution(timeRange, startTime, endTime));
  }

  /**
   * 获取活动统计概览
   */
  @Get('/activity/overview')
  async getActivityOverview() {
    return this.ok(await this.opocStatisticsService.getActivityOverview());
  }

  /**
   * 获取活动参与趋势
   * @param timeRange 时间范围类型
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  @Get('/activity/trend')
  async getActivityParticipationTrend(
    @Query('timeRange') timeRange: TimeRangeType = TimeRangeType.MONTH,
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string
  ) {
    return this.ok(await this.opocStatisticsService.getActivityParticipationTrend(timeRange, startTime, endTime));
  }

  /**
   * 获取活动排行榜
   * @param limit 限制数量
   */
  @Get('/activity/ranking')
  async getActivityRanking(@Query('limit') limit: number = 10) {
    return this.ok(await this.opocStatisticsService.getActivityRanking(limit));
  }
}
