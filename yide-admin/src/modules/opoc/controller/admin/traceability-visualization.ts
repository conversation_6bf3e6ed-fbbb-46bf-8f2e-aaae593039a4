import { BaseController, CoolController } from '@cool-midway/core'
import { Provide } from '@midwayjs/core'
import { OpocTraceabilityVisualizationService } from '../../service/traceability-visualization'

/**
 * 溯源流程可视化
 */
@Provide()
@CoolController({
  api: [],
  serviceApis: [
    {
      method: 'getData',
      summary: '获取溯源流程可视化数据'
    }
  ],
  service: OpocTraceabilityVisualizationService,
  prefix: '/admin/opoc/traceability-visualization',
})
export class AdminTraceabilityVisualizationController extends BaseController {}
