import { BaseController, Cool<PERSON>ontroller, CoolUrlTag, TagTypes } from '@cool-midway/core'
import { Get, Inject, Query } from '@midwayjs/core'
import { OpocProductCodeService } from '../../service/product-code'
import { OpocProductInfoService } from '../../service/product-info'
import { VerifyCodeParams } from '../../typings/opoc'

/**
 * 产品码验证 - APP端
 */
@CoolController()
@CoolUrlTag({
  key: TagTypes.IGNORE_TOKEN,
  value: ['verify']
})
export class AppOpocCodeController extends BaseController {
  @Inject()
  opocProductInfoService: OpocProductInfoService;
  @Inject()
  opocProductCodeService: OpocProductCodeService;

  /**
   * 根据code验证唯一码存在且绑定产品，返回产品信息
   * @param code 产品唯一码
   * @param location 地理位置信息
   */
  @Get('/verify')
  async verify(@Query() params: VerifyCodeParams) {
    try {
      if (!params.code) {
        return this.fail('产品唯一码不能为空');
      }
      
      // 使用新的验证方法
      const result = await this.opocProductCodeService.verifyCode(params);
      
      return this.ok(result);
    } catch (error) {
      // 处理getTraceInfo可能抛出的错误
      return this.fail(error.message || '验证失败');
    }
  }
} 