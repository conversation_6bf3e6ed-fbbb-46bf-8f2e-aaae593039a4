import { BaseEntity } from '@cool-midway/core';
import { Column, Entity, Index } from 'typeorm';

/**
 * 活动参与记录
 */
@Entity('opoc_activity_record')
export class OpocActivityRecordEntity extends BaseEntity {
  @Index()
  @Column({ comment: '活动ID' })
  activityId: number;

  @Index()
  @Column({ comment: '用户ID', nullable: true })
  userId: number;

  @Column({ comment: '用户名称', length: 50, nullable: true })
  userName: string;

  @Column({ comment: '用户手机号', length: 20, nullable: true })
  userPhone: string;

  @Column({ comment: '产品码ID', nullable: true })
  codeId: number;

  @Column({ comment: '产品码', length: 100, nullable: true })
  code: string;

  @Column({ comment: '参与时间', type: 'datetime' })
  participationTime: Date;

  @Column({ comment: '参与结果（0:未中奖, 1:已中奖, 2:处理中, 3:已失效）', default: 0 })
  result: number;

  @Column({ comment: '奖品ID', nullable: true })
  prizeId: number;

  @Column({ comment: '奖品名称', length: 100, nullable: true })
  prizeName: string;

  @Column({ comment: '奖品类型（0:实物, 1:优惠券, 2:积分, 3:其他）', nullable: true })
  prizeType: number;

  @Column({ comment: '奖品数量', default: 0, nullable: true })
  prizeQuantity: number;

  @Column({ comment: '领取状态（0:未领取, 1:已领取, 2:已过期）', default: 0, nullable: true })
  receiveStatus: number;

  @Column({ comment: '领取时间', type: 'datetime', nullable: true })
  receiveTime: Date;

  @Column({ comment: '领取地址', length: 255, nullable: true })
  receiveAddress: string;

  @Column({ comment: '备注', length: 500, nullable: true })
  remark: string;

  @Column({ comment: 'IP地址', length: 50, nullable: true })
  ip: string;

  @Column({ comment: '设备信息', length: 255, nullable: true })
  deviceInfo: string;

  @Column({ comment: '地理位置', length: 255, nullable: true })
  location: string;

  @Column({ comment: '经度', type: 'decimal', precision: 10, scale: 6, nullable: true })
  longitude: number;

  @Column({ comment: '纬度', type: 'decimal', precision: 10, scale: 6, nullable: true })
  latitude: number;
}
