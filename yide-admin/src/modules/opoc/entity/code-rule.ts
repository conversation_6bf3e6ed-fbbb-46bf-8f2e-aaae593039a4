import { BaseEntity } from '@cool-midway/core';
import { Column, Entity } from 'typeorm';

/**
 * 编码规则
 */
@Entity('opoc_code_rule')
export class OpocCodeRuleEntity extends BaseEntity {
  @Column({ comment: '规则名称', length: 50 })
  name: string;

  @Column({ comment: '规则类型', length: 20 })
  type: string;

  @Column({ comment: '规则前缀', length: 20, nullable: true })
  prefix: string;

  @Column({ comment: '规则长度', default: 8 })
  length: number;

  @Column({ comment: '是否包含字母', default: true })
  includeLetters: boolean;

  @Column({ comment: '是否包含数字', default: true })
  includeNumbers: boolean;

  @Column({ comment: '是否包含特殊字符', default: false })
  includeSpecialChars: boolean;

  @Column({ comment: '是否默认规则', default: false })
  isDefault: boolean;

  @Column({ comment: '规则描述', length: 255, nullable: true })
  description: string;

  @Column({ comment: '状态 0-禁用 1-启用', default: 1 })
  status: number;
}
