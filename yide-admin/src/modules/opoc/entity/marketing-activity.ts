import { BaseEntity } from '@cool-midway/core';
import { Column, Entity, Index } from 'typeorm';

/**
 * 营销活动
 */
@Entity('opoc_marketing_activity')
export class OpocMarketingActivityEntity extends BaseEntity {
  @Column({ comment: '活动名称', length: 100 })
  activityName: string;

  @Column({ comment: '活动类型（0:扫码抽奖, 1:集码兑奖, 2:分享活动, 3:其他）', default: 0 })
  activityType: number;

  @Column({ comment: '活动描述', length: 500, nullable: true })
  description: string;

  @Column({ comment: '活动开始时间', type: 'datetime' })
  startTime: Date;

  @Column({ comment: '活动结束时间', type: 'datetime' })
  endTime: Date;

  @Column({ comment: '活动规则', type: 'text', nullable: true })
  rules: string;

  @Column({ comment: '活动状态（0:未开始, 1:进行中, 2:已结束, 3:已取消）', default: 0 })
  status: number;

  @Column({ comment: '参与限制（0:不限制, 1:每人一次, 2:每天一次, 3:自定义）', default: 0 })
  participationLimit: number;

  @Column({ comment: '自定义参与次数限制', default: 0, nullable: true })
  customLimit: number;

  @Column({ comment: '奖品设置', type: 'json', nullable: true })
  prizes: string;

  @Column({ comment: '活动图片', length: 255, nullable: true })
  image: string;

  @Column({ comment: '活动链接', length: 255, nullable: true })
  activityUrl: string;

  @Column({ comment: '是否需要验证产品码（0:不需要, 1:需要）', default: 0 })
  requireProductCode: number;

  @Column({ comment: '创建人ID', nullable: true })
  creatorId: number;

  @Column({ comment: '创建人姓名', length: 50, nullable: true })
  creatorName: string;
}
