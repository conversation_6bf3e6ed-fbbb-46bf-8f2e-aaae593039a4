import { BaseEntity } from '@cool-midway/core'
import { Column, Entity } from 'typeorm'

/**
 * 产品信息
 */
@Entity('opoc_product_info')
export class OpocProductInfoEntity extends BaseEntity {
  // 产品外部id
  @Column({ comment: '产品编码', length: 50 })
  productCode: string;

  @Column({ comment: '产品名称', length: 100 })
  productName: string;

  @Column({ comment: '产品SKU编码', length: 50 })
  skuCode: string;

  @Column({ comment: '产品SKU名称', length: 50, nullable: true })
  skuName: string;

  @Column({ comment: '产品图片', length: 255, nullable: true })
  image: string;

  @Column({ comment: '生产批次', length: 50, nullable: true })
  batchNo: string;

  @Column({ comment: '生产日期', type: 'datetime', nullable: true })
  produceDate: Date;

  @Column({ comment: '有效期至', type: 'datetime', nullable: true })
  expireDate: Date;

  @Column({ comment: '产品状态', type: 'int', default: 1 })
  // 1: 正常 2: 已过期 3: 已报废
  status: number;

  @Column({ comment: '产品描述', length: 500, nullable: true })
  description: string;
}
