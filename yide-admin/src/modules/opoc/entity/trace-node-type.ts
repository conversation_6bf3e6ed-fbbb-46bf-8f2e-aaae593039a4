import { Column, Entity, Index } from 'typeorm'
import { BaseEntity } from '../../base/entity/base'

/**
 * 溯源节点类型实体
 */
@Entity('opoc_trace_node_type')
export class OpocTraceNodeTypeEntity extends BaseEntity {
  @Column({ comment: '节点类型', length: 50 })
  @Index()
  nodeType: string;

  @Column({ comment: '节点名称', length: 100 })
  nodeName: string;

  @Column({ comment: '节点描述', nullable: true, length: 500 })
  description: string;

  @Column({ comment: '节点图标', nullable: true, length: 100 })
  icon: string;

  @Column({ comment: '节点颜色', nullable: true, length: 20 })
  color: string;

  @Column({ comment: '排序', default: 0 })
  sort: number;

  @Column({ comment: '状态', default: 1 })
  status: number;
}
