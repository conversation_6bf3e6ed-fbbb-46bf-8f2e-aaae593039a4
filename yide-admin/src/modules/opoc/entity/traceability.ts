import { Column, Entity, Index } from 'typeorm'
import { BaseEntity } from '../../base/entity/base'

/**
 * 溯源记录实体
 */
@Entity('opoc_traceability')
export class OpocTraceabilityEntity extends BaseEntity {
  @Index()
  @Column({ comment: '产品码', length: 100 })
  code: string;

  @Column({ comment: '产品ID', nullable: true })
  productId: number;

  @Column({ comment: '产品名称', nullable: true, length: 100 })
  productName: string;

  @Column({ comment: '节点类型', length: 50 })
  nodeType: string;

  @Column({ comment: '节点名称', length: 100 })
  nodeName: string;

  @Column({ comment: '节点时间', type: 'datetime' })
  nodeTime: Date;

  @Column({ comment: '操作人', nullable: true, length: 50 })
  operator: string;

  @Column({ comment: '位置', nullable: true, length: 100 })
  location: string;

  @Column({ comment: '经度', nullable: true, type: 'decimal', precision: 10, scale: 6 })
  longitude: number;

  @Column({ comment: '纬度', nullable: true, type: 'decimal', precision: 10, scale: 6 })
  latitude: number;

  @Column({ comment: '状态', default: 0 })
  status: number;

  @Column({ comment: '备注', nullable: true, type: 'text' })
  remark: string;

  @Column({ comment: '图片URL', nullable: true, type: 'text' })
  imageUrls: string;
}
