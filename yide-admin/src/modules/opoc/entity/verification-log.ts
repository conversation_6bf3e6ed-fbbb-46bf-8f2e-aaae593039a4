import { BaseEntity } from '@cool-midway/core';
import { Column, Entity } from 'typeorm';

/**
 * 验证日志
 */
@Entity('opoc_verification_log')
export class OpocVerificationLogEntity extends BaseEntity {
  @Column({ comment: '标识码ID' })
  codeId: number;

  @Column({ comment: '验证时间', type: 'datetime' })
  verifyTime: Date;

  @Column({ comment: '验证结果（0:真, 1:假）', default: 0 })
  verifyResult: number;

  @Column({ comment: '验证地理位置', length: 255, nullable: true })
  verifyLocation: string;

  @Column({ comment: '经度', type: 'decimal', precision: 10, scale: 6, nullable: true })
  longitude: number;

  @Column({ comment: '纬度', type: 'decimal', precision: 10, scale: 6, nullable: true })
  latitude: number;

  @Column({ comment: 'IP地址', length: 50, nullable: true })
  ip: string;

  @Column({ comment: '设备信息', length: 255, nullable: true })
  deviceInfo: string;
}
