import { BaseService, CoolCommException } from '@cool-midway/core';
import { Provide } from '@midwayjs/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { OpocMarketingActivityEntity } from '../entity/marketing-activity';
import { ActivityRuleParams } from '../typings/opoc';

/**
 * 活动规则配置服务
 */
@Provide()
export class OpocActivityRuleService extends BaseService {
  @InjectEntityModel(OpocMarketingActivityEntity)
  opocMarketingActivityEntity: Repository<OpocMarketingActivityEntity>;

  /**
   * 获取活动规则
   * @param activityId 活动ID
   */
  async getRule(activityId: number) {
    const activity = await this.opocMarketingActivityEntity.findOne({
      where: { id: activityId }
    });

    if (!activity) {
      throw new CoolCommException('活动不存在');
    }

    // 解析规则JSON
    let rules = {};
    try {
      if (activity.rules) {
        rules = JSON.parse(activity.rules);
      }
    } catch (error) {
      console.error('解析规则JSON失败', error);
    }

    return {
      activityId,
      activityName: activity.activityName,
      activityType: activity.activityType,
      rules
    };
  }

  /**
   * 保存活动规则
   * @param params 活动规则参数
   */
  async saveRule(params: ActivityRuleParams) {
    const { activityId, rules } = params;

    // 检查活动是否存在
    const activity = await this.opocMarketingActivityEntity.findOne({
      where: { id: activityId }
    });

    if (!activity) {
      throw new CoolCommException('活动不存在');
    }

    // 检查活动状态
    if (activity.status === 2) {
      throw new CoolCommException('活动已结束，无法修改规则');
    }

    // 验证规则格式
    this.validateRules(activity.activityType, rules);

    // 保存规则
    await this.opocMarketingActivityEntity.update(
      activityId,
      { rules: JSON.stringify(rules) }
    );

    return true;
  }

  /**
   * 验证规则格式
   * @param activityType 活动类型
   * @param rules 规则内容
   */
  private validateRules(activityType: number, rules: any) {
    // 根据活动类型验证规则
    switch (activityType) {
      case 0: // 扫码抽奖
        this.validateLotteryRules(rules);
        break;
      case 1: // 集码兑奖
        this.validateCollectionRules(rules);
        break;
      case 2: // 分享活动
        this.validateShareRules(rules);
        break;
      default:
        // 其他类型活动，基本验证
        if (!rules || typeof rules !== 'object') {
          throw new CoolCommException('规则格式不正确');
        }
        break;
    }
  }

  /**
   * 验证抽奖规则
   * @param rules 规则内容
   */
  private validateLotteryRules(rules: any) {
    if (!rules) {
      throw new CoolCommException('抽奖规则不能为空');
    }

    // 验证每日抽奖次数和总抽奖次数
    if (rules.dailyLimit !== undefined && (typeof rules.dailyLimit !== 'number' || rules.dailyLimit < 0)) {
      throw new CoolCommException('每日抽奖次数必须是非负数');
    }

    if (rules.totalLimit !== undefined && (typeof rules.totalLimit !== 'number' || rules.totalLimit < 0)) {
      throw new CoolCommException('总抽奖次数必须是非负数');
    }

    // 验证奖品列表
    if (!Array.isArray(rules.prizes)) {
      throw new CoolCommException('抽奖规则必须包含奖品列表');
    }

    const prizes = rules.prizes;
    if (prizes.length === 0) {
      throw new CoolCommException('奖品列表不能为空');
    }

    // 验证概率总和
    const totalProbability = prizes.reduce((sum, prize) => sum + (Number(prize.probability) || 0), 0);
    if (totalProbability > 100) {
      throw new CoolCommException('奖品概率总和不能超过100%');
    }

    // 验证每个奖品
    prizes.forEach((prize, index) => {
      if (!prize.name) {
        throw new CoolCommException(`第${index + 1}个奖品名称不能为空`);
      }
      if (typeof prize.type !== 'number' || prize.type < 0 || prize.type > 3) {
        throw new CoolCommException(`第${index + 1}个奖品类型无效`);
      }
      if (prize.probability === undefined || prize.probability === null ||
          prize.probability < 0 || prize.probability > 100) {
        throw new CoolCommException(`第${index + 1}个奖品概率必须在0-100之间`);
      }
      if (typeof prize.quantity !== 'number' || prize.quantity < 0) {
        throw new CoolCommException(`第${index + 1}个奖品数量必须是非负数`);
      }
    });
  }

  /**
   * 验证集码兑奖规则
   * @param rules 规则内容
   */
  private validateCollectionRules(rules: any) {
    if (!rules) {
      throw new CoolCommException('集码兑奖规则不能为空');
    }

    // 验证奖励列表
    if (!Array.isArray(rules.rewards)) {
      throw new CoolCommException('集码兑奖规则必须包含奖励列表');
    }

    const rewards = rules.rewards;
    if (rewards.length === 0) {
      throw new CoolCommException('奖励列表不能为空');
    }

    // 验证每个奖励
    rewards.forEach((reward, index) => {
      if (!reward.name) {
        throw new CoolCommException(`第${index + 1}个奖励名称不能为空`);
      }
      if (typeof reward.type !== 'number' || reward.type < 0 || reward.type > 3) {
        throw new CoolCommException(`第${index + 1}个奖励类型无效`);
      }
      if (!reward.requiredCodes || typeof reward.requiredCodes !== 'number' || reward.requiredCodes <= 0) {
        throw new CoolCommException(`第${index + 1}个奖励所需码数必须大于0`);
      }
      if (typeof reward.quantity !== 'number' || reward.quantity < 0) {
        throw new CoolCommException(`第${index + 1}个奖励数量必须是非负数`);
      }
    });

    // 验证所需码数是否有重复
    const requiredCodesList = rewards.map(reward => reward.requiredCodes);
    const uniqueRequiredCodes = new Set(requiredCodesList);
    if (uniqueRequiredCodes.size !== requiredCodesList.length) {
      throw new CoolCommException('存在相同所需码数的奖励，请确保每个奖励的所需码数不同');
    }
  }

  /**
   * 验证分享活动规则
   * @param rules 规则内容
   */
  private validateShareRules(rules: any) {
    if (!rules) {
      throw new CoolCommException('分享活动规则不能为空');
    }

    // 验证分享奖励
    if (!rules.shareReward) {
      throw new CoolCommException('分享奖励不能为空');
    }

    // 验证分享奖励类型和值
    if (typeof rules.shareReward.type !== 'number' || rules.shareReward.type < 0 || rules.shareReward.type > 3) {
      throw new CoolCommException('分享奖励类型无效');
    }

    if (typeof rules.shareReward.value !== 'number' || rules.shareReward.value < 0) {
      throw new CoolCommException('分享奖励值必须是非负数');
    }

    // 验证分享次数限制
    if (rules.shareLimit !== undefined && (typeof rules.shareLimit !== 'number' || rules.shareLimit < 0)) {
      throw new CoolCommException('分享次数限制不能为负数');
    }

    // 验证邀请奖励
    if (rules.inviteReward) {
      if (typeof rules.inviteReward.type !== 'number' || rules.inviteReward.type < 0 || rules.inviteReward.type > 3) {
        throw new CoolCommException('邀请奖励类型无效');
      }

      if (typeof rules.inviteReward.value !== 'number' || rules.inviteReward.value < 0) {
        throw new CoolCommException('邀请奖励值必须是非负数');
      }
    }
  }
}
