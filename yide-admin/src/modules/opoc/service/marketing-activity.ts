import { BaseService } from '@cool-midway/core';
import { Provide } from '@midwayjs/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { OpocActivityRecordEntity } from '../entity/activity-record';
import { OpocMarketingActivityEntity } from '../entity/marketing-activity';
import { ActivityStatisticsParams, ActivityStatisticsResult, CreateActivityParams } from '../typings/opoc';

/**
 * 营销活动服务
 */
@Provide()
export class OpocMarketingActivityService extends BaseService {
  @InjectEntityModel(OpocMarketingActivityEntity)
  opocMarketingActivityEntity: Repository<OpocMarketingActivityEntity>;

  @InjectEntityModel(OpocActivityRecordEntity)
  opocActivityRecordEntity: Repository<OpocActivityRecordEntity>;

  /**
   * 创建活动
   * @param params 活动参数
   */
  async create(params: CreateActivityParams, userId?: number, userName?: string) {
    // 检查活动名称是否已存在
    const existingActivity = await this.opocMarketingActivityEntity.findOneBy({
      activityName: params.activityName,
    });
    if (existingActivity) {
      throw new Error('活动名称已存在');
    }

    // 检查活动时间
    if (new Date(params.startTime) >= new Date(params.endTime)) {
      throw new Error('活动开始时间必须早于结束时间');
    }

    // 创建活动
    const activity = this.opocMarketingActivityEntity.create({
      ...params,
      creatorId: userId,
      creatorName: userName,
      status: this.getActivityStatus(params.startTime, params.endTime),
    });

    return await this.opocMarketingActivityEntity.save(activity);
  }

  /**
   * 更新活动
   * @param param 更新参数
   */
  async update(param: any): Promise<void> {
    const { id, ...params } = param;
    const activity = await this.opocMarketingActivityEntity.findOneBy({ id });
    if (!activity) {
      throw new Error('活动不存在');
    }

    // 如果更新了时间，检查时间是否合法
    if (params.startTime && params.endTime) {
      if (new Date(params.startTime) >= new Date(params.endTime)) {
        throw new Error('活动开始时间必须早于结束时间');
      }
      // 更新活动状态
      params['status'] = this.getActivityStatus(params.startTime, params.endTime);
    } else if (params.startTime && !params.endTime) {
      if (new Date(params.startTime) >= new Date(activity.endTime)) {
        throw new Error('活动开始时间必须早于结束时间');
      }
      // 更新活动状态
      params['status'] = this.getActivityStatus(params.startTime, activity.endTime);
    } else if (!params.startTime && params.endTime) {
      if (new Date(activity.startTime) >= new Date(params.endTime)) {
        throw new Error('活动开始时间必须早于结束时间');
      }
      // 更新活动状态
      params['status'] = this.getActivityStatus(activity.startTime, params.endTime);
    }

    await this.opocMarketingActivityEntity.update(id, params);
  }

  /**
   * 自定义更新活动
   * @param id 活动ID
   * @param params 活动参数
   */
  async updateActivity(id: number, params: Partial<CreateActivityParams>) {
    const activity = await this.opocMarketingActivityEntity.findOneBy({ id });
    if (!activity) {
      throw new Error('活动不存在');
    }

    // 如果更新了时间，检查时间是否合法
    if (params.startTime && params.endTime) {
      if (new Date(params.startTime) >= new Date(params.endTime)) {
        throw new Error('活动开始时间必须早于结束时间');
      }
      // 更新活动状态
      params['status'] = this.getActivityStatus(params.startTime, params.endTime);
    } else if (params.startTime && !params.endTime) {
      if (new Date(params.startTime) >= new Date(activity.endTime)) {
        throw new Error('活动开始时间必须早于结束时间');
      }
      // 更新活动状态
      params['status'] = this.getActivityStatus(params.startTime, activity.endTime);
    } else if (!params.startTime && params.endTime) {
      if (new Date(activity.startTime) >= new Date(params.endTime)) {
        throw new Error('活动开始时间必须早于结束时间');
      }
      // 更新活动状态
      params['status'] = this.getActivityStatus(activity.startTime, params.endTime);
    }

    return await this.opocMarketingActivityEntity.update(id, params);
  }

  /**
   * 获取活动状态
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  private getActivityStatus(startTime: Date, endTime: Date): number {
    const now = new Date();
    if (now < new Date(startTime)) {
      return 0; // 未开始
    } else if (now > new Date(endTime)) {
      return 2; // 已结束
    } else {
      return 1; // 进行中
    }
  }

  /**
   * 获取活动统计数据
   * @param params 统计参数
   */
  async getStatistics(params: ActivityStatisticsParams): Promise<ActivityStatisticsResult> {
    const { activityId, startDate, endDate, groupBy = 'day' } = params;

    // 检查活动是否存在
    const activity = await this.opocMarketingActivityEntity.findOneBy({ id: activityId });
    if (!activity) {
      throw new Error('活动不存在');
    }

    // 构建查询条件
    const queryBuilder = this.opocActivityRecordEntity
      .createQueryBuilder('record')
      .where('record.activityId = :activityId', { activityId });

    // 添加时间范围条件
    if (startDate) {
      queryBuilder.andWhere('record.participationTime >= :startDate', {
        startDate: new Date(startDate),
      });
    }
    if (endDate) {
      queryBuilder.andWhere('record.participationTime <= :endDate', {
        endDate: new Date(endDate),
      });
    }

    // 获取总参与人次
    const totalParticipants = await queryBuilder.getCount();

    // 获取独立参与人数（按用户ID去重）
    const uniqueParticipantsQuery = queryBuilder.clone();
    const uniqueParticipants = await uniqueParticipantsQuery
      .select('COUNT(DISTINCT record.userId)')
      .getRawOne()
      .then(result => parseInt(result?.count || '0', 10));

    // 获取总发放奖品数
    const totalPrizesQuery = queryBuilder.clone();
    const totalPrizes = await totalPrizesQuery
      .andWhere('record.result = :result', { result: 1 }) // 已中奖
      .getCount();

    // 获取奖品分布
    const prizesDistributionQuery = queryBuilder.clone();
    const prizesDistribution = await prizesDistributionQuery
      .select('record.prizeName, COUNT(record.id) as count')
      .andWhere('record.result = :result', { result: 1 }) // 已中奖
      .groupBy('record.prizeName')
      .getRawMany()
      .then(results => {
        const distribution: { [key: string]: number } = {};
        results.forEach(item => {
          distribution[item.prizeName || '未知奖品'] = parseInt(item.count, 10);
        });
        return distribution;
      });

    // 获取参与趋势
    let dateFormat: string;
    switch (groupBy) {
      case 'week':
        dateFormat = '%Y-%u'; // ISO周格式 YYYY-WW
        break;
      case 'month':
        dateFormat = '%Y-%m'; // 月格式 YYYY-MM
        break;
      case 'day':
      default:
        dateFormat = '%Y-%m-%d'; // 日格式 YYYY-MM-DD
        break;
    }

    const participationTrendQuery = queryBuilder.clone();
    const participationTrend = await participationTrendQuery
      .select(`DATE_FORMAT(record.participationTime, '${dateFormat}') as date, COUNT(record.id) as count`)
      .groupBy('date')
      .orderBy('date', 'ASC')
      .getRawMany()
      .then(results => {
        return results.map(item => ({
          date: item.date,
          count: parseInt(item.count, 10),
        }));
      });

    return {
      totalParticipants,
      uniqueParticipants,
      totalPrizes,
      prizesDistribution,
      participationTrend,
    };
  }
}
