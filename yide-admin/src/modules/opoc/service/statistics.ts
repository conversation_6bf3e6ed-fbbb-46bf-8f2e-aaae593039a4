import { BaseService } from '@cool-midway/core';
import { Inject, Provide } from '@midwayjs/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { OpocVerificationLogEntity } from '../entity/verification-log';
import { OpocProductCodeEntity } from '../entity/product-code';
import { OpocProductInfoEntity } from '../entity/product-info';
import { OpocActivityRecordEntity } from '../entity/activity-record';
import { OpocMarketingActivityEntity } from '../entity/marketing-activity';
import * as moment from 'moment';
import { fillTimeSeriesData, getTimeRange, TimeRangeType } from '../util/statistics';

/**
 * 统计服务
 */
@Provide()
export class OpocStatisticsService extends BaseService {
  @InjectEntityModel(OpocVerificationLogEntity)
  verificationLogEntity: Repository<OpocVerificationLogEntity>;

  @InjectEntityModel(OpocProductCodeEntity)
  productCodeEntity: Repository<OpocProductCodeEntity>;

  @InjectEntityModel(OpocProductInfoEntity)
  productInfoEntity: Repository<OpocProductInfoEntity>;

  @InjectEntityModel(OpocActivityRecordEntity)
  activityRecordEntity: Repository<OpocActivityRecordEntity>;

  @InjectEntityModel(OpocMarketingActivityEntity)
  marketingActivityEntity: Repository<OpocMarketingActivityEntity>;

  /**
   * 获取验证统计概览
   */
  async getVerificationOverview() {
    // 获取总验证次数
    const totalCount = await this.verificationLogEntity.count();
    
    // 获取今日验证次数
    const today = moment().format('YYYY-MM-DD');
    const todayCount = await this.verificationLogEntity
      .createQueryBuilder()
      .where('DATE(verifyTime) = :today', { today })
      .getCount();
    
    // 获取真假比例
    const verifyResultStats = await this.verificationLogEntity
      .createQueryBuilder()
      .select('verifyResult')
      .addSelect('COUNT(*) as count')
      .groupBy('verifyResult')
      .getRawMany();
    
    // 计算真假比例
    const validCount = verifyResultStats.find(item => item.verifyResult === 0)?.count || 0;
    const invalidCount = verifyResultStats.find(item => item.verifyResult === 1)?.count || 0;
    
    // 获取最近一周的验证趋势
    const lastWeekTrend = await this.getVerificationTrend(TimeRangeType.WEEK);
    
    return {
      totalCount,
      todayCount,
      validCount,
      invalidCount,
      validRate: totalCount > 0 ? Number((validCount / totalCount * 100).toFixed(2)) : 0,
      lastWeekTrend
    };
  }

  /**
   * 获取验证趋势
   * @param timeRange 时间范围类型
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  async getVerificationTrend(timeRangeType: TimeRangeType, startTime?: string, endTime?: string) {
    // 获取时间范围
    const { startTime: start, endTime: end } = getTimeRange(timeRangeType, startTime, endTime);
    
    // 格式化时间
    const startDate = moment(start).format('YYYY-MM-DD');
    const endDate = moment(end).format('YYYY-MM-DD');
    
    // 查询验证趋势数据
    const trendData = await this.nativeQuery(`
      SELECT 
        DATE(verifyTime) as date,
        COUNT(*) as total,
        SUM(CASE WHEN verifyResult = 0 THEN 1 ELSE 0 END) as valid,
        SUM(CASE WHEN verifyResult = 1 THEN 1 ELSE 0 END) as invalid
      FROM opoc_verification_log
      WHERE DATE(verifyTime) BETWEEN ? AND ?
      GROUP BY DATE(verifyTime)
      ORDER BY date
    `, [startDate, endDate]);
    
    // 填充时间序列数据
    return fillTimeSeriesData(
      trendData,
      'date',
      'total',
      start,
      end,
      'day',
      'YYYY-MM-DD'
    );
  }

  /**
   * 获取验证地理分布
   * @param timeRange 时间范围类型
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  async getVerificationGeoDistribution(timeRangeType: TimeRangeType, startTime?: string, endTime?: string) {
    // 获取时间范围
    const { startTime: start, endTime: end } = getTimeRange(timeRangeType, startTime, endTime);
    
    // 查询地理分布数据
    const geoData = await this.verificationLogEntity
      .createQueryBuilder()
      .select('verifyLocation')
      .addSelect('COUNT(*) as count')
      .where('verifyTime BETWEEN :start AND :end', { start, end })
      .andWhere('verifyLocation IS NOT NULL')
      .groupBy('verifyLocation')
      .orderBy('count', 'DESC')
      .limit(20)
      .getRawMany();
    
    return geoData.map(item => ({
      location: item.verifyLocation || '未知',
      count: parseInt(item.count) || 0
    }));
  }

  /**
   * 获取活动统计概览
   */
  async getActivityOverview() {
    // 获取活动总数
    const totalActivities = await this.marketingActivityEntity.count();
    
    // 获取进行中的活动数
    const ongoingActivities = await this.marketingActivityEntity.count({
      where: { status: 1 }
    });
    
    // 获取参与总人次
    const totalParticipations = await this.activityRecordEntity.count();
    
    // 获取中奖总人次
    const totalWinners = await this.activityRecordEntity.count({
      where: { result: 1 }
    });
    
    // 获取最近一周的参与趋势
    const lastWeekTrend = await this.getActivityParticipationTrend(TimeRangeType.WEEK);
    
    return {
      totalActivities,
      ongoingActivities,
      totalParticipations,
      totalWinners,
      winRate: totalParticipations > 0 ? Number((totalWinners / totalParticipations * 100).toFixed(2)) : 0,
      lastWeekTrend
    };
  }

  /**
   * 获取活动参与趋势
   * @param timeRange 时间范围类型
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  async getActivityParticipationTrend(timeRangeType: TimeRangeType, startTime?: string, endTime?: string) {
    // 获取时间范围
    const { startTime: start, endTime: end } = getTimeRange(timeRangeType, startTime, endTime);
    
    // 格式化时间
    const startDate = moment(start).format('YYYY-MM-DD');
    const endDate = moment(end).format('YYYY-MM-DD');
    
    // 查询参与趋势数据
    const trendData = await this.nativeQuery(`
      SELECT 
        DATE(participationTime) as date,
        COUNT(*) as total,
        SUM(CASE WHEN result = 1 THEN 1 ELSE 0 END) as winners
      FROM opoc_activity_record
      WHERE DATE(participationTime) BETWEEN ? AND ?
      GROUP BY DATE(participationTime)
      ORDER BY date
    `, [startDate, endDate]);
    
    // 填充时间序列数据
    return fillTimeSeriesData(
      trendData,
      'date',
      'total',
      start,
      end,
      'day',
      'YYYY-MM-DD'
    );
  }

  /**
   * 获取活动排行榜
   * @param limit 限制数量
   */
  async getActivityRanking(limit: number = 10) {
    return this.nativeQuery(`
      SELECT 
        a.id,
        a.activityName,
        COUNT(r.id) as participationCount,
        SUM(CASE WHEN r.result = 1 THEN 1 ELSE 0 END) as winnerCount
      FROM opoc_marketing_activity a
      LEFT JOIN opoc_activity_record r ON a.id = r.activityId
      GROUP BY a.id, a.activityName
      ORDER BY participationCount DESC
      LIMIT ?
    `, [limit]);
  }
}
