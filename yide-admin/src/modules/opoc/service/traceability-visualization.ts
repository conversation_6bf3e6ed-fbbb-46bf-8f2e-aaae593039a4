import { Provide, Inject } from '@midwayjs/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { BaseService, CoolCommException } from '@cool-midway/core';
import { isEmpty } from 'lodash';
import { OpocTraceabilityEntity } from '../entity/traceability';
import { OpocProductCodeEntity } from '../entity/product-code';
import { OpocProductInfoEntity } from '../entity/product-info';
import { OpocTraceNodeTypeEntity } from '../entity/trace-node-type';

/**
 * 溯源流程可视化服务
 */
@Provide()
export class OpocTraceabilityVisualizationService extends BaseService {
  @InjectEntityModel(OpocTraceabilityEntity)
  traceabilityEntity: Repository<OpocTraceabilityEntity>;

  @InjectEntityModel(OpocProductInfoEntity)
  productInfoEntity: Repository<OpocProductInfoEntity>;

  @InjectEntityModel(OpocProductCodeEntity)
  codeEntity: Repository<OpocProductCodeEntity>;

  @InjectEntityModel(OpocTraceNodeTypeEntity)
  traceNodeTypeEntity: Repository<OpocTraceNodeTypeEntity>;

  /**
   * 获取溯源流程可视化数据
   * @param param 查询参数
   */
  async getData(param) {
    if (!param.code) {
      throw new CoolCommException('产品码不能为空');
    }

    // 查询产品码对应的溯源记录
    const traces = await this.traceabilityEntity.find({
      where: {
        code: param.code,
      },
      order: {
        nodeTime: 'ASC',
      },
    });

    if (isEmpty(traces)) {
      return {
        nodes: [],
        edges: [],
      };
    }

    // 获取所有节点类型
    const nodeTypes = await this.traceNodeTypeEntity.find();
    const nodeTypeMap = {};
    nodeTypes.forEach(item => {
      nodeTypeMap[item.nodeType] = item;
    });

    // 构建节点和边
    const nodes = [];
    const edges = [];

    traces.forEach((trace, index) => {
      // 构建节点
      const nodeType = nodeTypeMap[trace.nodeType] || {};
      const node = {
        id: `node-${trace.id}`,
        label: trace.nodeName,
        type: trace.nodeType,
        time: trace.nodeTime,
        status: trace.status,
        data: trace,
        style: {
          fill: nodeType.color || '#1890FF',
          stroke: trace.status === 0 ? '#1890FF' : '#FF4D4F',
        },
      };
      nodes.push(node);

      // 构建边（除了第一个节点外，每个节点都与前一个节点相连）
      if (index > 0) {
        const edge = {
          id: `edge-${index}`,
          source: `node-${traces[index - 1].id}`,
          target: `node-${trace.id}`,
          style: {
            stroke: trace.status === 0 ? '#1890FF' : '#FF4D4F',
          },
        };
        edges.push(edge);
      }
    });

    return {
      nodes,
      edges,
      product: await this.getProductInfo(param.code),
    };
  }

  /**
   * 获取产品信息
   * @param code 产品码
   */
  async getProductInfo(code) {
    const productCode = await this.codeEntity.findOne({
      where: {
        code,
      },
    });

    if (!productCode || !productCode.productId) {
      return null;
    }

    return await this.productInfoEntity.findOne({
      where: {
        id: productCode.productId,
      },
    });
  }
}
