import { BaseService, CoolCommException } from '@cool-midway/core';
import { Inject, Provide } from '@midwayjs/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { isEmpty } from 'lodash';
import { Between, In, Like, Repository } from 'typeorm';
import { OpocProductCodeEntity } from '../entity/product-code';
import { OpocProductInfoEntity } from '../entity/product-info';
import { OpocTraceNodeTypeEntity } from '../entity/trace-node-type';
import { OpocTraceabilityEntity } from '../entity/traceability';

/**
 * 溯源异常预警服务
 */
@Provide()
export class OpocTraceabilityWarningService extends BaseService {
  @InjectEntityModel(OpocTraceabilityEntity)
  traceabilityEntity: Repository<OpocTraceabilityEntity>;

  @InjectEntityModel(OpocProductInfoEntity)
  productInfoEntity: Repository<OpocProductInfoEntity>;

  @InjectEntityModel(OpocProductCodeEntity)
  codeEntity: Repository<OpocProductCodeEntity>;

  @InjectEntityModel(OpocTraceNodeTypeEntity)
  nodeTypeEntity: Repository<OpocTraceNodeTypeEntity>;

  /**
   * 获取异常溯源记录
   * @param param 查询参数
   */
  async getWarnings(param) {
    const { 
      startTime, 
      endTime, 
      keyWord, 
      nodeType, 
      status, 
      page = 1, 
      size = 20 
    } = param;

    // 构建查询条件
    const where: any = {};
    
    // 状态条件：默认查询所有异常状态(status != 0)
    if (status !== undefined) {
      where.status = status;
    } else {
      where.status = In([1, 2, 3]); // 查询所有异常状态
    }

    // 时间范围条件
    if (startTime && endTime) {
      where.nodeTime = Between(startTime, endTime);
    } else if (startTime) {
      where.nodeTime = Between(startTime, new Date());
    }

    // 节点类型条件
    if (nodeType) {
      where.nodeType = nodeType;
    }

    // 关键词搜索
    if (keyWord) {
      where.code = Like(`%${keyWord}%`);
      // 或者产品名称包含关键词
      // 由于TypeORM的限制，复杂的OR条件需要使用QueryBuilder
    }

    // 使用QueryBuilder处理复杂查询
    const queryBuilder = this.traceabilityEntity.createQueryBuilder('traceability');
    
    // 添加基本条件
    if (where.status) {
      if (where.status instanceof In) {
        queryBuilder.where('traceability.status IN (:...statuses)', { 
          statuses: where.status.value 
        });
      } else {
        queryBuilder.where('traceability.status = :status', { 
          status: where.status 
        });
      }
    }
    
    // 添加时间条件
    if (where.nodeTime) {
      if (where.nodeTime instanceof Between) {
        queryBuilder.andWhere('traceability.nodeTime BETWEEN :startTime AND :endTime', {
          startTime: where.nodeTime.value[0],
          endTime: where.nodeTime.value[1]
        });
      }
    }
    
    // 添加节点类型条件
    if (where.nodeType) {
      queryBuilder.andWhere('traceability.nodeType = :nodeType', {
        nodeType: where.nodeType
      });
    }
    
    // 添加关键词搜索条件
    if (keyWord) {
      queryBuilder.andWhere(
        '(traceability.code LIKE :keyWord OR traceability.productName LIKE :keyWord OR traceability.nodeName LIKE :keyWord)',
        { keyWord: `%${keyWord}%` }
      );
    }
    
    // 添加排序
    queryBuilder.orderBy('traceability.nodeTime', 'DESC');
    
    // 分页
    const total = await queryBuilder.getCount();
    queryBuilder.skip((page - 1) * size).take(size);
    
    const list = await queryBuilder.getMany();
    
    return {
      list,
      pagination: {
        page,
        size,
        total
      }
    };
  }

  /**
   * 获取异常统计数据
   */
  async getWarningStats() {
    // 获取各异常等级的数量
    const statusCounts = await this.traceabilityEntity
      .createQueryBuilder('traceability')
      .select('traceability.status', 'status')
      .addSelect('COUNT(traceability.id)', 'count')
      .where('traceability.status != 0')
      .groupBy('traceability.status')
      .getRawMany();
    
    // 获取各节点类型的异常数量
    const nodeTypeCounts = await this.traceabilityEntity
      .createQueryBuilder('traceability')
      .select('traceability.nodeType', 'nodeType')
      .addSelect('COUNT(traceability.id)', 'count')
      .where('traceability.status != 0')
      .groupBy('traceability.nodeType')
      .getRawMany();
    
    // 获取最近30天的异常趋势
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const dailyTrend = await this.traceabilityEntity
      .createQueryBuilder('traceability')
      .select('DATE(traceability.nodeTime)', 'date')
      .addSelect('COUNT(traceability.id)', 'count')
      .where('traceability.status != 0')
      .andWhere('traceability.nodeTime >= :thirtyDaysAgo', { thirtyDaysAgo })
      .groupBy('DATE(traceability.nodeTime)')
      .orderBy('date', 'ASC')
      .getRawMany();
    
    return {
      statusCounts,
      nodeTypeCounts,
      dailyTrend
    };
  }

  /**
   * 处理异常记录
   * @param param 处理参数
   */
  async handleWarning(param) {
    const { id, handleResult, handleNote } = param;
    
    if (!id) {
      throw new CoolCommException('异常记录ID不能为空');
    }
    
    const warning = await this.traceabilityEntity.findOne({
      where: { id: Number(id) }
    });
    
    if (!warning) {
      throw new CoolCommException('异常记录不存在');
    }
    
    // 更新处理结果
    await this.traceabilityEntity.update(id, {
      status: 0, // 标记为已处理
      remark: handleNote ? `${warning.remark || ''}\n处理结果: ${handleResult}\n处理说明: ${handleNote}` : warning.remark
    });
    
    return true;
  }
}
