import { BaseService } from '@cool-midway/core'
import { Config, Provide } from '@midwayjs/core'
import { InjectEntityModel } from '@midwayjs/typeorm'
import { Repository } from 'typeorm'
import { OpocProductCodeEntity } from '../entity/product-code'
import { OpocVerificationLogEntity } from '../entity/verification-log'

/**
 * 验证服务
 */
@Provide()
export class OpocVerificationService extends BaseService {
  @InjectEntityModel(OpocVerificationLogEntity)
  opocVerificationLogEntity: Repository<OpocVerificationLogEntity>;

  @InjectEntityModel(OpocProductCodeEntity)
  opocProductCodeEntity: Repository<OpocProductCodeEntity>;

  @Config('module.opoc.config.code')
  codeConfig: {
    length: number;
    prefix: string;
    verifyLimit: number;
  };

  /**
   * 验证产品真伪
   * @param code 标识码
   * @param location 地理位置
   */
  async verify(code: string, location?: string) {
    const codeInfo = await this.opocProductCodeEntity.findOneBy({ code });

    // 验证结果 0:真 1:假
    let result = 1;
    let message = '产品验证失败，可能是假冒产品';

    if (codeInfo && codeInfo.status > 0) {
      // 检查验证次数限制
      if (codeInfo.verifyLimit > 0 && codeInfo.verifyCount >= codeInfo.verifyLimit) {
        result = 1;
        message = '产品码验证次数已达上限';
      } else {
        result = 0;
        message = '产品验证成功';

        // 更新验证次数和时间
        await this.opocProductCodeEntity.update(codeInfo.id, {
          verifyCount: () => 'IFNULL(verifyCount, 0) + 1',
          lastVerifyTime: new Date()
        });
      }
    }

    // 记录验证日志
    await this.opocVerificationLogEntity.save({
      codeId: codeInfo?.id || 0,
      verifyTime: new Date(),
      verifyResult: result,
      verifyLocation: location,
    });

    return {
      isValid: result === 0,
      message,
    };
  }

  /**
   * 获取验证统计
   */
  async getStats() {
    // 这里可以实现验证统计逻辑，如统计每天的验证次数、真假比例等
    return this.nativeQuery(`
      SELECT
        DATE_FORMAT(verify_time, '%Y-%m-%d') as date,
        COUNT(*) as total,
        SUM(CASE WHEN verify_result = 0 THEN 1 ELSE 0 END) as valid,
        SUM(CASE WHEN verify_result = 1 THEN 1 ELSE 0 END) as invalid
      FROM opoc_verification_log
      GROUP BY DATE_FORMAT(verify_time, '%Y-%m-%d')
      ORDER BY date DESC
      LIMIT 30
    `);
  }
}
