export type GenerateCodeParams = {
  length: number;
  type: 'random' | 'number' | 'letter' | 'mixed';
  quantity?: number;
};

export type VerifyCodeParams = {
  code: string;
  location?: {address: string, longitude: number, latitude: number};
};

export type SetVerifyLimitParams = {
  id: number;
  verifyLimit: number;
};

export type CodeFormatParams = {
  type: 'random' | 'number' | 'letter' | 'mixed' | 'custom';
  length: number;
  includeLetters?: boolean;
  includeNumbers?: boolean;
  includeSpecialChars?: boolean;
  prefix?: string;
};

export type ExportParams = {
  ids?: number[];
  fields?: string[];
  fileName?: string;
  fileType?: 'xlsx' | 'csv' | 'txt';
  includeHeader?: boolean;
  status?: number;
  productId?: number;
  dateRange?: [string, string];
};

export type BatchBindParams = {
  codeIds: number[];
  productId: number;
};

export type BatchImportResult = {
  success: boolean;
  message: string;
  data?: any[];
  errors?: { row: number; field: string; message: string }[];
  duplicates?: string[];
};

export type CreateActivityParams = {
  activityName: string;
  activityType: number;
  description?: string;
  startTime: Date;
  endTime: Date;
  rules?: string;
  participationLimit: number;
  customLimit?: number;
  prizes?: any;
  image?: string;
  activityUrl?: string;
  requireProductCode: number;
};

export type ActivityStatisticsParams = {
  activityId: number;
  startDate?: string;
  endDate?: string;
  groupBy?: 'day' | 'week' | 'month';
};

export type ActivityStatisticsResult = {
  totalParticipants: number;
  uniqueParticipants: number;
  totalPrizes: number;
  prizesDistribution: { [key: string]: number };
  participationTrend: { date: string; count: number }[];
};

export type GeoStatParams = {
  timeRange?: 'day' | 'week' | 'month' | 'year' | 'custom';
  startTime?: string;
  endTime?: string;
};

export type GeoAnomalyParams = {
  codeId: number;
  timeThreshold?: number;
  speedThreshold?: number;
};

export type GeoStatResult = {
  location: string;
  count: number;
  longitude: number;
  latitude: number;
};

export type GeoAnomalyResult = {
  hasAnomaly: boolean;
  anomalies: Array<{
    id: number;
    verifyTime: Date;
    verifyLocation: string;
    prevTime: Date;
    prevLocation: string;
    distance: number;
    timeDiff: number;
    speed: number;
  }>;
};

export type HeatMapPoint = {
  lng: number;
  lat: number;
  count: number;
};

export type TrajectoryPoint = {
  id: number;
  verifyTime: Date;
  verifyLocation: string;
  longitude: number;
  latitude: number;
};

export type GeoDistributionResult = {
  name: string;
  value: number;
};

export type ActivityRuleParams = {
  activityId: number;
  rules: any;
};

export type LotteryPrize = {
  id?: number;
  name: string;
  type: number; // 0:实物, 1:优惠券, 2:积分, 3:其他
  probability: number; // 中奖概率，0-100
  quantity: number; // 奖品数量
  image?: string; // 奖品图片
  description?: string; // 奖品描述
};

export type CollectionReward = {
  id?: number;
  name: string;
  type: number; // 0:实物, 1:优惠券, 2:积分, 3:其他
  requiredCodes: number; // 所需码数
  quantity: number; // 奖励数量
  image?: string; // 奖励图片
  description?: string; // 奖励描述
};

export type ShareRule = {
  shareReward: {
    type: number; // 0:实物, 1:优惠券, 2:积分, 3:其他
    value: number; // 奖励值
    description?: string; // 奖励描述
  };
  shareLimit?: number; // 分享次数限制，0表示不限制
  inviteReward?: {
    type: number; // 0:实物, 1:优惠券, 2:积分, 3:其他
    value: number; // 奖励值
    description?: string; // 奖励描述
  };
};
