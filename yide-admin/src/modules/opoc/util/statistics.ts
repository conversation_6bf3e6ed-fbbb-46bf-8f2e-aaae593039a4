/**
 * 数据统计工具类
 * 提供通用的数据统计方法，如时间段统计、趋势分析等
 */

import * as moment from 'moment';

/**
 * 时间范围类型
 */
export enum TimeRangeType {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  YEAR = 'year',
  CUSTOM = 'custom'
}

/**
 * 时间范围配置
 */
export interface TimeRange {
  type: TimeRangeType;
  startTime?: Date | string;
  endTime?: Date | string;
  format?: string;
}

/**
 * 统计数据项
 */
export interface StatItem {
  label: string;
  value: number;
  [key: string]: any;
}

/**
 * 时间序列数据项
 */
export interface TimeSeriesItem {
  time: string;
  value: number;
  [key: string]: any;
}

/**
 * 获取时间范围
 * @param type 时间范围类型
 * @param customStart 自定义开始时间
 * @param customEnd 自定义结束时间
 * @returns 开始时间和结束时间
 */
export function getTimeRange(type: TimeRangeType, customStart?: Date | string, customEnd?: Date | string): { startTime: Date; endTime: Date } {
  const now = new Date();
  let startTime: Date;
  let endTime: Date = now;

  switch (type) {
    case TimeRangeType.DAY:
      // 今天
      startTime = moment().startOf('day').toDate();
      break;
    case TimeRangeType.WEEK:
      // 本周
      startTime = moment().startOf('week').toDate();
      break;
    case TimeRangeType.MONTH:
      // 本月
      startTime = moment().startOf('month').toDate();
      break;
    case TimeRangeType.YEAR:
      // 本年
      startTime = moment().startOf('year').toDate();
      break;
    case TimeRangeType.CUSTOM:
      // 自定义时间范围
      if (customStart && customEnd) {
        startTime = typeof customStart === 'string' ? new Date(customStart) : customStart;
        endTime = typeof customEnd === 'string' ? new Date(customEnd) : customEnd;
      } else {
        // 默认最近30天
        startTime = moment().subtract(30, 'days').startOf('day').toDate();
      }
      break;
    default:
      // 默认最近7天
      startTime = moment().subtract(7, 'days').startOf('day').toDate();
  }

  return { startTime, endTime };
}

/**
 * 生成时间序列
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @param interval 时间间隔 (day, week, month, year)
 * @param format 时间格式
 * @returns 时间序列数组
 */
export function generateTimeSeries(
  startTime: Date | string,
  endTime: Date | string,
  interval: 'day' | 'week' | 'month' | 'year' = 'day',
  format: string = 'YYYY-MM-DD'
): string[] {
  const start = moment(startTime);
  const end = moment(endTime);
  const result: string[] = [];

  let current = start.clone();
  while (current.isSameOrBefore(end)) {
    result.push(current.format(format));
    current.add(1, interval);
  }

  return result;
}

/**
 * 填充时间序列数据
 * @param data 原始数据
 * @param timeField 时间字段名
 * @param valueField 值字段名
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @param interval 时间间隔
 * @param format 时间格式
 * @returns 填充后的时间序列数据
 */
export function fillTimeSeriesData(
  data: any[],
  timeField: string,
  valueField: string,
  startTime: Date | string,
  endTime: Date | string,
  interval: 'day' | 'week' | 'month' | 'year' = 'day',
  format: string = 'YYYY-MM-DD'
): TimeSeriesItem[] {
  // 生成时间序列
  const timeSeries = generateTimeSeries(startTime, endTime, interval, format);
  
  // 将原始数据转换为Map，以时间为键
  const dataMap = new Map<string, any>();
  data.forEach(item => {
    const time = moment(item[timeField]).format(format);
    dataMap.set(time, item);
  });

  // 填充数据
  return timeSeries.map(time => {
    const item = dataMap.get(time);
    return {
      time,
      value: item ? item[valueField] : 0,
      ...(item || {})
    };
  });
}

/**
 * 计算同比增长率
 * @param current 当前值
 * @param previous 上一期值
 * @returns 增长率
 */
export function calculateGrowthRate(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0;
  return Number(((current - previous) / previous * 100).toFixed(2));
}

/**
 * 计算数据汇总
 * @param data 数据数组
 * @param valueField 值字段名
 * @returns 汇总值
 */
export function calculateSum(data: any[], valueField: string): number {
  return data.reduce((sum, item) => sum + (Number(item[valueField]) || 0), 0);
}

/**
 * 计算数据平均值
 * @param data 数据数组
 * @param valueField 值字段名
 * @returns 平均值
 */
export function calculateAverage(data: any[], valueField: string): number {
  if (data.length === 0) return 0;
  const sum = calculateSum(data, valueField);
  return Number((sum / data.length).toFixed(2));
}

/**
 * 分组统计
 * @param data 数据数组
 * @param groupField 分组字段
 * @param valueField 值字段
 * @returns 分组统计结果
 */
export function groupByAndCount(data: any[], groupField: string, valueField?: string): StatItem[] {
  const groups = new Map<string, number>();
  
  data.forEach(item => {
    const key = String(item[groupField] || '其他');
    const value = valueField ? (Number(item[valueField]) || 0) : 1;
    
    if (groups.has(key)) {
      groups.set(key, groups.get(key)! + value);
    } else {
      groups.set(key, value);
    }
  });
  
  return Array.from(groups.entries()).map(([label, value]) => ({ label, value }));
}
