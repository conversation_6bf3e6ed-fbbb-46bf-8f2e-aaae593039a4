import { createApp, close, createHttpRequest } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';
import * as assert from 'assert';
import { OpocProductCodeService } from '../../../src/modules/opoc/service/product-code';
import { OpocProductInfoService } from '../../../src/modules/opoc/service/product-info';

describe('test/controller/opoc/code.test.ts', () => {
  let app: Framework.Application;
  let productCodeService: OpocProductCodeService;
  let productInfoService: OpocProductInfoService;
  let testCodeIds: number[] = [];
  let testProductId: number;

  beforeAll(async () => {
    // 创建应用
    app = await createApp();
    // 获取服务
    productCodeService = await app.getApplicationContext().getAsync(OpocProductCodeService);
    productInfoService = await app.getApplicationContext().getAsync(OpocProductInfoService);

    // 创建测试产品
    const testProduct = await productInfoService.opocProductInfoEntity.save({
      productName: 'Test Product',
      productCode: 'TP001',
      skuCode: 'SKU001',
      skuName: 'Test SKU',
      status: 1,
    });
    testProductId = testProduct.id;

    // 创建测试产品码
    const codes = [];
    for (let i = 0; i < 10; i++) {
      codes.push({
        code: `TEST-CODE-${i}`,
        status: i % 3, // 0: 未使用, 1: 已绑定, 2: 已激活
        productId: i % 2 === 0 ? testProductId : null,
        verifyCount: i,
        verifyLimit: 10,
      });
    }
    const savedCodes = await productCodeService.opocProductCodeEntity.save(codes);
    testCodeIds = savedCodes.map(code => code.id);
  });

  afterAll(async () => {
    // 清理测试数据
    if (testCodeIds.length > 0) {
      await productCodeService.opocProductCodeEntity.delete(testCodeIds);
    }
    if (testProductId) {
      await productInfoService.opocProductInfoEntity.delete(testProductId);
    }
    await close(app);
  });

  // 测试批量导出API
  it('should export codes', async () => {
    // 测试导出所有码
    const result1 = await createHttpRequest(app)
      .post('/admin/opoc/code/export')
      .send({})
      .expect(200);

    assert(result1.body.code === 1000);
    assert(Array.isArray(result1.body.data));
    assert(result1.body.data.length >= testCodeIds.length);

    // 测试导出指定ID的码
    const result2 = await createHttpRequest(app)
      .post('/admin/opoc/code/export')
      .send({
        ids: testCodeIds.slice(0, 5),
      })
      .expect(200);

    assert(result2.body.code === 1000);
    assert(Array.isArray(result2.body.data));
    assert(result2.body.data.length === 5);

    // 测试导出指定状态的码
    const result3 = await createHttpRequest(app)
      .post('/admin/opoc/code/export')
      .send({
        status: 0, // 未使用
      })
      .expect(200);

    assert(result3.body.code === 1000);
    assert(Array.isArray(result3.body.data));
    assert(result3.body.data.every(item => item.status === 0));

    // 测试导出指定产品的码
    const result4 = await createHttpRequest(app)
      .post('/admin/opoc/code/export')
      .send({
        productId: testProductId,
      })
      .expect(200);

    assert(result4.body.code === 1000);
    assert(Array.isArray(result4.body.data));
    assert(result4.body.data.every(item => item.productId === testProductId));

    // 测试导出指定字段
    const fields = ['id', 'code', 'status'];
    const result5 = await createHttpRequest(app)
      .post('/admin/opoc/code/export')
      .send({
        fields,
      })
      .expect(200);

    assert(result5.body.code === 1000);
    assert(Array.isArray(result5.body.data));
    assert(result5.body.data.length > 0);
    assert(Object.keys(result5.body.data[0]).every(key => fields.includes(key)));
    assert(Object.keys(result5.body.data[0]).length === fields.length);
  });

  // 测试生成产品码API
  it('should generate codes', async () => {
    const result = await createHttpRequest(app)
      .post('/admin/opoc/code/generate')
      .send({
        type: 'mixed',
        length: 8,
        quantity: 5,
      })
      .expect(200);

    assert(result.body.code === 1000);
    
    // 清理生成的测试码
    const generatedCodes = await productCodeService.opocProductCodeEntity.find({
      where: {
        code: result.body.data.map(item => item.code),
      },
    });
    
    if (generatedCodes.length > 0) {
      await productCodeService.opocProductCodeEntity.delete(generatedCodes.map(code => code.id));
    }
  });
});
