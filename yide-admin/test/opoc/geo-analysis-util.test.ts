import * as assert from 'assert';
import { GeoAnalysisUtil, GeoPoint } from '../../src/modules/opoc/util/geo-analysis';

describe('地理位置分析工具类测试', () => {
  // 测试角度转弧度
  it('应该能够正确将角度转换为弧度', () => {
    assert.equal(GeoAnalysisUtil.degreeToRadian(0), 0);
    assert.equal(GeoAnalysisUtil.degreeToRadian(180), Math.PI);
    assert.equal(GeoAnalysisUtil.degreeToRadian(360), 2 * Math.PI);
  });

  // 测试距离计算
  it('应该能够正确计算两点之间的距离', () => {
    // 北京天安门坐标
    const point1: GeoPoint = {
      longitude: 116.397452,
      latitude: 39.909187
    };
    
    // 上海东方明珠坐标
    const point2: GeoPoint = {
      longitude: 121.4952,
      latitude: 31.2427
    };
    
    // 两点之间的距离约为1067公里
    const distance = GeoAnalysisUtil.calculateDistance(point1, point2);
    assert(distance > 1060000 && distance < 1070000);
    
    // 测试无效坐标
    assert.equal(GeoAnalysisUtil.calculateDistance(null, point2), -1);
    assert.equal(GeoAnalysisUtil.calculateDistance(point1, null), -1);
    assert.equal(GeoAnalysisUtil.calculateDistance({} as GeoPoint, point2), -1);
  });

  // 测试异常检测
  it('应该能够检测地理位置异常', () => {
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
    
    // 北京天安门坐标
    const point1 = {
      longitude: 116.397452,
      latitude: 39.909187,
      time: fiveMinutesAgo
    };
    
    // 上海东方明珠坐标
    const point2 = {
      longitude: 121.4952,
      latitude: 31.2427,
      time: now
    };
    
    // 检测异常
    const anomalies = GeoAnalysisUtil.detectAnomalies([point1, point2]);
    
    // 应该检测到异常
    assert.equal(anomalies.length, 1);
    assert(anomalies[0].distance > 1060000 && anomalies[0].distance < 1070000);
    assert.equal(anomalies[0].timeDiff, 5 * 60 * 1000);
    assert(anomalies[0].speed > 3500); // 速度应该超过3500米/秒
    
    // 测试正常情况
    const nearbyPoint = {
      longitude: 116.407452, // 距离天安门约1公里
      latitude: 39.919187,
      time: now
    };
    
    const normalAnomalies = GeoAnalysisUtil.detectAnomalies([point1, nearbyPoint]);
    assert.equal(normalAnomalies.length, 0); // 不应该检测到异常
  });

  // 测试热力图数据生成
  it('应该能够生成热力图数据', () => {
    const points = [
      { longitude: 116.397452, latitude: 39.909187 },
      { longitude: 116.397452, latitude: 39.909187 }, // 重复点
      { longitude: 121.4952, latitude: 31.2427 },
      { longitude: 116.407452, latitude: 39.919187 }
    ];
    
    const heatMapData = GeoAnalysisUtil.generateHeatMapData(points);
    
    // 应该有3个不同的点
    assert.equal(heatMapData.length, 3);
    
    // 第一个点应该有计数2
    const firstPoint = heatMapData.find(p => p.lng === 116.397452 && p.lat === 39.909187);
    assert.equal(firstPoint.count, 2);
    
    // 其他点应该有计数1
    const secondPoint = heatMapData.find(p => p.lng === 121.4952 && p.lat === 31.2427);
    assert.equal(secondPoint.count, 1);
  });

  // 测试地区提取
  it('应该能够从地址中提取省市信息', () => {
    assert.equal(GeoAnalysisUtil.extractRegion('北京市海淀区中关村南大街5号'), '北京海淀区');
    assert.equal(GeoAnalysisUtil.extractRegion('上海市浦东新区陆家嘴环路1000号'), '上海浦东新区');
    assert.equal(GeoAnalysisUtil.extractRegion('广东省深圳市南山区科技园'), '广东深圳市');
    assert.equal(GeoAnalysisUtil.extractRegion('浙江省杭州市'), '浙江杭州市');
    assert.equal(GeoAnalysisUtil.extractRegion('北京'), '北京');
    assert.equal(GeoAnalysisUtil.extractRegion(''), '未知');
    assert.equal(GeoAnalysisUtil.extractRegion(null), '未知');
  });

  // 测试按地区分组统计
  it('应该能够按地区分组统计', () => {
    const locations = [
      { location: '北京市海淀区中关村南大街5号', count: 5 },
      { location: '北京市朝阳区建国门外大街1号', count: 3 },
      { location: '上海市浦东新区陆家嘴环路1000号', count: 2 },
      { location: '上海市黄浦区人民大道100号', count: 4 },
      { location: '广东省深圳市南山区科技园', count: 1 }
    ];
    
    const result = GeoAnalysisUtil.groupByRegion(locations);
    
    // 应该有3个不同的地区
    assert.equal(result.length, 3);
    
    // 北京地区应该有8次
    const beijing = result.find(r => r.name.includes('北京'));
    assert.equal(beijing.value, 8);
    
    // 上海地区应该有6次
    const shanghai = result.find(r => r.name.includes('上海'));
    assert.equal(shanghai.value, 6);
    
    // 广东地区应该有1次
    const guangdong = result.find(r => r.name.includes('广东'));
    assert.equal(guangdong.value, 1);
  });
});
