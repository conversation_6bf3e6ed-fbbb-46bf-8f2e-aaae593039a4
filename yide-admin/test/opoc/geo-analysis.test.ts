import { createApp, close, createHttpRequest } from '@midwayjs/mock';
import { Framework } from '@midwayjs/web';
import { Application } from '@midwayjs/koa';
import * as assert from 'assert';
import { OpocVerificationLogEntity } from '../../src/modules/opoc/entity/verification-log';
import { Repository } from 'typeorm';

describe('地理位置分析测试', () => {
  let app: Application;
  let verificationLogRepo: Repository<OpocVerificationLogEntity>;

  beforeAll(async () => {
    // 创建应用
    app = await createApp<Framework>();
    // 获取实体仓库
    verificationLogRepo = app.getApplicationContext().getRepository(OpocVerificationLogEntity);
  });

  afterAll(async () => {
    // 关闭应用
    await close(app);
  });

  // 测试数据准备
  beforeEach(async () => {
    // 清空测试数据
    await verificationLogRepo.clear();

    // 插入测试数据
    const testData = [
      {
        codeId: 1,
        verifyTime: new Date('2023-01-01'),
        verifyResult: 0,
        verifyLocation: '北京市海淀区',
        longitude: 116.3252,
        latitude: 39.9884,
        ip: '***********',
        deviceInfo: 'iPhone',
      },
      {
        codeId: 1,
        verifyTime: new Date('2023-01-02'),
        verifyResult: 0,
        verifyLocation: '上海市浦东新区',
        longitude: 121.5447,
        latitude: 31.2223,
        ip: '***********',
        deviceInfo: 'Android',
      },
      {
        codeId: 2,
        verifyTime: new Date('2023-01-03'),
        verifyResult: 0,
        verifyLocation: '广州市天河区',
        longitude: 113.3612,
        latitude: 23.1292,
        ip: '***********',
        deviceInfo: 'iPhone',
      },
      {
        codeId: 2,
        verifyTime: new Date('2023-01-04'),
        verifyResult: 1,
        verifyLocation: '深圳市南山区',
        longitude: 113.9304,
        latitude: 22.5333,
        ip: '***********',
        deviceInfo: 'Android',
      },
    ];

    for (const data of testData) {
      await verificationLogRepo.save(data);
    }
  });

  // 测试按地理位置分组统计
  it('应该能够按地理位置分组统计验证次数', async () => {
    // 发送请求
    const result = await createHttpRequest(app)
      .get('/admin/opoc/verification/geoStat')
      .set('Authorization', 'Bearer test-token');

    // 验证结果
    assert.equal(result.status, 200);
    assert.equal(result.body.code, 1000);
    
    const data = result.body.data;
    assert(Array.isArray(data));
    assert(data.length > 0);
    
    // 验证数据结构
    const item = data[0];
    assert(item.location);
    assert(typeof item.count === 'number');
    assert(typeof item.longitude === 'number');
    assert(typeof item.latitude === 'number');
  });

  // 测试地理位置异常检测
  it('应该能够检测地理位置异常', async () => {
    // 发送请求
    const result = await createHttpRequest(app)
      .get('/admin/opoc/verification/geoAnomaly')
      .query({ codeId: 1 })
      .set('Authorization', 'Bearer test-token');

    // 验证结果
    assert.equal(result.status, 200);
    assert.equal(result.body.code, 1000);
    
    const data = result.body.data;
    assert(data.hasAnomaly !== undefined);
    
    if (data.hasAnomaly) {
      assert(Array.isArray(data.anomalies));
      assert(data.anomalies.length > 0);
      
      // 验证异常数据结构
      const anomaly = data.anomalies[0];
      assert(anomaly.verifyTime);
      assert(anomaly.verifyLocation);
      assert(typeof anomaly.distance === 'number');
      assert(typeof anomaly.timeDiff === 'number');
    }
  });

  // 测试地理位置热力图数据
  it('应该能够获取地理位置热力图数据', async () => {
    // 发送请求
    const result = await createHttpRequest(app)
      .get('/admin/opoc/verification/heatmap')
      .query({ startTime: '2023-01-01', endTime: '2023-01-31' })
      .set('Authorization', 'Bearer test-token');

    // 验证结果
    assert.equal(result.status, 200);
    assert.equal(result.body.code, 1000);
    
    const data = result.body.data;
    assert(Array.isArray(data));
    assert(data.length > 0);
    
    // 验证热力图数据结构
    const point = data[0];
    assert(typeof point.lng === 'number');
    assert(typeof point.lat === 'number');
    assert(typeof point.count === 'number');
  });

  // 测试地理位置轨迹分析
  it('应该能够获取地理位置轨迹数据', async () => {
    // 发送请求
    const result = await createHttpRequest(app)
      .get('/admin/opoc/verification/trajectory')
      .query({ codeId: 1 })
      .set('Authorization', 'Bearer test-token');

    // 验证结果
    assert.equal(result.status, 200);
    assert.equal(result.body.code, 1000);
    
    const data = result.body.data;
    assert(Array.isArray(data));
    assert(data.length > 0);
    
    // 验证轨迹数据结构
    const point = data[0];
    assert(point.verifyTime);
    assert(point.verifyLocation);
    assert(typeof point.longitude === 'number');
    assert(typeof point.latitude === 'number');
  });

  // 测试地理位置分布统计
  it('应该能够获取地理位置分布统计数据', async () => {
    // 发送请求
    const result = await createHttpRequest(app)
      .get('/admin/opoc/verification/geoDistribution')
      .set('Authorization', 'Bearer test-token');

    // 验证结果
    assert.equal(result.status, 200);
    assert.equal(result.body.code, 1000);
    
    const data = result.body.data;
    assert(Array.isArray(data));
    assert(data.length > 0);
    
    // 验证分布统计数据结构
    const region = data[0];
    assert(region.name);
    assert(typeof region.value === 'number');
  });
});
