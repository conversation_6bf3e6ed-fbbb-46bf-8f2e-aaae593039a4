import { createApp, close, createHttpRequest } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';
import * as assert from 'assert';
import * as fs from 'fs';
import * as path from 'path';
import * as XLSX from 'xlsx';
import { OpocProductInfoService } from '../../src/modules/opoc/service/product-info';

describe('测试产品信息批量导入', () => {
  let app: Framework.Application;
  let productInfoService: OpocProductInfoService;
  let testProductIds: number[] = [];

  beforeAll(async () => {
    // 创建应用
    app = await createApp<Framework>();
    
    // 获取服务
    productInfoService = await app.getApplicationContext().getAsync('opocProductInfoService');
  });

  afterAll(async () => {
    // 清理测试数据
    if (testProductIds.length > 0) {
      await productInfoService.delete({ ids: testProductIds });
    }
    
    // 关闭应用
    await close(app);
  });

  // 创建测试Excel文件
  function createTestExcelFile(data: any[]): Buffer {
    // 创建工作簿
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(data);
    
    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '产品信息');
    
    // 生成Excel文件
    return XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });
  }

  // 测试批量导入API
  it('应该能够批量导入产品信息', async () => {
    // 创建测试数据
    const testData = [
      {
        productCode: 'TEST-IMPORT-001',
        productName: '测试产品1',
        skuCode: 'TEST-SKU-001',
        skuName: '测试SKU1',
        batchNo: 'B001',
        produceDate: '2023-01-01',
        expireDate: '2024-01-01',
        status: 1,
        description: '测试描述1'
      },
      {
        productCode: 'TEST-IMPORT-002',
        productName: '测试产品2',
        skuCode: 'TEST-SKU-002',
        skuName: '测试SKU2',
        batchNo: 'B002',
        produceDate: '2023-02-01',
        expireDate: '2024-02-01',
        status: 1,
        description: '测试描述2'
      }
    ];
    
    // 创建Excel文件
    const excelBuffer = createTestExcelFile(testData);
    
    // 调用批量导入方法
    const result = await productInfoService.batchImport(excelBuffer);
    
    // 断言结果
    assert(result.success);
    assert.equal(result.data.length, 2);
    
    // 保存测试产品ID，用于清理
    testProductIds = result.data.map(item => item.id);
    
    // 验证导入的数据
    for (const testItem of testData) {
      const product = await productInfoService.findOne({
        where: { productCode: testItem.productCode }
      });
      
      assert(product);
      assert.equal(product.productName, testItem.productName);
      assert.equal(product.skuCode, testItem.skuCode);
    }
  });
  
  // 测试重复产品编码导入
  it('应该检测到重复的产品编码', async () => {
    // 确保有测试数据
    if (testProductIds.length === 0) {
      const testProduct = await productInfoService.add({
        productCode: 'TEST-DUPLICATE',
        productName: '测试重复产品',
        skuCode: 'TEST-SKU-DUP',
        skuName: '测试SKU',
        status: 1
      });
      
      testProductIds.push(testProduct.id);
    }
    
    // 创建包含重复编码的测试数据
    const testData = [
      {
        productCode: 'TEST-DUPLICATE', // 重复的产品编码
        productName: '测试产品重复',
        skuCode: 'TEST-SKU-NEW',
        skuName: '测试SKU新',
        status: 1
      }
    ];
    
    // 创建Excel文件
    const excelBuffer = createTestExcelFile(testData);
    
    // 调用批量导入方法
    const result = await productInfoService.batchImport(excelBuffer);
    
    // 断言结果
    assert(!result.success);
    assert(result.message.includes('重复'));
    assert(result.duplicates && result.duplicates.includes('TEST-DUPLICATE'));
  });
  
  // 测试数据验证
  it('应该验证导入数据的有效性', async () => {
    // 创建无效的测试数据
    const testData = [
      {
        // 缺少必填字段 productCode
        productName: '测试产品验证',
        // 缺少必填字段 skuCode
        skuName: '测试SKU验证',
        status: 1
      }
    ];
    
    // 创建Excel文件
    const excelBuffer = createTestExcelFile(testData);
    
    // 调用批量导入方法
    const result = await productInfoService.batchImport(excelBuffer);
    
    // 断言结果
    assert(!result.success);
    assert(result.message.includes('验证失败'));
    assert(result.errors && result.errors.length > 0);
    
    // 验证错误信息
    const errors = result.errors;
    assert(errors.some(err => err.field === 'productCode' && err.message.includes('不能为空')));
    assert(errors.some(err => err.field === 'skuCode' && err.message.includes('不能为空')));
  });
  
  // 测试HTTP API
  it('应该通过HTTP API导入产品信息', async () => {
    // 创建测试数据
    const testData = [
      {
        productCode: 'TEST-API-001',
        productName: '测试API产品1',
        skuCode: 'TEST-API-SKU-001',
        skuName: '测试API SKU1',
        status: 1
      }
    ];
    
    // 创建Excel文件
    const excelBuffer = createTestExcelFile(testData);
    
    // 转换为Base64
    const base64Data = excelBuffer.toString('base64');
    
    // 调用API
    const result = await createHttpRequest(app)
      .post('/admin/opoc/product/import')
      .send({
        fileData: `data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,${base64Data}`
      })
      .expect(200);
    
    // 断言结果
    assert.equal(result.body.code, 1000);
    assert(result.body.data.success);
    
    // 保存测试产品ID，用于清理
    if (result.body.data.data) {
      testProductIds = [...testProductIds, ...result.body.data.data.map(item => item.id)];
    }
  });
});
