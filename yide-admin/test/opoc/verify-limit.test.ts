import { createApp, close } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';
import * as assert from 'assert';
import { OpocProductCodeEntity } from '../../src/modules/opoc/entity/product-code';
import { OpocProductInfoEntity } from '../../src/modules/opoc/entity/product-info';

describe('测试验证次数限制', () => {
  let app: any;
  let productCodeService: any;
  let productInfoService: any;
  let testProductCode: any;
  let testProduct: any;

  beforeAll(async () => {
    // 创建应用
    app = await createApp<Framework>();
    
    // 获取服务
    productCodeService = await app.getApplicationContext().getAsync('opocProductCodeService');
    productInfoService = await app.getApplicationContext().getAsync('opocProductInfoService');
    
    // 创建测试产品
    testProduct = await productInfoService.add({
      productCode: 'TEST-PRODUCT',
      productName: '测试产品',
      skuCode: 'TEST-SKU',
      skuName: '测试SKU',
      status: 1
    });
    
    // 创建测试产品码
    testProductCode = await productCodeService.add({
      code: 'TEST-CODE-' + Date.now(),
      status: 2, // 已激活
      productId: testProduct.id,
      verifyCount: 0,
      verifyLimit: 3, // 设置验证次数限制为3次
      activateTime: new Date()
    });
  });

  afterAll(async () => {
    // 清理测试数据
    if (testProductCode && testProductCode.id) {
      await productCodeService.delete({ ids: [testProductCode.id] });
    }
    
    if (testProduct && testProduct.id) {
      await productInfoService.delete({ ids: [testProduct.id] });
    }
    
    // 关闭应用
    await close(app);
  });

  // 测试设置验证次数限制
  it('应该能够设置验证次数限制', async () => {
    // 设置验证次数限制
    const params = {
      id: testProductCode.id,
      verifyLimit: 5
    };
    
    await productCodeService.setVerifyLimit(params);
    
    // 获取更新后的产品码
    const updatedCode = await productCodeService.info(testProductCode.id);
    
    // 断言结果
    assert(updatedCode);
    assert.equal(updatedCode.verifyLimit, 5);
  });
  
  // 测试批量设置验证次数限制
  it('应该能够批量设置验证次数限制', async () => {
    // 创建多个测试产品码
    const testCodes = [];
    for (let i = 0; i < 3; i++) {
      const code = await productCodeService.add({
        code: `TEST-BATCH-${i}-${Date.now()}`,
        status: 2, // 已激活
        productId: testProduct.id,
        verifyCount: 0,
        verifyLimit: 0, // 初始无限制
        activateTime: new Date()
      });
      testCodes.push(code);
    }
    
    // 批量设置验证次数限制
    const codeIds = testCodes.map(code => code.id);
    await productCodeService.batchSetVerifyLimit(codeIds, 10);
    
    // 获取更新后的产品码
    for (const codeId of codeIds) {
      const updatedCode = await productCodeService.info(codeId);
      assert(updatedCode);
      assert.equal(updatedCode.verifyLimit, 10);
    }
    
    // 清理测试数据
    await productCodeService.delete({ ids: codeIds });
  });
  
  // 测试验证次数限制功能
  it('应该在达到验证次数限制时拒绝验证', async () => {
    // 设置验证次数限制
    await productCodeService.setVerifyLimit({
      id: testProductCode.id,
      verifyLimit: 2
    });
    
    // 第一次验证
    const result1 = await productCodeService.verifyCode({
      code: testProductCode.code
    });
    
    // 断言结果
    assert(result1);
    assert.equal(result1.verified, true);
    
    // 第二次验证
    const result2 = await productCodeService.verifyCode({
      code: testProductCode.code
    });
    
    // 断言结果
    assert(result2);
    assert.equal(result2.verified, true);
    
    // 第三次验证（应该失败）
    const result3 = await productCodeService.verifyCode({
      code: testProductCode.code
    });
    
    // 断言结果
    assert(result3);
    assert.equal(result3.verified, false);
    assert.equal(result3.message, '产品码验证次数已达上限');
    
    // 获取更新后的产品码
    const updatedCode = await productCodeService.info(testProductCode.id);
    
    // 断言结果
    assert(updatedCode);
    assert.equal(updatedCode.verifyCount, 2); // 验证次数应该是2，因为第三次验证失败了
  });
  
  // 测试无限制的情况
  it('应该在验证次数限制为0时允许无限次验证', async () => {
    // 设置验证次数限制为0（无限制）
    await productCodeService.setVerifyLimit({
      id: testProductCode.id,
      verifyLimit: 0
    });
    
    // 重置验证次数
    await productCodeService.update(testProductCode.id, {
      verifyCount: 0
    });
    
    // 多次验证
    for (let i = 0; i < 5; i++) {
      const result = await productCodeService.verifyCode({
        code: testProductCode.code
      });
      
      // 断言结果
      assert(result);
      assert.equal(result.verified, true);
    }
    
    // 获取更新后的产品码
    const updatedCode = await productCodeService.info(testProductCode.id);
    
    // 断言结果
    assert(updatedCode);
    assert.equal(updatedCode.verifyCount, 5); // 验证次数应该是5
  });
});
